DJANGO_SETTINGS_MODULE=axepto.settings.development
AWS_ACCESS_KEY_ID='access key'
AWS_SECRET_ACCESS_KEY='sectret access key'
AWS_LOG_GROUP=''
SENTRY_DSN=''
SERVER_NAME='dev'
DB_HOST=postgres
DB_PORT=5432
DB_TABLE='axepto'
DB_USER='axepto'
DB_PASSWORD=''
CERT_DIR='/etc/letsencrypt/live'
CERT_CHECKER_SLACK_URL=''
TRUSTED_CERTIFICATES='/etc/ssl/certs/ca-certificates.crt'
INTIME_NOTIFICATIONS_HOST='http://127.0.0.1:9000/test/'
TELEKOM_NOTIFICATIONS_HOST='http://127.0.0.1:9000/packagestatus/'
TELEKOM_BASIC_AUTH_NAME=''
TELEKOM_BASIC_AUTH_PASSWORD=''
UNION_NOTIFICATIONS_HOST='http://127.0.0.1:9000/union/'
UNION_BASIC_AUTH_NAME=''
UNION_BASIC_AUTH_PASSWORD=''
DOCUMENT_SIGN_CERTIFICATE_MZONE=' '
DOCUMENT_SIGN_CERTIFICATE_O2=' '
DOCUMENT_SIGN_CERTIFICATE_ORANGE=' '
DOCUMENT_SIGN_CERTIFICATE_TELEKOM=' '
DOCUMENT_SIGN_CERTIFICATE_UNION=' '
