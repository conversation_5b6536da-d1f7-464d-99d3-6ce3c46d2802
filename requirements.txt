#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements.in
#
asgiref==3.4.1
    # via django
attrs==21.2.0
    # via jsonschema
backcall==0.2.0
    # via ipython
boto3==1.18.55
    # via django-ses
botocore==1.21.55
    # via
    #   boto3
    #   s3transfer
certifi==2025.8.3
    # via
    #   -r requirements.in
    #   requests
    #   sentry-sdk
cffi==1.15.0
    # via cryptography
charset-normalizer==2.0.6
    # via requests
click==8.0.1
    # via pip-tools
convertdate==2.3.2
    # via workalendar
cryptography==36.0.1
    # via pyopenssl
decorator==5.1.0
    # via
    #   -r requirements.in
    #   ipython
django==3.2.8
    # via
    #   -r requirements.in
    #   django-cors-headers
    #   django-cron
    #   django-debug-toolbar
    #   django-extensions
    #   django-guid
    #   django-picklefield
    #   django-qr-code
    #   django-ses
    #   django-setty
    #   django-storages
    #   jsonfield
django-cors-headers==3.10.0
    # via -r requirements.in
django-crispy-forms==1.13.0
    # via -r requirements.in
django-cron==0.6.0
    # via -r requirements.in
django-debug-toolbar==3.2.2
    # via -r requirements.in
django-dotenv==1.4.2
    # via -r requirements.in
django-extensions==3.1.3
    # via -r requirements.in
django-guid==3.2.0
    # via -r requirements.in
django-picklefield==3.0.1
    # via
    #   -r requirements.in
    #   django-setty
django-qr-code==2.2.0
    # via -r requirements.in
django-ses==4.1.0
    # via -r requirements.in
django-setty==3.2.1
    # via -r requirements.in
django-storages==1.11.1
    # via -r requirements.in
humanfriendly==10.0
    # via -r requirements.in
idna==3.2
    # via requests
ipython==7.28.0
    # via -r requirements.in
jedi==0.18.0
    # via ipython
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
jsonfield==3.1.0
    # via -r requirements.in
jsonschema==4.0.1
    # via -r requirements.in
lunardate==0.2.0
    # via workalendar
matplotlib-inline==0.1.3
    # via ipython
parso==0.8.2
    # via jedi
pep517==0.11.0
    # via pip-tools
pexpect==4.8.0
    # via ipython
pickleshare==0.7.5
    # via ipython
pip-tools==6.6.0
    # via -r requirements.in
prompt-toolkit==3.0.20
    # via ipython
psycopg2-binary==2.9.9
    # via -r requirements.in
ptyprocess==0.7.0
    # via pexpect
pycparser==2.21
    # via cffi
pygments==2.10.0
    # via ipython
pyluach==1.3.0
    # via workalendar
pymeeus==0.5.11
    # via convertdate
pyopenssl==22.0.0
    # via -r requirements.in
pyrsistent==0.18.0
    # via jsonschema
python-dateutil==2.8.2
    # via
    #   botocore
    #   workalendar
python-memcached==1.59
    # via django-setty
pytz==2021.3
    # via
    #   -r requirements.in
    #   convertdate
    #   django
    #   django-ses
requests==2.26.0
    # via -r requirements.in
s3transfer==0.5.0
    # via boto3
segno==1.3.3
    # via django-qr-code
sentry-sdk==1.9.0
    # via -r requirements.in
six==1.16.0
    # via
    #   python-dateutil
    #   python-memcached
sqlparse==0.4.2
    # via
    #   django
    #   django-debug-toolbar
statsd-exporter==3.2.1
    # via -r requirements.in
tomli==1.2.1
    # via pep517
traitlets==5.1.0
    # via
    #   ipython
    #   matplotlib-inline
urllib3==1.26.7
    # via
    #   botocore
    #   requests
    #   sentry-sdk
uwsgi==2.0.23
    # via -r requirements.in
wcwidth==0.2.5
    # via prompt-toolkit
wheel==0.37.0
    # via pip-tools
workalendar==16.1.0
    # via -r requirements.in

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
