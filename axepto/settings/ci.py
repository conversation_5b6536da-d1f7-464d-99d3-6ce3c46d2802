from .base import *

MODE = DELIVERY_COMPANY.INTIME

TEMPLATES[0]["OPTIONS"]["debug"] = False
HOST_DOMAIN = "https://%s.axepto.com" % MODE

LOGGING["loggers"]["axepto"]["level"] = "DEBUG"

DATABASES["default"]["HOST"] = "localhost"

USE_ZIP_UPLOAD = True

# AWS bucket for development
AWS_STORAGE_BUCKET_NAME = "axepto-test"

AWS_S3_CUSTOM_DOMAIN = "%s.s3.amazonaws.com" % AWS_STORAGE_BUCKET_NAME

MEDIA_URL = "https://%s/%s/" % (AWS_S3_CUSTOM_DOMAIN, MEDIAFILES_LOCATION)

ALLOW_BACKUPS = True

SEND_INTIME_NOTIFICATIONS = True

SEND_TELEKOM_NOTIFICATIONS = True

DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
LOCAL_STORAGE = True
