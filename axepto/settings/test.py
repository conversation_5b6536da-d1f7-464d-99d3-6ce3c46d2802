from .base import *

TEMPLATES[0]["OPTIONS"]["debug"] = False
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")

LOGGING["loggers"]["axepto"]["level"] = "DEBUG"

DATABASES["default"]["NAME"] = os.environ.get("DB_TABLE", "")
DATABASES["default"]["USER"] = os.environ.get("DB_USER", "")
DATABASES["default"]["HOST"] = os.environ.get("DB_HOST", "")
DATABASES["default"]["PASSWORD"] = os.environ.get("DB_PASSWORD", "")

USE_ZIP_UPLOAD = True

# AWS bucket for development
AWS_STORAGE_BUCKET_NAME = os.environ.get("AWS_STORAGE_BUCKET")

AWS_S3_CUSTOM_DOMAIN = "%s.s3.amazonaws.com" % AWS_STORAGE_BUCKET_NAME

MEDIA_URL = "https://%s/%s/" % (AWS_S3_CUSTOM_DOMAIN, MEDIAFILES_LOCATION)

ALLOW_BACKUPS = True

SEND_INTIME_NOTIFICATIONS = True

SEND_TELEKOM_NOTIFICATIONS = True

SEND_EMAILS = True
