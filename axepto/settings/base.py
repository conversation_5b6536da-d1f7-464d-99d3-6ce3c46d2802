"""
Django settings for axepto project.

Generated by 'django-admin startproject' using Django 1.8.

For more information on this file, see
https://docs.djangoproject.com/en/1.8/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.8/ref/settings/
"""

import locale
import os
import sys
from datetime import timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Dict, List

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)

# Use V4 signature algorithm when connecting to S3,
# as it is required for European servers
os.environ["S3_USE_SIGV4"] = "True"

BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

ASSETS_DIR = BASE_DIR + "/assets"
ASSETS_DIR_V2 = ASSETS_DIR
SURVEY_ASSETS_DIR = ASSETS_DIR + "/surveys"

SERVER_NAME = os.environ.get("SERVER_NAME", "dev")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.8/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "$3s6_eye6qmwr6^_at@8jf0#3m4y!^3mp%uj*jd6-1qkk$q*t="

DEBUG = False

ALLOWED_HOSTS = ["*"]

# Application definition

try:
    MIGRATION_COMMAND = sys.argv[1] in ("makemigrations", "migrate")
except IndexError:
    MIGRATION_COMMAND = False

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
    "django_extensions",
    "django_guid",
    "setty",
    "axepto.rest",
    "axepto.archive",
    "axepto",
    "storages",
    "qr_code",
    "django_cron",
    "debug_toolbar",
    "corsheaders",
    "crispy_forms",
    "axepto.audit.apps.AuditConfig",
]

if not MIGRATION_COMMAND:
    INSTALLED_APPS.append("axepto.web.apps.WebConfig")


class DELIVERY_COMPANY(Enum):
    INTIME = "intime"
    PACKETA = "packeta"
    DEV = "dev"


SHELL_PLUS = "ipython"

CRISPY_TEMPLATE_PACK = "bootstrap3"

SITE_ID = 1

MIDDLEWARE = (
    "django_guid.middleware.guid_middleware",
    "axepto.middleware.StatsDMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "axepto.middleware.BasicAuthMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "axepto.middleware.LoggingMiddleware",
)

ROOT_URLCONF = "axepto.urls"

"""
TEMPLATE_DIRS = (
    BASE_DIR + '/web/templates/',
    BASE_DIR + '/web/templates/helpers/',
    BASE_DIR + '/test/templates/',
)
"""
TEMPLATES: List[Dict[str, Any]] = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            BASE_DIR + "/web/templates/",
            BASE_DIR + "/web/templates/helpers/",
            BASE_DIR + "/test/templates/",
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "debug": False,
            "context_processors": [
                "setty.context_processors.setty_settings",
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "axepto.wsgi.application"

# Database
# https://docs.djangoproject.com/en/1.8/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": "axepto",
        "USER": "axepto",
        "PASSWORD": "axeptopassword",
        "HOST": "postgres",
    }
}

DATABASE_ROUTERS = ["axepto.routers.DefaultRouter"]


def get_console_handler(level):
    return {
        "filters": ["correlation_id"],
        "level": level,
        "class": "logging.StreamHandler",
        "formatter": "verbose",
    }


LOGGING: Dict[str, Any] = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "correlation_id": {
            "()": "django_guid.log_filters.CorrelationId",
        }
    },
    "formatters": {
        "verbose": {
            "format": (
                "[{}][%(levelname)s][%(asctime)s][%(name)s][%(correlation_id)s]"
                + " %(message)s"
            ).format(SERVER_NAME)
        },
        "simple": {"format": "[%(levelname)s] %(message)s"},
    },
    "handlers": {
        "null": {
            "level": "DEBUG",
            "class": "logging.NullHandler",
        },
        "console_debug": get_console_handler("DEBUG"),
        "console_info": get_console_handler("INFO"),
        "console_warning": get_console_handler("WARNING"),
        "console_error": get_console_handler("ERROR"),
        "console_critical": get_console_handler("CRITICAL"),
    },
    "loggers": {
        "": {
            "handlers": [
                "console_debug",
                "console_info",
                "console_warning",
                "console_error",
                "console_critical",
            ],
            "level": "DEBUG",
            "propagate": True,
        },
        "botocore": {
            "handlers": ["null"],
            "propagate": False,
            "level": "DEBUG",
        },
        "django_guid": {
            "handlers": ["null"],
            "propagate": False,
            "level": "DEBUG",
        },
        "django.db.backends": {
            "handlers": ["null"],
            "propagate": False,
            "level": "DEBUG",
        },
        "axepto": {
            "handlers": [
                "console_debug",
                "console_info",
                "console_warning",
                "console_error",
                "console_critical",
            ],
            "level": "INFO",
            "propagate": False,
        },
        "django.template": {
            "handlers": ["null"],
            "level": "INFO",
            "propagate": True,
        },
        "setty": {"level": "INFO"},
    },
}

# Internationalization
# https://docs.djangoproject.com/en/1.8/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Session settings

SESSION_COOKIE_AGE_OPERATOR = int(timedelta(minutes=15).total_seconds())
SESSION_COOKIE_AGE_OPERATOR_WEB = int(timedelta(hours=8).total_seconds())
SESSION_COOKIE_AGE_MANAGER = int(timedelta(hours=24).total_seconds())

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.8/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

LOGIN_URL = "web_login"

SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Disable max fields check
DATA_UPLOAD_MAX_NUMBER_FIELDS = None
# Set max request memory size to 10 MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10485760

# EC2 backup
MIN_BACKUP_INTERVAL = timedelta(days=7)
ALLOW_BACKUPS = False

# Notifications for courier company
SEND_INTIME_NOTIFICATIONS = False
INTIME_NOTIFICATIONS_HOST = os.environ.get("INTIME_NOTIFICATIONS_HOST")
SEND_TELEKOM_NOTIFICATIONS = True
TELEKOM_NOTIFICATIONS_HOST = os.environ.get("TELEKOM_NOTIFICATIONS_HOST")
TELEKOM_AUTH_TOKEN = os.environ.get("TELEKOM_AUTH_TOKEN")
TELEKOM_BASIC_AUTH_NAME = os.environ.get("TELEKOM_BASIC_AUTH_NAME")
TELEKOM_BASIC_AUTH_PASSWORD = os.environ.get("TELEKOM_BASIC_AUTH_PASSWORD")
TELEKOM_ROOT_CA_PATH = os.environ.get("TELEKOM_ROOT_CA_PATH")
UNION_NOTIFICATIONS_HOST = os.environ.get("UNION_NOTIFICATIONS_HOST")
UNION_BASIC_AUTH_NAME = os.environ.get("UNION_BASIC_AUTH_NAME")
UNION_BASIC_AUTH_PASSWORD = os.environ.get("UNION_BASIC_AUTH_PASSWORD")
COURIER_ID = os.environ.get("COURIER_ID")

# Deleting old documents
OLD_QUARANTINE_DOCUMENTS_REMOVAL_LIMIT = 30  # in days

LOCAL_STORAGE = False

AWS_REGION = os.environ.get("AWS_REGION")

AWS_SES_REGION_NAME = os.getenv("AWS_REGION")
AWS_SES_REGION_ENDPOINT = os.getenv("AWS_SES_API_ENDPOINT")

MEDIAFILES_LOCATION = "media"
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

CRON_CLASSES = [
    "axepto.cron.DeleteOldDocumentsJob",
    "axepto.cron.CleanDriverList",
    "axepto.cron.SendCourierNotifications",
    "axepto.cron.SendTelekomNotifications",
    "axepto.cron.CreateAuditExports",
    "axepto.cron.DailySummary",
    "axepto.cron.CheckCertificates",
    "axepto.cron.SendUnionNotifications",
    "axepto.cron.HistoryDeleteFix",
]

CORS_ORIGIN_ALLOW_ALL = True

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_METHODS = ("POST",)

NOTIFICATION_MAIL = os.getenv("WARNING_EMAIL", "")
NOTIFICATIONS_RECIPIENTS = [os.getenv("NOTIFICATION_EMAIL", "")]

INTIME_EMAIL_RECIPIENTS = [os.getenv("NOTIFICATION_EMAIL", "")]

SIGNED_PACKAGES_SIZE = 50000

INTERNAL_IPS = ["127.0.0.1"]


def GET_DELETED_REST(rest):
    result = {}
    to_keep = [
        "zakaznik_mesto",
        "zakaznik_psc",
        "documents_info",
        "scans_info",
        "zakaznik_op_suhlas",
    ]

    for key in to_keep:
        if key in rest:
            result[key] = rest[key]

    return result


SETTY_BACKEND = "CacheBackend"

TRACKING_KEYS = [
    "gps_on",
    "gps_end",
    "dev_width",
    "doc_width",
    "gps_start",
    "wizard_close",
    "wizard_start",
]

AUTHENTICATION_BACKENDS = ["axepto.login_backend.PrefetchModelBackend"]

EMAIL_BACKEND = "django_ses.SESBackend"
USE_SES_V2 = True

CERT_CHECKER_SLACK_URL = os.getenv("CERT_CHECKER_SLACK_URL", "")

CERT_DIR = os.getenv("CERT_DIR", "/etc/letsencrypt/live")
TRUSTED_CERTIFICATES = os.getenv(
    "TRUSTED_CERTIFICATES", "/etc/ssl/certs/ca-certificates.crt"
)

MODE: DELIVERY_COMPANY = DELIVERY_COMPANY(
    os.environ.get("MODE", DELIVERY_COMPANY.INTIME)
)

TELEKOM_SERVER_KEY_PATH = os.getenv("TELEKOM_SERVER_KEY_PATH", "")
TELEKOM_CERTIFICATE_PATH = os.getenv("TELEKOM_CERTIFICATE_PATH", "")

CRON_ENDPOINT = os.getenv("API_CRON_PATH", "")
if CRON_ENDPOINT and CRON_ENDPOINT[0] == "/":
    CRON_ENDPOINT = CRON_ENDPOINT[1:]

SENTRY_DSN = os.getenv("SENTRY_DSN")

CRON_TIMEOUT = int(os.getenv("CRON_TIMEOUT", 60))

ARCHIVE_BATCH_SIZE = int(os.getenv("ARCHIVE_BATCH_SIZE", 100))
ARCHIVE_OLDER_THAN_DAYS = int(os.getenv("ARCHIVE_OLDER_THAN_DAYS", 365))

COURIER_DOCUMENTS_URL = os.getenv(
    "COURIER_DOCUMENTS_URL",
    "https://t-t.sps-sro.sk/result.php?cmd=VERKNR_SEARCH"
    "&kundenr={shipper_id}&verknr={client_id}",
)

locale.setlocale(locale.LC_ALL, "sk_SK.UTF-8")
