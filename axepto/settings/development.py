from .base import *

MODE = DELIVERY_COMPANY.DEV

DEBUG = True
TEMPLATES[0]["OPTIONS"]["debug"] = DEBUG
HOST_DOMAIN = "http://127.0.0.1:8000"

NOTIFICATION_MAIL = "<EMAIL>"

LOGGING["loggers"]["axepto"]["level"] = "DEBUG"

ALLOW_TEST_VIEWS = True

ALLOW_BACKUPS = False

VALIDATE_PASSWORDS = False

DELETE_FROM_STORAGE = True

DATABASES["default"]["NAME"] = os.environ.get("DB_TABLE", "")
DATABASES["default"]["USER"] = os.environ.get("DB_USER", "axepto")
DATABASES["default"]["HOST"] = os.environ.get("DB_HOST", "localhost")
DATABASES["default"]["PORT"] = os.environ.get("DB_PORT", "5432")
DATABASES["default"]["PASSWORD"] = os.environ.get("DB_PASSWORD", "axeptopassword")

# Deleting old documents
DELETE_OLD_DOCUMENTS = 20  # in days
DELETE_HARD_OLD_DOCUMENTS = 20  # in days

SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Local storage for development
LOCAL_STORAGE = True

DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"

MEDIA_ROOT = os.path.join(BASE_DIR, "media")

SILKY_META = True
