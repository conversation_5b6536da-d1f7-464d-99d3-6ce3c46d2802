import base64
import binascii
import logging

from django.conf import settings
from django.contrib.auth import authenticate, login
from django.http import HttpRequest
from django.urls import Resolver404, resolve
from statsd import StatsClient

from axepto.fail2ban import can_user_login, is_banned

logger = logging.getLogger("axepto.request")

stats_client = StatsClient()


def format_request(request):
    user = (
        request.user
        if hasattr(request, "user")
        else "Unknown (probably redirected by CommonMiddleware because of APPEND_SLASH)"
    )
    if getattr(settings, "MODE") == "dev":
        return (
            "\nRequest\nMethod: %s\nRoute: %s\nMeta: %s\nLogged user: %s\nCookies: "
            + "%s\nGET data: %s\nPOST data: %s\n"
        ) % (
            request.method,
            request.path,
            request.META,
            user,
            request.COOKIES,
            request.GET,
            request.POST,
        )
    else:
        return (
            "\nRequest\nMethod: %s\nRoute: %s\nLogged user: %s\nCookies: %s\nGET data: "
            + "%s\n"
        ) % (request.method, request.path, user, request.COOKIES, request.GET)


class RequestLoggingMiddleware(object):
    def process_request(self, request):
        logger.info(format_request(request))
        return None


class ResponseLoggingMiddleware(object):
    def process_response(self, request, response):
        if 400 <= response.status_code < 600:
            logger.info(
                "\nResponse: Status code: %s\nContent: %s\n%s\n"
                % (response.status_code, response.content, format_request(request))
            )
        return response


class LoggingMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        logger.info(format_request(request))

        response = self.get_response(request)

        if (400 <= response.status_code < 600) or getattr(settings, "MODE") == "dev":
            logger.info(
                "\nResponse: Status code: %s\nContent: %s\n%s\n"
                % (response.status_code, response.content, format_request(request))
            )

        return response


class BasicAuthMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request: HttpRequest) -> None:
        if (
            "HTTP_AUTHORIZATION" in request.META
            and len(request.META["HTTP_AUTHORIZATION"].split(" ")) == 2
        ):
            auth, credentials = request.META["HTTP_AUTHORIZATION"].split(" ")
            if auth == "Basic":
                try:
                    credentials = base64.b64decode(credentials).decode("utf-8")
                except binascii.Error as e:
                    logger.info("Basic auth: {}".format(e))
                if len(credentials.split(":")) == 2:
                    username, password = credentials.split(":")

                    response = is_banned(request, username)
                    if response:
                        return response

                    user = authenticate(username=username, password=password)
                    if user and can_user_login(request, username, user):
                        logger.info(f"Logging in {user} with basic-auth")
                        login(request, user)

        return self.get_response(request)


class StatsDMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            resolved_path = resolve(request.path).view_name
        except Resolver404:
            resolved_path = "not_found"

        try:
            client = request.META["HTTP_HOST"].split(".")[0]
        except KeyError:
            client = "unknown"

        with stats_client.timer(
            "{}.{}.response_time".format(resolved_path, request.method),
        ) as t:
            response = self.get_response(request)
            t.tags = {"status_code": response.status_code, "client": client}

        stats_client.incr(
            "{}.{}".format(resolved_path, request.method),
            tags={"status_code": response.status_code, "client": client},
        )

        return response
