import json

from django.shortcuts import redirect, render
from django.views.generic import View

from axepto.helpers import auth_required, json_response
from axepto.rest.forms import FileForm, UserForm
from axepto.rest.helpers import load_json
from axepto.rest.models import Document
from axepto.rest.views import ListSignedSinceView


# For GET requests, transforms the response of corresponding route using
# given html_response function
def transform_response(transformation):
    def decorator(func):
        def call(self, request, *args, **kwargs):
            return transformation(func(self, request, *args, **kwargs))

        return call

    return decorator


class TestUploadView(View):
    @auth_required
    def get(self, request):
        return render(
            "upload.html", {"file_form": FileForm(), "route": "document_unsigned_post"}
        )


class TestLoginView(View):
    def get(self, request):
        return render("auth.html", {"form": UserForm(), "route": "login"})


class TestUploadSignedView(View):
    def get(self, request, **kwargs):
        if "id" not in kwargs:
            return json_response({"error": "Missing id"}, 400)
        return render(
            "upload_signed.html",
            {"file_form": FileForm(), "route": "document_signed", "id": kwargs["id"]},
        )


class TestSignedSinceView(View):
    @auth_required
    @transform_response(
        lambda response: render(
            "list.html", {"response": json.loads(response.content.decode("utf-8"))}
        )
    )
    def get(self, request, timestamp):
        return ListSignedSinceView.dispatch(request, timestamp)


class ResetDocumentsView(View):
    @auth_required
    def post(self, request):
        Document.objects.all().delete()
        return redirect("dashboard")


class TestEncodeView(View):
    @auth_required
    def post(self, request):
        json_meta = load_json(request.body)
        # doc = Document.objects.filter(id='e30f62e24c584df68296b0ead62adff4')
        return json_response({"result": json.dumps(json_meta, ensure_ascii=False)}, 200)
