from django.conf import settings
from django.urls import re_path

from . import views as v

ALLOW_TEST_VIEWS = getattr(settings, "ALLOW_TEST_VIEWS", False)

if ALLOW_TEST_VIEWS:
    urlpatterns = [
        re_path(
            r"^reset_documents/$",
            v.ResetDocumentsView.as_view(),
            name="reset_documents",
        ),
        re_path(
            r"^document/unsigned/$", v.TestUploadView.as_view(), name="test_upload_doc"
        ),
        re_path(
            r"^document/signed/since/([0-9]+)/$",
            v.TestSignedSinceView.as_view(),
            name="test_signed_since",
        ),
        re_path(
            r"^document/signed/(?P<id>[0-9a-zA-Z-_]+)/$",
            v.TestUploadSignedView.as_view(),
            name="test_upload_signed",
        ),
        re_path(r"^login/$", v.TestLoginView.as_view(), name="test_login"),
        re_path(r"^encode/$", v.TestEncodeView.as_view(), name="test_encode"),
    ]

else:
    urlpatterns = []
