from datetime import datetime, timedelta

from django.contrib.auth.models import User
from django.test import TestCase

from axepto.constants import SUMMARY_TYPES
from axepto.library.tasks import daily_summary, document_summary, history_summary
from axepto.rest.models import Courier, Customer, Document, Metric


class ApiTest(TestCase):
    # TODO: `o2`, `telekom` must match keys from `schemas.py`,
    # make them constants and import them
    def setUp(self):
        # Note: `o2` customer is already created using migration
        self.customer1 = Customer.objects.filter(id="o2")[0]
        self.customer2 = Customer.objects.create(
            id="telekom", name="telekom", active=True
        )
        self.customer3 = Customer.objects.create(
            id="orange", name="orange", active=True
        )

        self.courier = Courier.objects.create(
            user=User.objects.create_user(username="courier", password="pass"),
            id="courier",
        )

    def datetime_to_timestamp(self, time):
        return datetime.timestamp(time) * 1000

    def test_summary1(self):
        unsigned1 = Document.objects.create(
            id="1",
            client_id="1",
            timestamp=self.datetime_to_timestamp(datetime.now()),
            customer=self.customer1,
        )
        unsigned2 = Document.objects.create(
            id="2",
            client_id="2",
            timestamp=self.datetime_to_timestamp(datetime.now()),
            customer=self.customer1,
        )
        unsigned3 = Document.objects.create(
            id="3",
            client_id="3",
            timestamp=self.datetime_to_timestamp(datetime.now()),
            customer=self.customer1,
        )
        unsigned4 = Document.objects.create(
            id="4",
            client_id="4",
            timestamp=self.datetime_to_timestamp(datetime.now()),
            customer=self.customer3,
        )
        unsigned5 = Document.objects.create(
            id="5",
            client_id="5",
            timestamp=self.datetime_to_timestamp(datetime.now() - timedelta(days=2)),
            customer=self.customer3,
        )
        signed = Document.objects.create(
            id="6",
            client_id="6",
            timestamp=self.datetime_to_timestamp(datetime.now()),
            customer=self.customer1,
            parent=unsigned1,
        )
        self.client.post(
            "/login/", {"username": "courier", "password": "pass"}, follow=True
        )
        self.client.post("/active/add/", {"id": "5"})

        daily_summary([SUMMARY_TYPES.signed, SUMMARY_TYPES.unsigned], document_summary)
        daily_summary([SUMMARY_TYPES.assign], history_summary)

        unsigned_customer1 = Metric.objects.get(
            customer=self.customer1, type=SUMMARY_TYPES.unsigned.value
        )
        signed_customer1 = Metric.objects.get(
            customer=self.customer1, type=SUMMARY_TYPES.signed.value
        )
        assign_customer1 = Metric.objects.get(
            customer=self.customer1, type=SUMMARY_TYPES.assign.value
        )
        self.assertEqual(unsigned_customer1.value, 3)
        self.assertEqual(signed_customer1.value, 1)
        self.assertEqual(assign_customer1.value, 0)

        unsigned_customer2 = Metric.objects.get(
            customer=self.customer2, type=SUMMARY_TYPES.unsigned.value
        )
        signed_customer2 = Metric.objects.get(
            customer=self.customer2, type=SUMMARY_TYPES.signed.value
        )
        assign_customer2 = Metric.objects.get(
            customer=self.customer2, type=SUMMARY_TYPES.assign.value
        )
        self.assertEqual(unsigned_customer2.value, 0)
        self.assertEqual(signed_customer2.value, 0)
        self.assertEqual(assign_customer2.value, 0)

        unsigned_customer3 = Metric.objects.get(
            customer=self.customer3, type=SUMMARY_TYPES.unsigned.value
        )
        signed_customer3 = Metric.objects.get(
            customer=self.customer3, type=SUMMARY_TYPES.signed.value
        )
        assign_customer3 = Metric.objects.get(
            customer=self.customer3, type=SUMMARY_TYPES.assign.value
        )
        self.assertEqual(unsigned_customer3.value, 1)
        self.assertEqual(signed_customer3.value, 0)
        self.assertEqual(assign_customer3.value, 1)
