import re

from django.conf import settings

VALIDATE_PASSWORDS = getattr(settings, "VALIDATE_PASSWORDS", True)


def check_diversity(password):
    pattern = re.compile(r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$")

    if pattern.match(password) is None and VALIDATE_PASSWORDS:
        return (
            "Your password must contain at least 8 characters, 1 uppercase alphabet, "
            + "1 lowercase alphabet and 1 number"
        )

    return None
