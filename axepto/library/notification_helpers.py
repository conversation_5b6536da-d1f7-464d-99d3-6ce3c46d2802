from datetime import datetime, timedelta

from django.conf import settings

from axepto.rest.models import Document, Notification

from .alerts import send_email

NOTIFICATIONS_RECIPIENTS = getattr(settings, "NOTIFICATIONS_RECIPIENTS", [])
INTIME_EMAIL_RECIPIENTS = getattr(
    settings, "INTIME_EMAIL_RECIPIENTS", "<EMAIL>"
)


# notifications can be array oq queryset
def send_notifications(notifications, send_to_courier, title="Axepto Notification"):
    txts = []

    exit = True
    for n in notifications:
        txts.append("<p>%s</p>" % n.text)
        exit = False

    if exit:
        return

    r: list[str] | str = []
    if send_to_courier:
        r = INTIME_EMAIL_RECIPIENTS
    else:
        r = NOTIFICATIONS_RECIPIENTS

    return send_email(text="".join(txts), recipients=r, subject=title)


# notification is ORM instance
def add_notification(notification, send_now):
    notification.save()

    if send_now:
        send_notifications([notification], False)
        if notification.send_to_courier:
            send_notifications([notification], True)
        notification.sent = True
        notification.save(update_fields=["sent"])
