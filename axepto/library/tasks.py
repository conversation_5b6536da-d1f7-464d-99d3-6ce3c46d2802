import logging
import os
import re
import subprocess
import time
from datetime import date, datetime, timedelta
from typing import Callable, Iterable

import requests
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.storage import default_storage
from django.db import IntegrityError, transaction
from OpenSSL import crypto
from workalendar.europe import Slovakia

from axepto.constants import (
    ASSIGN,
    NOT_OBTAINED,
    REPORT_DAILY,
    SIGNED_REMOTELY,
    SUMMARY_TYPES,
)
from axepto.helpers import date_to_timestamp, get_ba_tz, send_slack_notification
from axepto.library.notification_helpers import send_notifications
from axepto.rest.helpers import remove_documents
from axepto.rest.models import (
    Customer,
    Document,
    History,
    Metric,
    Notification,
    Quarantine,
)
from axepto.rest.notifications import (
    notify_assign_ratio,
    notify_not_obtained,
    notify_remotely_signed_not_uploaded,
    notify_uploaded_decrease,
)
from axepto.rest.useful import id_to_filename

UNOBTAINED_NOTIFICATION_LIMIT = getattr(
    settings, "UNOBTAINED_NOTIFICATION_LIMIT", timedelta(minutes=25)
).total_seconds()

logger = logging.getLogger(__name__)

# CRON TASKS


def check_not_obtained():
    now = time.time()
    past = (now - UNOBTAINED_NOTIFICATION_LIMIT) * 1000
    doc = (
        Document.objects.signed()
        .active()
        .filter(timestamp__lt=past)
        .exclude(notifications__kind=NOT_OBTAINED)
    )
    for d in doc:
        notify_not_obtained(d, now * 1000 - d.timestamp)


def handle_email_notifications():
    dt = get_ba_tz(datetime.now())
    notif = Notification.objects.filter(sent=False).order_by("kind", "-value")

    title = ""
    if dt.weekday() == 0:  # monday
        notif = notif  # daily + weekly
        title = "Weekly report"

    else:
        notif = notif.filter(type=REPORT_DAILY)  # daily
        title = "Daily report"

    cour_notif = notif.filter(send_to_courier=True)

    send_notifications(notif, False, title)
    send_notifications(cour_notif, True, title)

    for n in notif:
        n.sent = True
        n.save(update_fields=["sent"])


# expects datetime
def unsigned_in_day(until, customer):
    since = until.replace(hour=0, minute=0, second=0, microsecond=0)
    return (
        Document.objects.signed(False)
        .since(date_to_timestamp(since))
        .until(date_to_timestamp(until))
        .with_customer(customer)
        .count()
    )


# this sould be executed only at 12:00
def check_uploaded_amount():
    cal = Slovakia()
    week = timedelta(weeks=1)
    now = get_ba_tz(datetime.now())
    now_date = now.date()
    if cal.is_working_day(now_date):
        customers = Customer.objects.all()
        for customer in customers:
            today_count = unsigned_in_day(now, customer)
            mes = []
            for i in range(1, 5):
                mes.append(unsigned_in_day(now - (i * week), customer))

            avg = sum(mes) / len(mes)
            if today_count < (0.5 * avg):
                notify_uploaded_decrease(customer)


# this sould be executed only at 9:00
def check_assign_ratio():
    cal = Slovakia()
    now = get_ba_tz(datetime.now())
    now_date = now.date()
    if cal.is_working_day(now_date):
        day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        auto_assigns = (
            History.objects.filter(action=ASSIGN, auto=True)
            .since(day_start)
            .until(now)
            .count()
        )

        manual_assigns = (
            History.objects.filter(action=ASSIGN, auto=False)
            .since(day_start)
            .until(now)
            .count()
        )

        if auto_assigns > manual_assigns:
            notify_assign_ratio(manual_assigns / (manual_assigns + auto_assigns) * 100)


def check_remotely_signed_not_uploaded():
    documents = Document.objects.filter(
        signed_remotely=True,
        children=None,
        deleted=False,
        history__action=SIGNED_REMOTELY,
    )
    actions = {
        doc.id: doc.history.filter(action=SIGNED_REMOTELY).first() for doc in documents
    }

    notify_remotely_signed_not_uploaded(documents, actions)


def document_summary(
    summary_type: SUMMARY_TYPES,
    customer: Customer,
    execution_time: datetime,
    execution_date: date,
    last_execution_time,
) -> None:
    """Create summary from rest_document for specified type, customer and date."""
    documents = Document.objects.filter(
        timestamp__gt=datetime.timestamp(last_execution_time) * 1000,
        timestamp__lte=datetime.timestamp(execution_time) * 1000,
        customer=customer,
    ).filter_type(summary_type)

    Metric(
        type=summary_type.value,
        customer=customer,
        date=execution_date,
        executed=execution_time,
        value=documents.count(),
    ).save()


def history_summary(
    summary_type: SUMMARY_TYPES,
    customer: Customer,
    execution_time: datetime,
    execution_date: date,
    last_execution_time: datetime,
) -> None:
    """Create summary from rest_history for specified type, customer and date."""
    history = History.objects.filter(
        created_at__gt=last_execution_time,
        created_at__lte=execution_time,
        action=summary_type.value,
        document__customer=customer,
    ).distinct("document")

    Metric(
        type=summary_type.value,
        customer=customer,
        date=execution_date,
        executed=execution_time,
        value=history.count(),
    ).save()


def daily_summary(types: Iterable[SUMMARY_TYPES], summary: Callable) -> None:
    """Create summaries for the last day"""
    execution_time = datetime.now()
    execution_date = date.today()
    # if summary is executed after midnight (e.g. 00:01) set execution date to yesterday
    if execution_time.hour < 12:
        execution_date -= timedelta(days=1)

    customers = Customer.objects.filter(active=True).all()

    for summary_type in types:
        for customer in customers:
            try:
                last_execution_time = Metric.objects.get(
                    customer=customer,
                    date=(execution_date - timedelta(days=1)),
                    type=summary_type.value,
                ).executed
            except ObjectDoesNotExist:
                last_execution_time = datetime(
                    execution_date.year, execution_date.month, execution_date.day
                )

            try:
                summary(
                    summary_type,
                    customer,
                    execution_time,
                    execution_date,
                    last_execution_time,
                )
            # handle two server instances remove when cronjobs are reworked
            except IntegrityError:
                return


def parse_chain(chain):
    _PEM_RE = re.compile(
        b"-----BEGIN CERTIFICATE-----\r?.+?\r?-----END CERTIFICATE-----\r?\n?",
        re.DOTALL,
    )
    return [
        crypto.load_certificate(crypto.FILETYPE_PEM, c.group())
        for c in _PEM_RE.finditer(chain)
    ]


def validate_certificate(chain_path: str, cert_path: str, domain: str):
    with open(cert_path, "r") as cert_file:
        cert = crypto.load_certificate(
            crypto.FILETYPE_PEM, cert_file.read().encode("utf-8")
        )

    with open(chain_path, "r") as chain_file:
        chain = parse_chain(chain_file.read())

    store = crypto.X509Store()
    store.load_locations(settings.TRUSTED_CERTIFICATES)

    try:
        store_context = crypto.X509StoreContext(store, cert, chain)
        store_context.verify_certificate()
    except crypto.X509StoreContextError as e:
        send_slack_notification(
            "Certificate validation error",
            f"{domain}\n{str(e)}",
            "#D00000",
            settings.CERT_CHECKER_SLACK_URL,
        )


def check_expiration(cert: str, domain: str):
    expiration_check = subprocess.run(
        [
            "openssl",
            "x509",
            "-checkend",
            "604800",
            "-noout",
            "-in",
            cert,
        ]
    )
    if expiration_check.returncode:
        expiration = subprocess.run(
            [
                "openssl",
                "x509",
                "-enddate",
                "-in",
                cert,
            ],
            stdout=subprocess.PIPE,
        )
        expiration_time = datetime.strptime(
            expiration.stdout.split(b"\n")[0].split(b"=")[1].decode("utf8"),
            "%b %d %H:%M:%S %Y %Z",
        )
        send_slack_notification(
            "Certificate expiration warning",
            f"Certificate for {domain} will expire at "
            + expiration_time.strftime("%d.%m.%Y"),
            "#FF9900",
            settings.CERT_CHECKER_SLACK_URL,
        )


def check_certificates():
    for domain in os.listdir(settings.CERT_DIR):
        directory = f"{settings.CERT_DIR}/{domain}"
        cert = os.path.abspath(f"{directory}/cert.pem")
        chain = os.path.abspath(f"{directory}/fullchain.pem")
        if os.path.isdir(directory):
            # Check if certificate is valid
            validate_certificate(cert, chain, domain)

            # Check if certificate will expire in less than 7 days
            check_expiration(cert, domain)


def remove_old_signed_documents(customers: list[Customer]) -> None:
    """Delete signed documents that are older than n-days"""
    now = datetime.now()

    for customer in customers:
        past = int(
            (
                now - timedelta(days=customer.old_signed_packages_removal_limit)
            ).timestamp()
            * 1000
        )
        old_documents = (
            Document.objects.with_customer(customer).signed().active().until(past)
        )
        remove_documents(old_documents, True)


def remove_old_quarantine_documents(customers: list[Customer]) -> None:
    """Delete documents in quarantine that are older than n-days"""
    now = datetime.now()
    past = int(
        (
            now - timedelta(days=settings.OLD_QUARANTINE_DOCUMENTS_REMOVAL_LIMIT)
        ).timestamp()
        * 1000
    )
    old_quarantine_documents = Quarantine.objects.filter(
        deleted=False, timestamp__lte=past, customer__isnull=True
    )
    for quarantine in old_quarantine_documents:
        quarantine.remove()

    for customer in customers:
        past = int(
            (
                now - timedelta(days=customer.old_quarantine_packages_removal_limit)
            ).timestamp()
            * 1000
        )
        old_quarantine_documents = Quarantine.objects.filter(
            deleted=False, timestamp__lte=past, customer=customer
        )
        for quarantine in old_quarantine_documents:
            quarantine.remove()


def send_union_notifications() -> None:
    """Send signed documents notifications to UNION"""
    documents = Document.objects.filter(
        operator_notification=False,
        customer_id="union",
        parent__isnull=False,
        deleted=False,
    )
    for doc in documents:
        file = default_storage.open(id_to_filename(doc.id), mode="rb")
        data = {"zipFile": (doc.original_file_name, file)}
        logger.info(f"[UNION NOTIFICATION] Sending notification to union: {doc.id}")
        headers = {"Accept": "application/json"}
        result = requests.post(
            settings.UNION_NOTIFICATIONS_HOST,
            auth=(settings.UNION_BASIC_AUTH_NAME, settings.UNION_BASIC_AUTH_PASSWORD),
            files=data,
            headers=headers,
        )
        logger.info(
            f"[UNION NOTIFICATION] Send notification to union: {doc.id}\n"
            f"Response code: {result.status_code}\nResponse text: {result.text}\n"
        )
        if result.status_code == 200:
            with transaction.atomic():
                doc.operator_notification = True
                if doc.parent:
                    doc.parent.operator_notification = True
                    doc.parent.save(update_fields=["operator_notification"])
                doc.save(update_fields=["operator_notification"])
