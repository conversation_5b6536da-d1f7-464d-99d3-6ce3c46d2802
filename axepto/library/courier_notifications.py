import json
import logging
from datetime import datetime

import pytz
import requests
from django.conf import settings
from django.db import transaction

from axepto.helpers import get_ba_tz
from axepto.rest.models import Document

logger = logging.getLogger(__name__)

host = getattr(settings, "INTIME_NOTIFICATIONS_HOST", "")
imsi = "111111111111113"  # login id
key = "016e4f7"  # secret

date_format = "%y%m%d"
date_file_format = "%d.%m.%Y"
time_format = "%H%M%S"
time_file_format = "%H:%M:%S"


# content of notification file
def generate_notification_content(doc, moment):
    res = [""] * 24
    res[0] = doc.parent.internal_id
    res[7] = moment.strftime(date_file_format)
    res[8] = moment.strftime(time_file_format)
    res[9] = "64"
    res[10] = "703"
    res[11] = "1"
    res[12] = str(doc.author.user.username)
    res[13] = "e-sign"
    return ";".join(res) + ";;\n"


# name of notification file
def generate_notification_name():
    ba_tz = pytz.timezone("Europe/Bratislava")
    moment = datetime.now(ba_tz)
    date = moment.strftime(date_format)
    time = moment.strftime(time_format)
    return "tif-" + date + "-" + time + "-654321.csv"


# sends notification to courier company
@transaction.atomic()
def send_cc_notifications():
    docs = Document.objects.signed().filter(notified=False)

    if len(docs) == 0:
        return

    fc = "VERSION=4\n"
    for it in docs:
        moment = get_ba_tz(datetime.fromtimestamp(it.timestamp / 1000))
        fc = fc + generate_notification_content(it, moment)
    req = {
        "fileModel": {"fileContent": fc, "fileName": generate_notification_name()},
        "imsi": imsi,
        "licenseKey": key,
    }

    logger.info(
        "[COURIER NOTIFICATION] Sending notification to courier:\n"
        + json.dumps(req["fileModel"])
    )

    headers = {"Content-type": "application/json"}
    response = requests.post(host, data=json.dumps(req), headers=headers)

    # check result, if ok mark as notified
    try:
        json_res = json.loads(response.text)
        notified = "error" in json_res and json_res["error"] is None
    except ValueError:
        notified = False

    if notified:
        Document.objects.with_ids([doc.id for doc in docs]).update(notified=True)

    logger.info(
        "[COURIER NOTIFICATION] Response code: %s\nResponse text: %s\nNotified: %s\n"
        % (response.status_code, response.text, notified)
    )
