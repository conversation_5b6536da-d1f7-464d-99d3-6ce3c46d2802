import base64
import logging
import uuid
import xml.etree.cElementTree as ET
from datetime import datetime

import pytz
import requests
from django.conf import settings
from django.db.models import Count, Q

from axepto.rest.models import Document

logger = logging.getLogger(__name__)

host = getattr(settings, "TELEKOM_NOTIFICATIONS_HOST", "")


def render_xml_request(doc):
    # temporary legacy handling
    if settings.MODE == settings.DELIVERY_COMPANY.INTIME:
        root = ET.Element("m:updateDocSignResult")
        root.attrib["xmlns:m"] = (
            "http://services.tsvk.telekom.net"
            + "/CustRelMgmt/CustMgmt/CourierEsign_Callback/v01/types"
        )
        ET.SubElement(root, "messageId").text = uuid.uuid4().hex
        ET.SubElement(root, "stPackageId").text = doc.rest["shipment_id"]
        ET.SubElement(root, "courierId").text = "INTIME"
        ET.SubElement(root, "courierPackageId").text = doc.id
        if doc.deleted:
            status = "REJECTED"
        elif doc.parent is not None:
            status = "SIGNED"
        ET.SubElement(root, "status").text = status

        return ET.tostring(root, encoding="unicode", method="xml")

    root = ET.Element("v01:updateDocSignResult")
    root.attrib[
        "xmlns:v01"
    ] = "http://services.tsvk.telekom.net/CustRelMgmt/CustMgmt/EsignServiceNotify/v01"
    root.attrib["xmlns:typ"] = (
        "http://services.tsvk.telekom.net/CustRelMgmt/CustMgmt/EsignServiceNotify"
        + "/v01/types"
    )
    root.attrib["xmlns:csdg"] = "http://schemas.telekom.net/csdg_v01.02"

    context = ET.SubElement(root, "typ:context")

    technical_context = ET.SubElement(context, "csdg:technicalContext")
    ET.SubElement(
        technical_context, "csdg:from"
    ).text = "sk.telekom.architecture.BSS:CouriereSignToCDM"
    ET.SubElement(
        technical_context, "csdg:routingInfo"
    ).text = "sk.telekom.architecture.BSS:CouriereSignToCDM:updateDocSignResult"
    ET.SubElement(technical_context, "csdg:messageId").text = f"BSS-{uuid.uuid4().hex}"
    ET.SubElement(
        technical_context, "csdg:currentSenderTimestampUTC"
    ).text = (
        f"{datetime.now().astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3]}Z"
    )
    ET.SubElement(technical_context, "csdg:expiryOffsetInMillis").text = "180000"

    business_context = ET.SubElement(context, "csdg:businessContext")
    ET.SubElement(business_context, "csdg:processId").text = doc.rest["shipment_id"]
    ET.SubElement(
        business_context, "csdg:processTypeId"
    ).text = "ESignServiceNotify.UpdateDocSignResult"

    data = ET.SubElement(root, "typ:data")
    ET.SubElement(data, "typ:packageId").text = doc.rest["shipment_id"]
    ET.SubElement(data, "typ:courierId").text = settings.COURIER_ID
    if doc.deleted:
        status = "REJECTED"
    elif doc.parent is not None:
        status = "SIGNED"
    ET.SubElement(data, "typ:status").text = status
    ET.SubElement(data, "typ:courierInternalPackageId").text = doc.id

    return ET.tostring(root, encoding="unicode", method="xml")


def send_telekom_notifications():
    documents = Document.objects.filter(
        operator_notification=False, rest__has_key="shipment_id"
    ).filter(
        Q(deleted=True, parent__isnull=True) | Q(parent__isnull=False, deleted=False)
    )
    auth = f"Bearer {getattr(settings, 'TELEKOM_AUTH_TOKEN', '')}"
    if settings.MODE == settings.DELIVERY_COMPANY.INTIME:
        credentials = base64.b64encode(
            f"{settings.TELEKOM_BASIC_AUTH_NAME}"
            f":{settings.TELEKOM_BASIC_AUTH_PASSWORD}".encode("utf-8")
        ).decode("utf-8")
        auth = f"Basic {credentials}"

    for doc in documents:
        xml = render_xml_request(doc)
        logger.info("[TELEKOM NOTIFICATION] Sending notification to telekom:\n" + xml)
        headers = {
            "Content-Type": "application/xml",
            "Accept": "application/xml",
            "Authorization": auth,
        }

        result = requests.post(
            host,
            data=xml,
            headers=headers,
            verify=False,
            cert=(
                settings.TELEKOM_CERTIFICATE_PATH,
                settings.TELEKOM_SERVER_KEY_PATH,
            ),
        )
        logger.info(
            "[TELEKOM NOTIFICATION] Response code: %s\nResponse text: %s\n"
            % (result.status_code, result.request.headers)
        )
        if result.status_code == 200:
            doc.operator_notification = True
            if doc.parent:
                doc.parent.operator_notification = True
                doc.parent.save(update_fields=["operator_notification"])
            doc.save(update_fields=["operator_notification"])
