from django.conf import settings
from django.core.mail import send_mail as django_send_email

NOTIFICATIONS_RECIPIENTS = getattr(settings, "NOTIFICATIONS_RECIPIENTS", [])
NOTIFICATION_MAIL = getattr(settings, "NOTIFICATION_MAIL", "<EMAIL>")


# if recipients empty message will be sent to axepto support
def send_email(
    subject="Axepto Notification",
    text="You have new notification",
    recipients=NOTIFICATIONS_RECIPIENTS,
):
    django_send_email(subject, text, NOTIFICATION_MAIL, recipients, html_message=text)
