from heapq import heappush
from unittest import mock

import requests
from django.test import TestCase

from axepto.library.telekom_notifications import send_telekom_notifications
from axepto.rest.models import Document


def mocked_requests_post(*args, **kwargs):
    print("Calling request.post, kwargs:")
    print(kwargs)

    class MockResponse:
        status_code = 200

        class request:
            headers = {}

    return MockResponse


class TelekomNotificationsTestCase(TestCase):
    def setUp(self):
        self.doc1 = Document.objects.create(
            rest={"shipment_id": "SHIP_ID_1"},
            id="1",
            deleted=False,
            operator_notification=False,
        )
        self.doc2 = Document.objects.create(
            rest={"shipment_id": "SHIP_ID_2"},
            id="2",
            deleted=False,
            operator_notification=False,
            parent=self.doc1,
        )
        self.doc3 = Document.objects.create(
            rest={"shipment_id": "SHIP_ID_3"},
            id="3",
            deleted=True,
            operator_notification=False,
        )

    @mock.patch("requests.post", side_effect=mocked_requests_post)
    def test1(self, mock_post):
        send_telekom_notifications()
        self.assertEqual(
            Document.objects.filter(operator_notification=False).count(), 0
        )
