from django.conf import settings
from django.conf.urls import include
from django.contrib import admin
from django.urls import path, re_path

import axepto.archive.urls
import axepto.audit.urls
import axepto.rest.urls
import axepto.test.urls
import axepto.web.urls
from axepto.cron import CronRunView

urlpatterns = [
    re_path(r"^archive/", include(axepto.archive.urls)),
    re_path(r"^admin/", admin.site.urls),
    re_path(r"^test/", include(axepto.test.urls)),
    re_path(r"^web/", include(axepto.web.urls)),
    re_path(r"^", include(axepto.rest.urls)),
    re_path(r"^web/audit/", include(axepto.audit.urls)),
    path(settings.CRON_ENDPOINT, CronRunView.as_view(), name="run_crons"),
]

if settings.DEBUG:
    import debug_toolbar

    # For django versions before 2.0:
    urlpatterns = [
        re_path(r"^__debug__/", include(debug_toolbar.urls)),
    ] + urlpatterns
