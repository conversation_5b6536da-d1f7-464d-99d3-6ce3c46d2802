from axepto.audit.models import <PERSON>t


def audit_setting_change(sender, instance, **kwargs):
    if instance.name == "AUDIT_ENABLED":
        current_audit = Audit.objects.first()

        current_status = True if current_audit and current_audit.status else False
        new_status = instance.value

        if new_status and not current_status:
            Audit.objects.create(status=True)

        elif not new_status and current_status:
            current_audit.status = False
            current_audit.save(update_fields=["status"])
