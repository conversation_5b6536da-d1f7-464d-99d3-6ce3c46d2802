from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, ListView, UpdateView, View
from setty import config
from setty.models import SettySettings

from axepto.audit.forms import AuditExportForm
from axepto.audit.models import Audit, AuditEntry, AuditExport
from axepto.constants import web_permissions
from axepto.web.helpers import specific_login_required
from axepto.web.menu import get_menu


class AuditView(ListView):
    template_name = "audit.html"
    model = AuditEntry

    def get_context_data(self, **kwargs):
        context = super(AuditView, self).get_context_data(**kwargs)
        context.update(
            {
                "menu_": get_menu(self.request),
                "audit": get_object_or_404(Audit, status=True),
            }
        )
        return context

    def get_queryset(self):
        return (
            super(AuditView, self)
            .get_queryset()
            .filter(audit=Audit.objects.first())[:100]
        )

    @specific_login_required(web_permissions["audit"])
    def get(self, request, *args, **kwargs):
        return super(AuditView, self).get(request, *args, **kwargs)


class AuditStatusView(ListView):
    model = Audit
    template_name = "audit_status.html"

    def get_context_data(self, **kwargs):
        context = super(AuditStatusView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    @specific_login_required(web_permissions["audit_status"])
    def post(self, request, *args, **kwargs):
        config.AUDIT_ENABLED = not config.AUDIT_ENABLED
        SettySettings.objects.filter(name="AUDIT_ENABLED").first().save()
        return super(AuditStatusView, self).get(request, *args, **kwargs)

    @specific_login_required(web_permissions["audit_status"])
    def get(self, request, *args, **kwargs):
        return super(AuditStatusView, self).get(request, *args, **kwargs)


class AuditExportListView(ListView):
    model = AuditExport
    template_name = "audit_export.html"

    def get_context_data(self, **kwargs):
        context = super(AuditExportListView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    @specific_login_required(web_permissions["audit"])
    def get(self, request, *args, **kwargs):
        return super(AuditExportListView, self).get(request, *args, **kwargs)


class AuditExportCreateView(CreateView):
    model = AuditExport
    form_class = AuditExportForm
    template_name = "audit_export_update.html"
    success_url = reverse_lazy("audit-export")

    def get_context_data(self, **kwargs):
        context = super(AuditExportCreateView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    @specific_login_required(web_permissions["audit"])
    def get(self, request, *args, **kwargs):
        return super(AuditExportCreateView, self).get(request, *args, **kwargs)

    @specific_login_required(web_permissions["audit"])
    def post(self, request, *args, **kwargs):
        return super(AuditExportCreateView, self).post(request, *args, **kwargs)


class AuditExportUpdateView(UpdateView):
    model = AuditExport
    form_class = AuditExportForm
    template_name = "audit_export_update.html"
    success_url = reverse_lazy("audit-export")

    def get_context_data(self, **kwargs):
        context = super(AuditExportUpdateView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    @specific_login_required(web_permissions["audit"])
    def get(self, request, *args, **kwargs):
        return super(AuditExportUpdateView, self).get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        return super(AuditExportUpdateView, self).post(request, *args, **kwargs)


class AuditExportDeleteView(DeleteView):
    model = AuditExport
    template_name = "audit_export_update.html"
    success_url = reverse_lazy("audit-export")

    @specific_login_required(web_permissions["audit"])
    def post(self, request, *args, **kwargs):
        return super(AuditExportDeleteView, self).post(request, *args, **kwargs)


class AuditExportDownloadView(View):
    @specific_login_required(web_permissions["audit"])
    def get(self, request, *args, **kwargs):
        export = get_object_or_404(AuditExport, pk=kwargs.get("pk"))
        response = HttpResponse(export.result.read(), content_type="text/csv")
        response["Content-Disposition"] = f"inline; filename={export.result.name}.csv"
        return response
