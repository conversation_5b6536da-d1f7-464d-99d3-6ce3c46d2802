from django.urls import path

from axepto.audit import views

urlpatterns = [
    path(r"", views.AuditView.as_view(), name="audit"),
    path(r"status/", views.AuditStatusView.as_view(), name="audit-status"),
    path(r"export/", views.AuditExportListView.as_view(), name="audit-export"),
    path(
        r"export/create",
        views.AuditExportCreateView.as_view(),
        name="audit-export-create",
    ),
    path(
        r"export/update/<int:pk>/",
        views.AuditExportUpdateView.as_view(),
        name="audit-export-update",
    ),
    path(
        r"export/delete/<int:pk>/",
        views.AuditExportDeleteView.as_view(),
        name="audit-export-delete",
    ),
    path(
        r"export/download/<int:pk>/",
        views.AuditExportDownloadView.as_view(),
        name="audit-export-download",
    ),
]
