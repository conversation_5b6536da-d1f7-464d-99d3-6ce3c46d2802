import csv
from datetime import datetime
from io import StringIO

import pytz
from django.conf import settings
from django.core.files.base import ContentFile
from setty import config

from axepto.audit.models import Audit, AuditEntry, AuditExport


def list_to_choices(items):
    return [(item, item) for item in items]


def get_all_data_keys():
    return get_data_keys(AuditEntry.objects.all().iterator())


def get_data_keys(entries):
    unique_keys = set()
    for entry_data in entries:
        unique_keys.update(entry_data.data.keys())

    return unique_keys


def get_export_data(start_datetime, end_datetime):
    data = AuditEntry.objects.filter(
        created_at__gt=start_datetime, created_at__lt=end_datetime
    )

    keys = get_data_keys(data.iterator())

    return keys, data.iterator()


def create_exports():
    exports = AuditExport.objects.filter(result="", end_datetime__lt=datetime.now())

    for export in exports:
        export.task_start_datetime = datetime.now(tz=pytz.timezone("Europe/Bratislava"))

        keys, data = get_export_data(export.start_datetime, export.end_datetime)
        keys = keys.intersection(set(export.field_names))
        ordered_keys = [key for key in export.field_names if key in keys]

        csv_buffer = StringIO()
        csv_writer = csv.writer(csv_buffer)
        csv_writer.writerow(ordered_keys + ["Created at"])
        for row in data:
            csv_writer.writerow(
                [row.data.get(key, "") for key in ordered_keys]
                + [row.created_at.strftime("%Y-%m-%d %H:%M:%S")]
            )
        export.result.save(
            f"Export_{export.pk}", ContentFile(csv_buffer.getvalue().encode("utf-8"))
        )

        export.task_end_datetime = datetime.now(tz=pytz.timezone("Europe/Bratislava"))
        export.save(
            update_fields=["result", "task_start_datetime", "task_end_datetime"]
        )


def add_audit_entry(data, courier_name, operator_name):
    tracking_keys = getattr(settings, "TRACKING_KEYS", [])
    tracking_data = dict()

    for key in tracking_keys:
        if key in data:
            tracking_data[key] = data[key]
            del data[key]

    if config.AUDIT_ENABLED:
        last_audit = Audit.objects.first()
        AuditEntry.objects.create(
            audit=last_audit,
            data=data,
            courier_name=courier_name,
            operator_name=operator_name,
        )
    return tracking_data
