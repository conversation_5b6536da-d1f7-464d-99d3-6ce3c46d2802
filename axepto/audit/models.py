from django.contrib.postgres.fields.array import Array<PERSON>ield
from django.db.models import (
    CASCADE,
    BooleanField,
    CharField,
    DateTimeField,
    FileField,
    ForeignKey,
    JSONField,
    Model,
)


class Audit(Model):
    status: BooleanField = <PERSON>oleanField(
        null=False,
        blank=True,
        default=True,
    )

    created_at: DateTimeField = DateTimeField(
        null=False,
        blank=False,
        auto_now_add=True,
    )

    updated_at: DateTimeField = DateTimeField(
        null=False,
        blank=False,
        auto_now=True,
    )

    def __str__(self):
        return f'Audit {self.pk} ({"Active" if self.status else "Inactive"})'

    class Meta:
        ordering = ["-pk"]


class AuditEntry(Model):
    data: JSONField = JSONField(
        default=dict,
    )

    audit: ForeignKey = ForeignKey(
        to=Audit,
        null=True,
        on_delete=CASCADE,
        related_name="entries",
    )

    courier_name: CharField = CharField(
        null=True,
        blank=False,
        max_length=127,
    )

    operator_name: CharField = CharField(
        null=False,
        blank=False,
        max_length=127,
    )

    created_at: DateTimeField = DateTimeField(
        null=False, blank=False, auto_now_add=True
    )


class AuditExport(Model):
    start_datetime: DateTimeField = DateTimeField(
        null=True,
        blank=True,
    )

    end_datetime: DateTimeField = DateTimeField(
        null=True,
        blank=True,
    )

    field_names: ArrayField = ArrayField(
        default=list,
        base_field=CharField(
            max_length=50,
            null=False,
            blank=False,
        ),
    )

    created_at: DateTimeField = DateTimeField(
        null=False,
        blank=False,
        auto_now_add=True,
    )

    task_start_datetime: DateTimeField = DateTimeField(
        null=True,
        blank=True,
    )

    task_end_datetime: DateTimeField = DateTimeField(
        null=True,
        blank=True,
        auto_now_add=True,
    )

    result: FileField = FileField(
        null=True,
        blank=True,
        upload_to="exports",
        max_length=150,
    )

    def delete(self, using=None, keep_parents=False):
        if self.is_finished:
            self.result.delete()
        return super(AuditExport, self).delete(using, keep_parents)

    @property
    def is_finished(self):
        return bool(self.result.name)

    def __str__(self):
        return (
            f'Export {self.pk} ({"Finished" if self.is_finished else "Not finished"})'
        )

    class Meta:
        ordering = ["-pk"]
