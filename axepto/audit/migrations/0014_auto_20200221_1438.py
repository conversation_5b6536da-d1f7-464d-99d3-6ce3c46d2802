# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-02-21 14:38
from __future__ import unicode_literals

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("audit", "0013_auto_20200221_1417"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="exportaction",
            name="schedule",
        ),
        migrations.AddField(
            model_name="exportschedule",
            name="result",
            field=models.FileField(max_length=150, null=True, upload_to="exports"),
        ),
        migrations.AddField(
            model_name="exportschedule",
            name="task_end_datetime",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="exportschedule",
            name="task_start_datetime",
            field=models.DateTimeField(default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.DeleteModel(
            name="ExportAction",
        ),
    ]
