# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-02-11 13:20
from __future__ import unicode_literals

from typing import List, Tuple

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies: List[Tuple[str, str]] = []

    operations = [
        migrations.CreateModel(
            name="AuditEntry",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("data", django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ("operator_name", models.CharField(max_length=127)),
                ("datetime", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
