# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-02-29 10:03
from __future__ import unicode_literals

from django.db import migrations
from setty.models import SettySettings, TypeChoices


def create_audit_setting(_, __):
    if not SettySettings.objects.filter(name__exact="AUDIT_ENABLED").exists():
        SettySettings.objects.create(
            name="AUDIT_ENABLED",
            type=TypeChoices.BOOL,
            value=0,
        )


def delete_audit_setting(_, __):
    SettySettings.objects.filter(name__exact="AUDIT_ENABLED").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("audit", "0018_auto_20200224_0945"),
        ("setty", "0002_settysettings_app_name"),
    ]

    operations = [migrations.RunPython(create_audit_setting, delete_audit_setting)]
