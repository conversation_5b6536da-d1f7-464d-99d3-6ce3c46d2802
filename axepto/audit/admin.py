from django.contrib import admin

from axepto.audit.models import Audit, AuditEntry, AuditExport


class AuditAdmin(admin.ModelAdmin):
    list_display = ("id", "created_at", "updated_at", "status")
    readonly_fields = ("status",)


class AuditEntryAdmin(admin.ModelAdmin):
    list_display = ("operator_name", "created_at")
    readonly_fields = ("created_at",)


class AuditExportAdmin(admin.ModelAdmin):
    list_display = ("created_at", "start_datetime", "end_datetime")


admin.site.register(Audit, AuditAdmin)
admin.site.register(AuditEntry, AuditEntryAdmin)
admin.site.register(AuditExport, AuditExportAdmin)
