from django import forms
from django.forms import ValidationError

from axepto.audit.helpers import get_all_data_keys, list_to_choices
from axepto.audit.models import AuditExport
from axepto.constants import FORM_DATETIME_FORMAT


class AuditExportForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super(AuditExportForm, self).__init__(*args, **kwargs)
        if self.instance.is_finished:
            self.fields["start_datetime"].disabled = True
            self.fields["end_datetime"].disabled = True
            self.fields["field_names"].disabled = True
        self.fields["field_names"].choices = list_to_choices(get_all_data_keys())

    start_datetime = forms.DateTimeField(
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        input_formats=[FORM_DATETIME_FORMAT],
    )

    end_datetime = forms.DateTimeField(
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        input_formats=[FORM_DATETIME_FORMAT],
    )

    field_names = forms.MultipleChoiceField(
        choices=[],
        widget=forms.SelectMultiple(attrs={"class": "form-control choices-multiple"}),
    )

    def clean_end_datetime(self):
        if self.cleaned_data["start_datetime"] >= self.cleaned_data["end_datetime"]:
            raise ValidationError("End datetime have to be higher than start datetime")
        return self.cleaned_data["end_datetime"]

    class Meta:
        model = AuditExport
        fields = ("start_datetime", "end_datetime", "field_names")
