{% load url_process %}

<div>
    <div class="card overflow-auto">
        <table class="table table-striped dataTable w-100">
            <thead>
                <tr>
                    {% include "th_sorting.html" with request=request name="ID"%}
                    {% include "th_sorting.html" with request=request name="Date created"%}
                    {% include "th_sorting.html" with request=request name="Time created"%}
                    {% include "th_sorting.html" with request=request name="Date updated"%}
                    {% include "th_sorting.html" with request=request name="Time updated"%}
                    {% include "th_sorting.html" with request=request name="Entries collected"%}
                    {% include "th_sorting.html" with request=request name="Status"%}
                </tr>
            </thead>
            <tbody>
                {% for audit in objects %}
                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                        <td>{{ audit.pk }}</td>
                        <td>{{ audit.created_at|date:'d.m.Y' }}</td>
                        <td>{{ audit.created_at|date:'H:i' }}</td>
                        <td>{{ audit.updated_at|date:'d.m.Y' }}</td>
                        <td>{{ audit.updated_at|date:'H:i' }}</td>
                        <td>{{ audit.entries.count }}</td>
                        <td>
                            <span class="{% if audit.status %}text-success{% else %}text-danger{% endif %}">
                                {% if audit.status %}Enabled{% else %}Disabled{% endif %}
                            </span>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>