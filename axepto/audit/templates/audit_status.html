{% extends 'base.html' %}
{% load url_process %}

{% block nav-audit %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3>
                <strong>Audit status:</strong>
                <span class="{% if setty.AUDIT_ENABLED %}text-success{% else %}text-danger{% endif %}">
                    {% if setty.AUDIT_ENABLED %}Enabled{% else %}Disabled{% endif %}
                </span>
                <button class="btn btn-default" data-bs-toggle="modal" data-bs-target="#modal">
                    {% if setty.AUDIT_ENABLED %} Disable {% else %} Enable {% endif %}
                </button>
            </h3>
        </div>
        <div class="row">
            {% include "audit_status_table.html" with objects=object_list %}
        </div>
    </div>
</main>
<div class="modal fade" id="modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLabel">{% if setty.AUDIT_ENABLED %}Disable{% else %}Enable{% endif %} audit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to change audit status?
            </div>
            <form method="post" action="{% url 'audit-status' %}" class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn {% if setty.AUDIT_ENABLED %}btn-danger{% else %}btn-success{% endif %}" id="confirm-delete">
                    {% if setty.AUDIT_ENABLED %}Disable{% else %}Enable{% endif %}
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}