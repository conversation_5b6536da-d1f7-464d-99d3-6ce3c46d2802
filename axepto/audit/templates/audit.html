{% extends 'base.html' %}
{% load url_process %}

{% block nav-audit %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3>
                <strong>Audit</strong>
            </h3>
        </div>
        <div class="row mb-3">
            <div>
                <div class="card py-3">
                    <div class="row">
                        <div class="col-12 col-sm-6 px-3">
                            <div>
                                <strong>Audit status: </strong>
                                {% if audit.status %}
                                    <span class="text-success">Enabled</span>
                                {% else %}
                                    <span class="text-danger">Disabled</span>
                                {% endif %}
                                {% if menu_.audit_status %}
                                    <span><a href="{% url 'audit-status' %}">(Change status)</a></span>
                                {% endif %}
                            </div>
                            <div>
                                <strong>Date created: </strong><span>{{ audit.created_at|date:'d.m.Y' }}</span>
                            </div>
                            <div>
                                <strong>Time created: </strong><span>{{ audit.created_at|date:'H:i' }}</span>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 px-3">
                            <div>
                                <strong>Date updated: </strong><span>{{ audit.updated_at|date:'d.m.Y' }}</span>
                            </div>
                            <div>
                                <strong>Time updated: </strong><span>{{ audit.updated_at|date:'H:i' }}</span>
                            </div>
                            <div>
                                <strong>Entries collected: </strong><span>{{ audit.entries.count }} <a href="{% url 'audit-export' %}">(Export entries)</a></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <h4>
                Collected entries <span class="text-muted">(last 100)</span>
            </h4>
        </div>
        <div class="row">
            {% include "audit_entries_table.html" with objects=object_list %}
        </div>
    </div>
</main>
{% endblock %}