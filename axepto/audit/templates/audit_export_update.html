{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block nav-audit %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="container-md vw-100 py-4">
    <div class="container-fluid p-0">
        <div class="row">
            <h3>
                <strong>
                    {% if form.instance.pk %}
                        Update audit export {{ form.instance.pk }}
                    {% else %}
                        New audit export
                    {% endif %}
                </strong>
            </h3>
        </div>
        <div class="row">
            <div class="card p-3">
                <form method="post">
                    {% for field in form %}
                        <div class="mb-3">
                            <label class="mb-2" for="{{ field.id_for_label }}">{{ field.label }}</label>
                            {{ field }}
                            {{ field.errors }}
                        </div>
                    {% endfor %}
                    <div class="d-flex justify-content-center">
                        <input type='submit' value='Save' class="btn btn-success"/>
                        {% if form.instance.is_finished %}
                            <a class="btn btn-primary ms-2" href="{% url 'audit-export-download' pk=form.instance.pk %}">Download export file</a>
                        {% endif %}
                        {% if form.instance.pk %}
                            <button class="btn btn-danger ms-2" type="button" data-bs-toggle="modal" data-bs-target="#modal">
                                Delete export
                            </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>
{% if form.instance.pk %}
    <div class="modal fade" id="modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <form class="modal-content" action="{% url 'audit-export-delete' pk=form.instance.pk %}" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Delete audit export</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this export and generated file?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
{% endif %}

<script>
  document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true
    })
    for(const select of document.querySelectorAll(".choices-multiple")) {
      new Choices(select, {removeItemButton: true, shouldSortItems: true})
    }
  })
</script>
{% endblock %}