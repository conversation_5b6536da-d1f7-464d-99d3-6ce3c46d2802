{% load url_process %}

<div>
    <div class="card overflow-auto">
        <table class="table table-striped dataTable w-100">
            <thead>
                <tr>
                    {% include "th_sorting.html" with request=request name="ID"%}
                    {% include "th_sorting.html" with request=request name="Courier name"%}
                    {% include "th_sorting.html" with request=request name="Date created"%}
                    {% include "th_sorting.html" with request=request name="Time created"%}
                </tr>
            </thead>
            <tbody>
                {% for entry in objects %}
                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                        <td>{{ entry.pk }}</td>
                        <td>{{ entry.courier_name }}</td>
                        <td>{{ entry.created_at|date:'d.m.Y' }}</td>
                        <td>{{ entry.created_at|date:'H:i' }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>