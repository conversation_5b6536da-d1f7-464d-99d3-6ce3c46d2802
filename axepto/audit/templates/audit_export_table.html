{% load url_process %}

<div>
    <div class="card overflow-auto">
        <table class="table table-striped dataTable w-100">
            <thead>
                <tr>
                    {% include "th_sorting.html" with request=request name="ID"%}
                    {% include "th_sorting.html" with request=request name="Start date"%}
                    {% include "th_sorting.html" with request=request name="Start time"%}
                    {% include "th_sorting.html" with request=request name="End date"%}
                    {% include "th_sorting.html" with request=request name="End time"%}
                    {% include "th_sorting.html" with request=request name="Finished"%}
                    {% include "th_sorting.html" with request=request%}
                    {% include "th_sorting.html" with request=request%}
                </tr>
            </thead>
            <tbody>
                {% for export in objects %}
                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                        <td>{{ export.pk }}</td>
                        <td>{{ export.start_datetime|date:'d.m.Y' }}</td>
                        <td>{{ export.start_datetime|date:'H:i:s' }}</td>
                        <td>{{ export.end_datetime|date:'d.m.Y' }}</td>
                        <td>{{ export.end_datetime|date:'H:i:s' }}</td>
                        <td>{{ export.is_finished }}</td>
                        <td>
                            <a href="{% url 'audit-export-update' pk=export.pk %}" class="btn btn-primary text-truncate w-100">Change</a>
                        </td>
                        <td>
                            {% if export.is_finished %}
                                <a href="{% url 'audit-export-download' pk=export.pk %}" class="btn btn-primary text-truncate w-100">Download</a>
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>