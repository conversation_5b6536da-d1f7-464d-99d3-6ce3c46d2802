import csv
import gc
import locale
import os
import uuid
from calendar import monthrange
from collections import defaultdict
from dataclasses import InitVar, dataclass
from datetime import date, datetime, timedelta
from io import StringIO
from typing import Type

from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.core.files import File
from django.core.files.base import ContentFile
from django.db.models import Count, F, Q, QuerySet
from django.forms import Form
from django.http import HttpRequest, HttpResponse
from django.utils.decorators import method_decorator
from workalendar.europe import Slovakia

from axepto.constants import (
    ACTION_TRANS,
    EXCEL_DATE_FORMAT,
    FORM_DATETIME_FORMAT,
    SUMMARY_TYPES,
    WEB_DATETIME_FORMAT,
    web_permissions,
)
from axepto.helpers import (
    date_to_timestamp,
    format_time,
    get_ba_tz,
    get_utc_tz,
    json_response,
)
from axepto.rest.models import (
    Ban,
    Courier,
    Document,
    History,
    Log,
    Metric,
    Quarantine,
    Survey,
    SurveyRecord,
)
from axepto.rest.zip_tools import InMemoryZip
from axepto.useful import get_in

from ..archive.models import DocumentArchive
from .menu import render_to_html

work_cal = Slovakia()
delta_day = timedelta(days=1)
delta_week = timedelta(days=7)


def batch_query_set(query_set, batch_size=30000):
    """
    Returns a (start, end, total, queryset) tuple for each batch in the given
    queryset. Useful when memory is an issue. Picked from djangosnippets.
    """
    # making sure we have definitive order
    order_by = query_set.query.order_by
    query_set = query_set.order_by(*order_by, "-pk")

    total = query_set.count()

    for start in range(0, total, batch_size):
        end = min(start + batch_size, total)
        yield (start, end, total, query_set[start:end])
        gc.collect()


def has_perm(user, perm):
    for allowed in web_permissions[perm]:
        if hasattr(user, allowed):
            return True
    return False


def get_rep_docs(uns_docs):
    return list(map(lambda doc: doc.get_representing_doc(), uns_docs))


# formating time
def clean_doc(doc: Document, dt=False) -> Document:
    doc.uploaded = doc.get_parent_time(dt)
    doc.signed = doc.get_signed_time(dt)

    return doc


def get_clean_docs(docs: list[Document], dt=False) -> list[Document]:
    return list(map(lambda doc: clean_doc(doc, dt), docs))


def prepare_dashboard_params(params):
    # order by
    ob = params.get("order_by", "")
    if not ob:
        ob = "-uploaded"

    params["order_by"] = ob  # we do this for templates

    if "prefilter" in params:
        if params["prefilter"] == "signed_today":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            params["begin"] = day_start.strftime(FORM_DATETIME_FORMAT)

        elif params["prefilter"] == "signed_last_work_day":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            the_day = get_ba_tz(datetime.now())
            while True:
                the_day = the_day - delta_day
                if work_cal.is_working_day(the_day):
                    break
            day_start = the_day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = the_day.replace(hour=23, minute=59, second=59, microsecond=0)
            params["begin"] = day_start.strftime(FORM_DATETIME_FORMAT)
            params["end"] = day_end.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "signed_this_week":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            monday = now - timedelta(days=now.weekday())
            monday = monday.replace(hour=0, minute=0, second=0, microsecond=0)
            params["begin"] = monday.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "signed_last_weekend":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            week_ago = now - delta_week
            week_ago_weekday = week_ago.weekday()
            week_ago_saturday = week_ago + timedelta(days=(5 - week_ago_weekday))
            week_ago_sunday = week_ago + timedelta(days=(6 - week_ago_weekday))
            week_ago_saturday = week_ago_saturday.replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            week_ago_sunday = week_ago_sunday.replace(
                hour=23, minute=59, second=59, microsecond=0
            )
            params["begin"] = week_ago_saturday.strftime(FORM_DATETIME_FORMAT)
            params["end"] = week_ago_sunday.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "signed_last_week":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            week_ago = now - delta_week
            week_ago_weekday = week_ago.weekday()
            past_monday = week_ago - timedelta(days=week_ago_weekday)
            past_sunday = week_ago + timedelta(days=(6 - week_ago_weekday))
            past_monday = past_monday.replace(hour=0, minute=0, second=0, microsecond=0)
            past_sunday = past_sunday.replace(
                hour=23, minute=59, second=59, microsecond=0
            )
            params["begin"] = past_monday.strftime(FORM_DATETIME_FORMAT)
            params["end"] = past_sunday.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "signed_this_month":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            first = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            params["begin"] = first.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "signed_last_month":
            params["status"] = "signed"
            params["order_by"] = "-signed"
            now = get_ba_tz(datetime.now())
            first = now.replace(day=1)
            last_month_day = first - delta_week
            last_month_start = last_month_day.replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )
            last_month_end = last_month_day.replace(
                day=monthrange(last_month_day.year, last_month_day.month)[1],
                hour=23,
                minute=59,
                second=59,
                microsecond=0,
            )
            params["begin"] = last_month_start.strftime(FORM_DATETIME_FORMAT)
            params["end"] = last_month_end.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "assigned":
            params["status"] = "assigned"
        elif params["prefilter"] == "unsigned_today":
            params["status"] = "unsigned"
            now = get_ba_tz(datetime.now())
            day_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            params["begin"] = day_start.strftime(FORM_DATETIME_FORMAT)
        elif params["prefilter"] == "unsigned_last_work_day":
            params["status"] = "unsigned"
            the_day = get_ba_tz(datetime.now())
            while True:
                the_day = the_day - delta_day
                if work_cal.is_working_day(the_day):
                    break
            day_start = the_day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = the_day.replace(hour=23, minute=59, second=59, microsecond=0)
            params["begin"] = day_start.strftime(FORM_DATETIME_FORMAT)
            params["end"] = day_end.strftime(FORM_DATETIME_FORMAT)

    return params


def get_raw_dashboard_data(model: Type[Document] | Type[DocumentArchive] = Document):
    doc = (
        model.objects.exclude(children__isnull=False)
        .annotate(customern=F("customer__name"))
        .annotate(shipper_id=F("customer__shipper_id"))
        .prefetch_related("parent", "author__user")
        .select_related("customer")
    )

    return doc


def get_dashboard_data(
    request: HttpRequest,
    form: Form,
    courier=None,
    model: Type[Document] | Type[DocumentArchive] = Document,
    archive: bool = False,
):
    doc = get_raw_dashboard_data(model)

    if not has_perm(request.user, "see_deleted_documents"):
        doc = doc.active()

    if not has_perm(request.user, "see_all_customer_documents"):
        doc = doc.with_courier(request.user.id)

    if not has_perm(request.user, "see_documents_of_all_customers"):
        doc = doc.with_customer(request.user.operator.customer)

    form.is_valid()
    if hasattr(form, "cleaned_data"):
        cd = form.cleaned_data
        if cd["begin"]:
            doc = doc.since(date_to_timestamp(cd["begin"]))
        if cd["end"]:
            doc = doc.until(date_to_timestamp(cd["end"]))
        if cd["q"]:
            doc = doc.text_query(cd["q"])
        if cd.get("customer", None):
            doc = doc.with_customer(cd["customer"])
        if "courier" in cd and cd["courier"]:
            try:
                cr = int(cd["courier"])
                doc = doc.filter(Q(author__user__id=cr) | Q(courier__user__id=cr))
            except ValueError:
                pass
        if courier:
            try:
                cr = int(courier)
                doc = doc.filter(Q(author__user__id=cr) | Q(courier__user__id=cr))
            except ValueError:
                pass
        if cd["status"]:
            if cd["status"] == "unsigned":
                doc = doc.signed(False)
            if cd["status"] == "signed":
                doc = doc.signed(True)
            if cd["status"] == "assigned" and not archive:
                doc = doc.signed(False).exclude(courier=None)

    ob = request.GET.get("order_by", "")
    if ob:
        if ob == "-city":
            doc = sorted(
                doc, key=lambda x: locale.strxfrm(x.get_city_name()), reverse=True
            )
        elif ob == "city":
            doc = sorted(
                doc, key=lambda x: locale.strxfrm(x.get_city_name()), reverse=False
            )
        elif ob == "-authorn":
            doc = doc.order_by("-author__user__username")
        elif ob == "authorn":
            doc = doc.order_by("author__user__username")
        elif ob == "-uploaded":
            doc = doc.order_by("-timestamp")
        elif ob == "uploaded":
            doc = doc.order_by("timestamp")
        elif ob == "-signed":
            doc = doc.order_by("-signed_locally", "-timestamp")
        elif ob == "signed":
            doc = doc.order_by("-signed_locally", "timestamp")
        else:
            doc = doc.order_by(ob)

    return doc


def get_document_history(
    request: HttpRequest,
    document: Document,
    model: Type[Document] | Type[DocumentArchive] = History,
):
    his = model.objects

    if document:
        his = his.filter(document_id=document.id)

    # order by
    ob = request.GET.get("order_by", "-created_at")
    request.GET = request.GET.copy()
    request.GET["order_by"] = ob  # we do this for templates
    if ob:
        his = his.order_by(ob)

    return his


def get_history_data(request: HttpRequest, form: Form):
    his = History.objects.all()

    form.is_valid()
    if hasattr(form, "cleaned_data"):
        cd = form.cleaned_data
        if cd["begin"]:
            his = his.filter(created_at__gte=get_utc_tz(cd["begin"]))
        if cd["end"]:
            his = his.filter(created_at__lte=get_utc_tz(cd["end"]))
        if cd["courier"]:
            try:
                cr = int(cd["courier"])
                his = his.filter(Q(author__id=cr) | Q(user__id=cr))
            except ValueError:
                pass
        if cd["type"] in ACTION_TRANS:
            his = his.filter(action=cd["type"])

        if cd["auto"]:
            dic = {"yes": True, "no": False}
            if cd["auto"] in dic:
                his = his.filter(auto=dic[cd["auto"]])

    # order by
    ob = request.GET.get("order_by", "-created_at")
    request.GET = request.GET.copy()
    request.GET["order_by"] = ob  # we do this for templates
    if ob:
        his = his.order_by(ob)

    return his


def get_quarantine_data(request: HttpRequest) -> list[Quarantine]:
    quarantine_documents = Quarantine.objects.select_related(
        "customer", "courier__user"
    ).all()

    # order by
    order_by = request.GET.get("order_by", "-timestamp")
    request.GET = request.GET.copy()
    request.GET["order_by"] = order_by  # we do this for templates
    quarantine_documents = quarantine_documents.order_by(order_by)

    show = request.GET.get("show", "active")
    request.GET["show"] = show
    if show == "active":
        quarantine_documents = quarantine_documents.filter(deleted=False)
    if show == "deleted":
        quarantine_documents = quarantine_documents.filter(deleted=True)

    return quarantine_documents


def get_logs_data(request):
    logs = Log.objects.all()

    # order by
    ob = request.GET.get("order_by", "-created_at")
    request.GET = request.GET.copy()
    request.GET["order_by"] = ob  # we do this for templates
    logs = logs.order_by(ob)

    return logs


def get_surveys_data(request):
    sur = Survey.objects.annotate(count_couriers=Count("couriers")).order_by(
        "-created_at"
    )

    return sur


def get_survey_records_data(request, survey_id):
    rec = SurveyRecord.objects.filter(survey=survey_id).order_by("-timestamp")

    return rec


@dataclass
class Paginator:
    query_set: QuerySet
    page_size: int
    page: int
    data: QuerySet | None = None
    data_size: int = 0
    num_pages: int = 0

    def __post_init__(self):
        self.data = self.query_set[
            self.page_size * (self.page - 1) : self.page_size * self.page + 1
        ]
        self.num_pages = self.page + len(self.data) // (self.page_size + 1)
        self.data = self.data[: self.page_size]
        self.data_size = len(self.data)

    def previous_page_number(self) -> int:
        return self.page - 1

    def next_page_number(self) -> int:
        return self.page + 1

    def has_previous(self) -> bool:
        return self.page > 1

    def has_next(self) -> bool:
        return self.num_pages > self.page

    def first_showing(self) -> int:
        return self.page_size * (self.page - 1) + 1

    def last_showing(self) -> int:
        return self.first_showing() + self.data_size - 1

    def summarize(self) -> str:
        return (
            f"Showing {self.first_showing() if self.data_size else 0} to "
            f"{self.last_showing()} entries"
        )


def paginate(request, doc):
    paginator = Paginator(
        doc, int(request.GET.get("page_size", 10)), int(request.GET.get("page", 1))
    )
    page = paginator.data

    return {"page": page, "paginator": paginator}


def get_owned_by(doc):
    res = []
    if doc.parent is None:
        for i in doc.courier_set.all():
            res.append(str(i))
        return ", ".join(res) if res else ""
    else:
        return ""


def get_done_items(items):
    return [x for x in items if (get_in(x, ["done"], 0) == 1)]


def prepare_excel_response(request, documents):
    del_opt = {
        True: "Yes",
        False: "No",
        "N/A": "",
    }

    show_tracking_data = has_perm(request.user, "see_tracking_data")

    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer)

    header = [
        "ID",
        "Client ID",
        "Uploaded",
        "Signed",
        "City",
        "ZIP",
        "Status",
        "Author",
        "Owned by",
        "Deleted",
        "Size (KB)",
    ]
    if show_tracking_data:
        header.extend(["Wizard Start", "Wizard Close"])
    header.extend(
        [
            "Wizard Version",
            "Power of attorney",
            "Mandatory scans",
            "Optional scans",
            "Custom scans",
        ]
    )
    csv_writer.writerow(header)

    for _, _, _, query_set in batch_query_set(documents, batch_size=5000):
        for document in query_set:
            row = [
                document.id,
                document.client_id,
                document.get_parent_time(dt=True),
                document.get_signed_time(dt=True),
                document.get_city_name(),
                document.get_data("rest", "zip"),
                document.get_status().upper(),
                "" if document.author is None else document.author.name,
                get_owned_by(document),
                del_opt[document.deleted],
                round(document.zip_size / 1000, 2),
            ]
            if show_tracking_data:
                wizard_times = [
                    document.get_data("tracking", "wizard-start"),
                    document.get_data("tracking", "wizard-close"),
                ]
                row.extend(
                    [
                        format_time(int(time), True) if time else ""
                        for time in wizard_times
                    ]
                )
            scans_info = document.get_data("rest", "scans-info")
            row.extend(
                [
                    document.parent.version if document.parent else document.version,
                    del_opt[document.get_data("rest", "power-of-attorney")],
                    len(
                        get_done_items(
                            get_in(
                                document.get_data("rest", "scans-info"),
                                ["mandatory"],
                                [],
                            )
                        )
                    ),
                    len(get_done_items(get_in(scans_info, ["optional"], [])))
                    + len(get_done_items(get_in(scans_info, ["conditional"], []))),
                    len(get_done_items(get_in(scans_info, ["custom"], []))),
                ]
            )
            csv_writer.writerow(row)

    response = HttpResponse(
        content=ContentFile(csv_buffer.getvalue().encode("utf-8")),
        content_type="text/csv",
    )
    response["Content-Disposition"] = "attachment; filename=Report.csv"
    return response


def prepare_survey_records_excel_response(request, documents):
    accepted_options = {True: "Yes", False: "No"}

    courier_regions = {
        "10": "SPS Bratislava",
        "11": "SPS Dunajská Streda",
        "12": "SPS Komárno",
        "20": "SPS Košice",
        "21": "SPS Bardejov",
        "22": "SPS Humenné",
        "23": "SPS Michalovce",
        "24": "SPS Stará Ľubovňa",
        "25": "SPS Svidník",
        "26": "SPS Trebišov",
        "30": "SPS Žilina",
        "31": "SPS Trnava",
        "32": "SPS Trenčín",
        "33": "SPS Liptovský Mikuláš",
        "34": "SPS Martin",
        "35": "SPS Poprad",
        "37": "SPS Ružomberok",
        "38": "SPS Spišská Nová Ves",
        "40": "SPS Banská Bystrica",
        "41": "SPS Nitra",
        "42": "SPS Lučenec",
        "43": "SPS Prievidza",
        "44": "SPS Rožňava",
        "45": "SPS Senica",
        "46": "SPS Levice",
        "47": "SPS Bánovce n/B",
        "48": "SPS Topoľčany",
    }

    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer)

    header = [
        "Prewizard Start",
        "Wizard closed",
        "Author",
        "Courier ID",
        "Region",
        "GPS_start",
        "GPS_end",
        "Survey Accepted",
    ]
    csv_writer.writerow(header)

    for document in documents:
        row = [
            document.get_time_formatted(),
            get_ba_tz(
                datetime.fromtimestamp(document.data["timestamp"] / 1000)
            ).strftime(WEB_DATETIME_FORMAT),
            document.courier.user.username,
            document.courier.id,
            (
                courier_regions[document.courier.id[4:6]]
                if document.courier.id[4:6] in courier_regions
                else ""
            ),
            document.data["gps_start"],
            document.data["gps_end"] if "gps_end" in document.data else "",
            accepted_options[document.accepted],
        ]
        csv_writer.writerow(row)

    response = HttpResponse(
        content=ContentFile(csv_buffer.getvalue().encode("utf-8")),
        content_type="text/csv",
    )
    response["Content-Disposition"] = "attachment; filename=Report.csv"
    return response


def date_range(start, end):
    return [
        (start + timedelta(days=i)).strftime(EXCEL_DATE_FORMAT)
        for i in range(int((end - start).days) + 1)
    ]


def prepare_summary_excel(metrics: Metric, start: date, end: date) -> HttpResponse:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer)

    data: dict[str, dict[str, dict[str, int]]] = defaultdict(lambda: defaultdict(dict))
    for metric in metrics:
        data[metric.type][metric.customer.name][
            metric.date.strftime(EXCEL_DATE_FORMAT)
        ] = metric.value

    dates = date_range(start, end)
    for summary_type in SUMMARY_TYPES:
        header = [summary_type.value] + dates
        csv_writer.writerow(header)

        for customer, values in data[summary_type.value].items():
            row = [customer]
            for d in dates:
                if d not in values:
                    values[d] = 0
                row.append(str(values[d]))
            csv_writer.writerow(row)

        csv_writer.writerow([])

    response = HttpResponse(
        content=ContentFile(csv_buffer.getvalue().encode("utf-8")),
        content_type="text/csv",
    )
    response["Content-Disposition"] = "attachment; filename=Report.csv"
    return response


def prepare_couriers_export(couriers: QuerySet[Courier]) -> HttpResponse:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer)

    csv_writer.writerow(["Username", "Courier ID", "Signed documents", "Last login"])

    couriers = couriers.values(
        "id", "signed_documents", "user__username", "user__last_login"
    )

    for courier in couriers:
        csv_writer.writerow(
            [
                courier["user__username"],
                courier["id"],
                courier["signed_documents"],
                get_ba_tz(courier["user__last_login"]).strftime(EXCEL_DATE_FORMAT),
            ]
        )

    response = HttpResponse(
        content=ContentFile(csv_buffer.getvalue().encode("utf-8")),
        content_type="text/csv",
    )
    response["Content-Disposition"] = "attachment; filename=Report.csv"
    return response


def permission_check(allowed_logins):
    def login_decorator(func):
        def func_wrapper(self, request, *args, **kwargs):
            if request.user.is_authenticated:
                for it in allowed_logins:
                    if hasattr(request.user, it):
                        return func(self, request, *args, **kwargs)

            return json_response(
                {"error": "Not logged in as " + ", ".join(allowed_logins)}, status=401
            )

        return func_wrapper

    return login_decorator


def unauthorized(request, *args, **kwarg):
    return render_to_html("unauthorized.html", {"request": request})


def operator_owns(request, document):
    if hasattr(request.user, "operator"):
        return (
            document.customer == request.user.operator.customer
            or request.user.operator.customer is None
        )
    else:
        return True


def specific_login_required(allowed):
    def login_decorator(func):
        def func_wrapper(self, request, *args, **kwargs):
            if request.user.is_authenticated:
                for it in allowed:
                    if hasattr(request.user, it):
                        return func(self, request, *args, **kwargs)

                return unauthorized(request)
            else:
                return method_decorator(login_required)(func)(
                    self, request, *args, **kwargs
                )

        return func_wrapper

    return login_decorator


def get_couriers_data(request: HttpRequest, form):
    couriers = Courier.objects.prefetch_related(
        "user", "documents", "author_of"
    ).available()

    form.is_valid()

    if request.GET.get("action") == "Export":
        couriers = couriers.filter(active=True, user__last_login__isnull=False)

    signed = {}
    if hasattr(form, "cleaned_data"):
        cd = form.cleaned_data
        if cd["begin"]:
            begin = get_utc_tz(cd["begin"])
            signed["author_of__timestamp__gte"] = date_to_timestamp(begin)
        if cd["end"]:
            end = get_utc_tz(cd["end"])
            signed["author_of__timestamp__lte"] = date_to_timestamp(end)
        if cd["q"]:
            couriers = couriers.text_query(cd["q"])

    couriers = couriers.annotate(
        signed_documents=Count(
            "author_of",
            filter=Q(**signed),
            distinct=True,
        ),
        assigned_documents=Count("documents", distinct=True),
    )

    oder_by = request.GET.get("order_by", "")
    if oder_by:
        couriers = couriers.order_by(oder_by)

    return couriers


# 1, 0, -1
def done_to_class(done):
    if done == 1:
        return "glyphicon-ok"
    elif done == 0:
        return "glyphicon-remove"
    else:
        return ""


def get_signing_files(document):
    documents_info = get_in(document.rest, ["documents_info"], [])
    res = []
    for i in documents_info:
        res.append(
            {
                "title": get_in(i, ["title"], ""),
                "file": get_in(i, ["file"], ""),
                "required": (
                    "Required" if get_in(i, ["required"], False) else "Optional"
                ),
                "done": done_to_class(get_in(i, ["done"], -1)),
            }
        )

    return res


def get_scan_files(document):
    documents_info = get_in(document.rest, ["scans_info"], [])
    res = []
    data = []
    data.append(
        {"category": "Mandatory", "fields": get_in(documents_info, ["mandatory"], [])}
    )
    data.append(
        {
            "category": "Optional",
            "fields": get_in(documents_info, ["optional"], [])
            + get_in(documents_info, ["conditional"], []),
        }
    )
    data.append(
        {"category": "Custom", "fields": get_in(documents_info, ["custom"], [])}
    )

    for group in data:
        for i in group["fields"]:
            res.append(
                {
                    "title": get_in(i, ["text"], ""),
                    "description": get_in(i, ["description"], ""),
                    "status": group["category"],
                    "done": done_to_class(get_in(i, ["done"], -1)),
                }
            )

    return res


def create_survey_package(config):
    zip_file = InMemoryZip()
    zip_file.append_str("constants.js", config)
    for file in os.listdir(settings.SURVEY_ASSETS_DIR):
        zip_file.append_file(os.path.join(settings.SURVEY_ASSETS_DIR, file), file)

    package = File(zip_file.im_zip)
    package.name = "{}.zip".format(uuid.uuid4().hex)
    return package


def calculate_data(data: QuerySet) -> HttpResponse:
    return HttpResponse(content=data.count())


def get_bans_data(request, form):
    bans = Ban.objects.prefetch_related("user")

    form.is_valid()

    if hasattr(form, "cleaned_data"):
        cd = form.cleaned_data
        if cd["time"]:
            bans.filter(
                valid_from__lte=get_utc_tz(cd["time"]),
                valid_to__gte=get_utc_tz(cd["time"]),
            )
        if cd["q"]:
            bans = bans.search(cd["q"])

    oder_by = request.GET.get("order_by", "-valid_from")
    if oder_by:
        bans = bans.order_by(oder_by)

    return bans
