import json
from distutils.util import strtobool

from django import forms
from django.forms import ValidationError
from setty.models import SettySettings, TypeChoices

from axepto.constants import ACTION_CHOICES, FORM_DATETIME_FORMAT
from axepto.rest.library.upload_unsigned import validate_data
from axepto.rest.model.courier_center import CourierCenter
from axepto.rest.models import Courier, Customer
from axepto.schemas import get_schema


class CouriersForm(forms.Form):
    begin = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(attrs={"class": "form-control flatpickr-input"}),
        required=False,
    )
    end = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(attrs={"class": "form-control flatpickr-input"}),
        required=False,
    )
    q = forms.CharField(
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "autocomplete": "off",
                "placeholder": "Search...",
            }
        ),
        required=False,
        label="Search",
    )


class SummaryForm(forms.Form):
    begin = forms.DateField(
        input_formats=["%Y/%m/%d"],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        error_messages={"required": ""},
    )
    end = forms.DateField(
        input_formats=["%Y/%m/%d"],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        error_messages={"required": ""},
    )
    customer = forms.ChoiceField()

    def __init__(self, *args, **kwargs):
        cust = kwargs.pop("customer", True)
        super().__init__(*args, **kwargs)
        if cust:
            cust_options = [("", "All")] + [
                (c.id, c.name) for c in Customer.objects.order_by("name").all()
            ]
            customer = forms.ChoiceField(
                widget=forms.Select(attrs={"class": "form-control"}),
                choices=cust_options,
                required=False,
            )
            self.fields["customer"] = customer
        else:
            self.fields.pop("customer", None)


class DashboardForm(forms.Form):
    order_by = forms.CharField(widget=forms.HiddenInput(), required=False)
    begin = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "readonly": "readonly"}
        ),
        required=False,
    )
    end = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "readonly": "readonly"}
        ),
        required=False,
    )
    status = forms.ChoiceField(
        widget=forms.Select(attrs={"class": "form-control choices-single"}),
        required=False,
        choices=[
            ("all", "All"),
            ("unsigned", "Unsigned"),
            ("signed", "Signed"),
            ("assigned", "Assigned"),
        ],
    )
    courier = forms.ChoiceField()
    customer = forms.ChoiceField()
    q = forms.CharField(
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "autocomplete": "off",
                "placeholder": "Search Client ID or Axepto ID",
            }
        ),
        required=False,
        label="Search",
    )

    def __init__(self, cour: bool = True, cust: bool = True, *args, **kwargs):
        super(DashboardForm, self).__init__(*args, **kwargs)
        if cour:
            c_options = [("all", "All")] + [
                (c.user_id, c.name)
                for c in Courier.objects.order_by("user__username")
                .select_related("user")
                .all()
            ]
            courier = forms.ChoiceField(
                widget=forms.Select(attrs={"class": "form-control"}),
                choices=c_options,
                required=False,
            )
            self.fields["courier"] = courier
        else:
            self.fields.pop("courier", None)
        if cust:
            cust_options = [("", "All")] + [
                (c.id, c.name) for c in Customer.objects.order_by("name").all()
            ]
            customer = forms.ChoiceField(
                widget=forms.Select(attrs={"class": "form-control"}),
                choices=cust_options,
                required=False,
            )
            self.fields["customer"] = customer
        else:
            self.fields.pop("customer", None)


class AddCourierForm(forms.Form):
    username = forms.CharField(
        max_length=20, widget=forms.TextInput(attrs={"class": "form-control"})
    )

    id = forms.CharField(
        max_length=20, widget=forms.TextInput(attrs={"class": "form-control"})
    )

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"})
    )

    password_again = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"})
    )


class EditCourierForm(forms.Form):
    username = forms.CharField(
        max_length=20, widget=forms.TextInput(attrs={"class": "form-control"})
    )

    id = forms.CharField(
        max_length=20, widget=forms.TextInput(attrs={"class": "form-control"})
    )

    active = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={"class": "form-check-input d-block"}),
    )


class PasswordForm(forms.Form):
    new_password = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"}), required=False
    )

    password_again = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"}), required=False
    )


class HistoryForm(forms.Form):
    def __init__(self, *args, **kwarg):
        super().__init__(*args, **kwarg)
        c_options = [("all", "All")] + [
            (c.user_id, c.name)
            for c in Courier.objects.select_related("user")
            .order_by("user__username")
            .all()
        ]
        courier = forms.ChoiceField(
            widget=forms.Select(attrs={"class": "form-control"}),
            choices=c_options,
            required=False,
        )
        self.fields["courier"] = courier

    begin = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        required=False,
    )
    end = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(
            attrs={"class": "form-control flatpickr-input", "autocomplete": "off"}
        ),
        required=False,
    )
    courier = forms.ChoiceField()
    t_options = [("all", "All")] + ACTION_CHOICES
    type = forms.ChoiceField(
        widget=forms.Select(attrs={"class": "form-control"}),
        required=False,
        choices=t_options,
    )
    a_options = [("all", "All"), ("no", "No"), ("yes", "Yes")]
    auto = forms.ChoiceField(
        widget=forms.Select(attrs={"class": "form-control"}),
        choices=a_options,
        required=False,
    )


class SurveyAddForm(forms.Form):
    subject = forms.CharField(
        widget=forms.TextInput(attrs={"class": "form-control"}),
        required=True,
    )

    description = forms.CharField(
        widget=forms.Textarea(attrs={"class": "form-control"}),
        required=True,
    )

    courier_centers = forms.ModelMultipleChoiceField(
        widget=forms.SelectMultiple(attrs={"class": "form-control choices-multiple"}),
        queryset=CourierCenter.objects.order_by("name").all(),
        required=False,
    )

    couriers = forms.ModelMultipleChoiceField(
        widget=forms.SelectMultiple(attrs={"class": "form-control choices-multiple"}),
        queryset=Courier.objects.select_related("user")
        .order_by("user__username")
        .all(),
        required=False,
    )

    form_url = forms.CharField(
        widget=forms.TextInput(attrs={"class": "form-control"}),
        required=True,
    )

    repeating = forms.IntegerField(
        widget=forms.NumberInput(attrs={"class": "form-control", "min": 0}),
        required=False,
    )

    active = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={"class": "form-check-input d-block"}),
        required=False,
    )


class SurveyEditForm(forms.Form):
    subject = forms.CharField(
        widget=forms.TextInput(attrs={"class": "form-control", "readonly": True}),
        required=True,
    )

    description = forms.CharField(
        widget=forms.Textarea(attrs={"class": "form-control", "readonly": True}),
        required=True,
    )

    courier_centers = forms.ModelMultipleChoiceField(
        widget=forms.SelectMultiple(attrs={"class": "form-control choices-multiple"}),
        queryset=CourierCenter.objects.order_by("name").all(),
        required=False,
    )

    couriers = forms.ModelMultipleChoiceField(
        widget=forms.SelectMultiple(attrs={"class": "form-control choices-multiple"}),
        queryset=Courier.objects.select_related("user")
        .order_by("user__username")
        .all(),
        required=False,
    )

    form_url = forms.CharField(
        widget=forms.TextInput(attrs={"class": "form-control", "readonly": True}),
        required=True,
    )

    repeating = forms.IntegerField(
        widget=forms.NumberInput(attrs={"class": "form-control", "min": 0}),
        required=False,
    )

    active = forms.BooleanField(
        widget=forms.CheckboxInput(attrs={"class": "form-check-input d-block"}),
        required=False,
    )


class SettingsForm(forms.ModelForm):
    BOOL_CHOICES = (
        (0, "False"),
        (1, "True"),
    )

    SERIALIZERS = {
        TypeChoices.BOOL: lambda x: strtobool(x),
        TypeChoices.DICT: lambda x: json.loads(x),
        TypeChoices.FLOAT: lambda x: float(x),
        TypeChoices.INTEGER: lambda x: int(x),
        TypeChoices.LIST: lambda x: json.loads(
            json.dumps(x.strip().replace("\r", "").split("\n"))
        ),
        TypeChoices.STRING: lambda x: str(x),
    }

    FIELDS = {
        TypeChoices.BOOL: lambda: forms.ChoiceField(
            widget=forms.Select(attrs={"class": "form-control"}),
            choices=SettingsForm.BOOL_CHOICES,
            label="Value",
        ),
        TypeChoices.DICT: lambda: forms.CharField(widget=forms.Textarea, label="Value"),
        TypeChoices.FLOAT: lambda: forms.FloatField(label="Value"),
        TypeChoices.INTEGER: lambda: forms.IntegerField(label="Value"),
        TypeChoices.LIST: lambda: forms.CharField(widget=forms.Textarea, label="Value"),
        TypeChoices.STRING: lambda: forms.CharField(),
    }

    def __init__(self, *args, instance=None, **kwargs):
        kwargs.pop("initial", None)
        initial_data = {"value_unpacked": getattr(instance, "value_unpacked", None)}
        super().__init__(*args, initial=initial_data, instance=instance, **kwargs)

        self.fields["value_unpacked"] = self.FIELDS[instance.type]()

        if instance.type == TypeChoices.DICT:
            self.initial["value_unpacked"] = json.dumps(instance.value, indent=2)
        elif instance.type == TypeChoices.LIST:
            self.initial["value_unpacked"] = "\n".join(instance.value)

    value_unpacked = forms.CharField(
        label="Value",
        help_text="The value to store. "
        "List and Dict data types should be defined as JSON strings.",
    )

    def clean_value_unpacked(self):
        serializer = self.SERIALIZERS[self.instance.type]
        try:
            serialized_value = serializer(
                str(self.cleaned_data["value_unpacked"]).replace("'", '"')
            )

            if self.instance.type == TypeChoices.DICT:
                validate_data(serialized_value, get_schema(self.instance.name))

        except Exception as e:
            raise ValidationError("An error occurred storing the value: {}".format(e))

        return serialized_value

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.value = self.cleaned_data["value_unpacked"]
        instance.save()

        return instance

    class Meta:
        model = SettySettings
        fields = ["value_unpacked"]


class QuarantineForm(forms.Form):
    show = forms.ChoiceField(
        widget=forms.Select(attrs={"class": "form-control"}),
        required=False,
        choices=[
            ("all", "All"),
            ("active", "Active"),
            ("deleted", "Deleted"),
        ],
    )


class BansForm(forms.Form):
    time = forms.DateTimeField(
        input_formats=[FORM_DATETIME_FORMAT],
        widget=forms.TextInput(attrs={"class": "form-control flatpickr-input"}),
        required=False,
    )
    q = forms.CharField(
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "autocomplete": "off",
                "placeholder": "Search by username or ip address",
            }
        ),
        required=False,
        label="Search",
    )
