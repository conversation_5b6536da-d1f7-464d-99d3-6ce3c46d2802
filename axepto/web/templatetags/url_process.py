from urllib.parse import urlencode

from django import template
from django.conf import settings
from django.urls import reverse

register = template.Library()


@register.simple_tag
def query_create(request, **kwargs):
    return "%s?%s" % (request.path, urlencode(kwargs))


@register.simple_tag
def query_transform(request, **kwargs):
    updated = request.GET.copy()
    for i, v in kwargs.items():
        updated[i] = v
    return "%s?%s" % (request.path, updated.urlencode())


@register.simple_tag
def sorting_link(request, sortby):
    to_sortby = sortby
    if sortby:
        desc = "-" + sortby
        if request.GET.get("order_by", "") != desc:
            to_sortby = desc

    return query_transform(request, page=1, order_by=to_sortby)


@register.simple_tag
def sorting_class(request, sortby):
    current = request.GET.get("order_by", "uploaded")
    if not current:
        return ""
    if current == "-" + sortby:
        return "sorting_desc"
    elif current == sortby:
        return "sorting_asc"
    else:
        return ""


@register.simple_tag
def summary_tag(page, paginator):
    return "Showing {first} to {stop} of {approximate_tag}{total} entries".format(
        first=page.start_index(),
        stop=page.end_index(),
        total=paginator.count,
        approximate_tag=(
            "" if getattr(paginator, "was_count_exact", True) else "approximately "
        ),
    )


@register.simple_tag
def url_query(page, **kwargs):
    data = {}
    for i, v in kwargs.items():
        data[i] = v
    return "%s?%s" % (reverse(page), urlencode(data))


@register.simple_tag
def courier_link(shipper_id, client_id):
    return settings.COURIER_DOCUMENTS_URL.format(
        shipper_id=shipper_id, client_id=client_id
    )
