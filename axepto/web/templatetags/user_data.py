from django import template

from axepto.constants import permissions_groups

register = template.Library()


@register.simple_tag
def is_logged_in(request):
    return hasattr(request, "user")


@register.simple_tag
def get_user_name(request):
    return request.user.username


@register.simple_tag
def get_user_perms(request):
    res = []
    for item in permissions_groups:
        if hasattr(request.user, item):
            res.append(item.capitalize())

    return ", ".join(res)
