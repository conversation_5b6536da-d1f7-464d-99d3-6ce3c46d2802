from collections import defaultdict
from datetime import date, timed<PERSON>ta
from typing import Type

from django.contrib.auth import authenticate
from django.contrib.auth import login as auth_login
from django.contrib.auth import logout as auth_logout
from django.contrib.auth.models import User
from django.db.models import Sum
from django.forms import Form
from django.http import HttpRequest, HttpResponse
from django.shortcuts import get_object_or_404, redirect
from django.views.generic import DeleteView, ListView, TemplateView, UpdateView, View
from setty.backend import CacheBackend
from setty.models import SettySettings
from workalendar.europe import Slovakia

from axepto.constants import SUMMARY_TYPES, WEB_DATE_FORMAT, web_permissions
from axepto.fail2ban import can_user_login, is_banned, unban
from axepto.helpers import json_response, set_cookie_validity
from axepto.rest.forms import UserForm
from axepto.rest.models import Courier, Document, Metric, Survey
from axepto.web.helpers import calculate_data, get_bans_data, prepare_couriers_export
from axepto.web.menu import get_menu

from ..archive.models import DocumentActionsArchive, DocumentArchive
from ..rest.helpers import create_courier
from ..rest.model.courier_center import CourierCenter
from ..rest.model.model_history import History
from .forms import (
    AddCourierForm,
    BansForm,
    CouriersForm,
    DashboardForm,
    EditCourierForm,
    HistoryForm,
    PasswordForm,
    QuarantineForm,
    SettingsForm,
    SummaryForm,
    SurveyAddForm,
    SurveyEditForm,
)
from .helpers import (
    get_clean_docs,
    get_couriers_data,
    get_dashboard_data,
    get_document_history,
    get_history_data,
    get_logs_data,
    get_quarantine_data,
    get_scan_files,
    get_signing_files,
    get_survey_records_data,
    get_surveys_data,
    has_perm,
    operator_owns,
    paginate,
    prepare_dashboard_params,
    prepare_excel_response,
    prepare_summary_excel,
    prepare_survey_records_excel_response,
    specific_login_required,
    unauthorized,
)
from .menu import render_to_html


class AboutAppView(TemplateView):
    template_name = "about.html"


def render_dashboard(
    request: HttpRequest,
    model: Type[Document] | Type[DocumentArchive] = Document,
    archive: bool = False,
    title: str = "Parcel list",
    link: str = "document_info",
) -> HttpResponse:
    request.GET = prepare_dashboard_params(request.GET.copy())
    display_customer_select = has_perm(request.user, "see_documents_of_all_customers")
    display_courier_select = has_perm(request.user, "see_all_customer_documents")
    form = DashboardForm(display_courier_select, display_customer_select, request.GET)

    data = get_dashboard_data(request, form, model=model, archive=archive)

    # excel export
    if request.GET.get("action") == "Export":
        return prepare_excel_response(request, data)

    # calculate total
    if request.GET.get("action") == "Calculate":
        return calculate_data(data)

    data = paginate(request, data)

    open_more_filters = False
    for dashboard_filter in ("begin", "end", "status", "courier", "customer"):
        open_more_filters = (
            open_more_filters or request.GET.get(dashboard_filter) is not None
        )

    return render_to_html(
        "dashboard.html",
        {
            "request": request,
            "page": data["page"],
            "paginator": data["paginator"],
            "documents": get_clean_docs(data["page"]),
            "form": form,
            "auto_calculate": request.GET.get("prefilter") is not None,
            "open_more_filters": open_more_filters,
            "archive": archive,
            "title": title,
            "link": link,
        },
    )


class DashboardView(View):
    @specific_login_required(web_permissions["dashboard"])
    def get(self, request):
        return render_dashboard(request)


def render_document_info(
    request: HttpRequest,
    pk: str,
    model: Type[Document] | Type[DocumentArchive] = Document,
    history_model: Type[History] | Type[DocumentActionsArchive] = History,
    archive: bool = False,
    link: str = "document_info",
):
    document = model.objects.with_id(pk).first()
    if not document:
        return json_response({"error": "Document %s does not exist" % pk}, 400)
    if not operator_owns(request, document):
        return unauthorized(request)
    if document.is_unsigned():
        history_coll = document.children
        unsigned_to_add = document
    else:
        history_coll = document.parent.children
        unsigned_to_add = document.parent
    # history_coll now contains the full version history of
    # this chain of documents, excluding the initial unsigned document
    history = list(history_coll.order_by("-timestamp"))
    history.append(unsigned_to_add)
    show_tracking_data = has_perm(request.user, "see_tracking_data")
    logs = get_document_history(request, document, history_model)
    files = get_signing_files(document)
    scans = get_scan_files(document)
    data = paginate(request, logs)

    return render_to_html(
        "document_info.html",
        {
            "request": request,
            "document": document,
            "history": history,
            "show_files": len(files) != 0,
            "files": files,
            "show_scans": len(scans) != 0,
            "scans": scans,
            "show_tracking_data": show_tracking_data,
            "logs": data["page"],
            "page": data["page"],
            "paginator": data["paginator"],
            "archive": archive,
            "link": link,
        },
    )


class DocumentInfoView(View):
    @specific_login_required(web_permissions["document_info"])
    def get(self, request, pk):
        return render_document_info(request, pk)


class CourierInfoView(TemplateView):
    @specific_login_required(web_permissions["courier_info"])
    def dispatch(self, request, pk):
        try:
            cour_view = False
            cust_view = True
            form = DashboardForm(cour_view, cust_view, request.GET)
            courier = Courier.objects.get(user_id=pk)
            data = get_dashboard_data(request, form, pk)
            data = paginate(request, data)
            return render_to_html(
                "courier_info.html",
                {
                    "courier": courier,
                    "request": request,
                    "page": data["page"],
                    "paginator": data["paginator"],
                    "documents": get_clean_docs(data["page"]),
                    "form": form,
                },
            )
        except Courier.DoesNotExist:
            return json_response({"error": "Courier %s does not exist" % pk}, 400)


class WebLoginView(View):
    def get(self, request):
        return render_to_html("auth.html", {"request": request, "form": UserForm()})

    def post(self, request):
        if ("username" in request.POST) and ("password" in request.POST):
            username = request.POST.get("username")
            password = request.POST.get("password")

            response = is_banned(request, username)
            if response:
                return response

            user = authenticate(username=username, password=password)
            if can_user_login(request, username, user):
                auth_login(request, user)
                set_cookie_validity(request, "web")
                return redirect(request.GET.get("next", "dashboard"))
            else:
                return json_response({"response": "authentication failed"}, 403)
        else:
            return json_response({"error": "some parameters are missing"}, 400)


def process_summary_values(values):
    """Applies predifined filters and sets default values when not specified"""
    default_values = {k: v for k, v in values.items()}
    default_values["open_more_filters"] = True
    today = date.today()

    if "prefilter" in default_values:
        if default_values["prefilter"] == "last_work_day":
            last_work_day = today
            work_cal = Slovakia()
            delta_day = timedelta(days=1)
            while True:
                last_work_day = last_work_day - delta_day
                if work_cal.is_working_day(last_work_day):
                    break

            default_values["begin"] = last_work_day.strftime(WEB_DATE_FORMAT)
            default_values["end"] = last_work_day.strftime(WEB_DATE_FORMAT)

        elif default_values["prefilter"] == "this_week":
            default_values["begin"] = (
                today - timedelta(days=today.weekday())
            ).strftime(WEB_DATE_FORMAT)
            default_values["end"] = today.strftime(WEB_DATE_FORMAT)

        elif default_values["prefilter"] == "last_weekend":
            default_values["begin"] = (
                today - timedelta(days=today.weekday() + 2)
            ).strftime(WEB_DATE_FORMAT)
            default_values["end"] = (
                today - timedelta(days=today.weekday() + 1)
            ).strftime(WEB_DATE_FORMAT)

        elif default_values["prefilter"] == "last_week":
            default_values["begin"] = (
                today - timedelta(days=today.weekday() + 7)
            ).strftime(WEB_DATE_FORMAT)
            default_values["end"] = (
                today - timedelta(days=today.weekday() + 1)
            ).strftime(WEB_DATE_FORMAT)

        elif default_values["prefilter"] == "this_month":
            default_values["begin"] = today.replace(day=1).strftime(WEB_DATE_FORMAT)
            default_values["end"] = today.strftime(WEB_DATE_FORMAT)

        elif default_values["prefilter"] == "last_month":
            last_month = today.replace(day=1) - timedelta(days=1)
            default_values["begin"] = last_month.replace(day=1).strftime(
                WEB_DATE_FORMAT
            )
            default_values["end"] = last_month.strftime(WEB_DATE_FORMAT)

    if "begin" not in default_values:
        default_values["begin"] = today.replace(day=1).strftime(WEB_DATE_FORMAT)
        default_values["open_more_filters"] = False
    if "end" not in default_values:
        default_values["end"] = today.strftime(WEB_DATE_FORMAT)
        default_values["open_more_filters"] = False

    return default_values


def handle_summary(
    request: HttpRequest, form: Form, open_more_filters: bool
) -> HttpResponse:
    metrics = Metric.objects.all()
    # operator
    if hasattr(request.user, "operator"):
        metrics = metrics.filter(customer=request.user.operator.customer)
    else:
        metrics = metrics.filter(customer__active=True)
    # if we have valid form, filter by it
    form.is_valid()
    if hasattr(form, "cleaned_data"):
        cd = form.cleaned_data
        metrics = metrics.filter(date__gte=cd["begin"])
        metrics = metrics.filter(date__lte=cd["end"])
        if "customer" in cd and cd["customer"]:
            metrics = metrics.filter(customer=cd["customer"])

    if request.GET.get("action") == "Export":
        return prepare_summary_excel(metrics, cd["begin"], cd["end"])

    metrics = metrics.values("customer__name", "type").annotate(sum=Sum("value"))

    data: defaultdict[str, defaultdict[str, int]] = defaultdict(
        lambda: defaultdict(int)
    )
    total: dict[str, int] = defaultdict(int)
    for metric in metrics:
        data[metric["customer__name"]][metric["type"]] = metric["sum"]
        total[metric["type"]] += metric["sum"]

    # calculate how many packages were assigned but not signed
    BALANCED = "BALANCED"
    total[BALANCED] = (
        total[SUMMARY_TYPES.assign.value] - total[SUMMARY_TYPES.signed.value]
    )
    for customer in data:
        data[customer][BALANCED] = (
            data[customer][SUMMARY_TYPES.assign.value]
            - data[customer][SUMMARY_TYPES.signed.value]
        )

    # allow django templates to iterate through defaultdict
    data.default_factory = None

    header = [summary_type.value for summary_type in SUMMARY_TYPES] + [BALANCED]

    return render_to_html(
        "summary.html",
        {
            "form": form,
            "request": request,
            "header": header,
            "data": data,
            "total": total,
            "open_more_filters": open_more_filters,
        },
    )


class WebSummaryView(View):
    @specific_login_required(web_permissions["summary"])
    def post(self, request):
        request.POST = process_summary_values(request.POST)
        is_operator = hasattr(request.user, "operator")
        return handle_summary(
            request,
            SummaryForm(request.POST, customer=(not is_operator)),
            request.POST["open_more_filters"],
        )

    @specific_login_required(web_permissions["summary"])
    def get(self, request):
        request.GET = process_summary_values(request.GET)
        is_operator = hasattr(request.user, "operator")
        return handle_summary(
            request,
            SummaryForm(request.GET, customer=(not is_operator)),
            request.GET["open_more_filters"],
        )


class WebCouriersView(View):
    def handle(self, request: HttpRequest, form: CouriersForm):
        data = get_couriers_data(request, form)

        if request.GET.get("action") == "Export":
            return prepare_couriers_export(data)

        data = paginate(request, data)
        return render_to_html(
            "couriers.html",
            {
                "request": request,
                "form": form,
                "page": data["page"],
                "paginator": data["paginator"],
                "couriers": data["page"],
            },
        )

    @specific_login_required(web_permissions["couriers"])
    def get(self, request: HttpRequest):
        return self.handle(request, CouriersForm(request.GET))


class WebCourierAddView(View):
    @specific_login_required(web_permissions["courier_add"])
    def get(self, request):
        form = AddCourierForm()
        return render_to_html("add_courier.html", {"request": request, "form": form})

    @specific_login_required(web_permissions["courier_add"])
    def post(self, request):
        form = AddCourierForm(request.POST)
        error = []  # list containing error messages

        form.is_valid()
        if User.objects.filter(username=form.cleaned_data["username"]).count() != 0:
            error.append(
                "User with username %s already exists." % form.cleaned_data["username"]
            )
        if Courier.objects.filter(id=form.cleaned_data["id"]).count() != 0:
            error.append("Courier with id %s already exists." % form.cleaned_data["id"])
        if form.cleaned_data["password"] != form.cleaned_data["password_again"]:
            error.append("Passwords do not match.")

        if len(error) == 0:
            create_courier(
                form.cleaned_data["username"],
                form.cleaned_data["password"],
                form.cleaned_data["id"],
            )

            return redirect("couriers")

        return render_to_html(
            "add_courier.html", {"request": request, "form": form, "error": error}
        )


class WebCourierEditView(View):
    @specific_login_required(web_permissions["courier_edit"])
    def get(self, request, id):
        cour = Courier.objects.filter(user_id=id)[0]
        form = EditCourierForm(
            initial={
                "username": cour.user.username,
                "id": cour.id,
                "active": cour.active,
            }
        )

        pform = PasswordForm()

        return render_to_html(
            "edit_courier.html",
            {"request": request, "form": form, "password_form": pform, "id": id},
        )

    @specific_login_required(web_permissions["courier_edit"])
    def post(self, request, id):
        cour = Courier.objects.filter(user_id=id)[0]
        error = []  # list containing error messages
        form = EditCourierForm(request.POST)
        form.is_valid()
        fd = form.cleaned_data
        # check for conflicts
        if User.objects.exclude(id=id).filter(username=fd.get("username")).count() != 0:
            error.append("User with username %s already exists." % fd.get("username"))
        if Courier.objects.exclude(user_id=id).filter(id=fd.get("id")).count() != 0:
            error.append("Courier with id %s already exists." % fd.get("id"))

        pform = PasswordForm(request.POST)
        pform.is_valid()
        pfd = pform.cleaned_data
        if "new_password" in pfd:
            if pfd.get("new_password") != pfd.get("password_again"):
                error.append("Passwords do not match.")

        if len(error) == 0:
            cour.user.username = fd.get("username")
            cour.id = fd.get("id", "")
            cour.active = True if request.POST.get("active") else False
            if "new_password" in pfd:
                cour.user.set_password(pfd.get("new_password"))
            cour.save(update_fields=["id", "active"])
            cour.assign_center()
            cour.user.save(update_fields=["username", "password"])
            return redirect("couriers")
        else:
            return render_to_html(
                "edit_courier.html",
                {
                    "request": request,
                    "form": form,
                    "password_form": PasswordForm(),
                    "error": error,
                    "id": id,
                },
            )


class WebLogoutView(View):
    def get(self, request):
        auth_logout(request)
        return redirect("dashboard")


class WebQuarantineView(View):
    def handle_view(self, request):
        data = get_quarantine_data(request)
        paginator = paginate(request, data)

        return render_to_html(
            "quarantine.html",
            {
                "form": QuarantineForm(request.GET),
                "request": request,
                "page": paginator["page"],
                "paginator": paginator["paginator"],
                "documents": paginator["page"],
            },
        )

    @specific_login_required(web_permissions["quarantine"])
    def get(self, request):
        return self.handle_view(request)


class WebCourierDeleteView(View):
    @specific_login_required(web_permissions["courier_delete"])
    def post(self, request, id):
        cour = Courier.objects.get(user_id=id)

        cour.deleted = True
        cour.save(update_fields=["deleted"])

        return redirect("couriers")


class WebHistoryView(View):
    def handle_view(self, request, form):
        data = get_history_data(request, form)
        data = paginate(request, data)

        return render_to_html(
            "history.html",
            {
                "form": form,
                "request": request,
                "page": data["page"],
                "paginator": data["paginator"],
                "documents": data["page"],
            },
        )

    @specific_login_required(web_permissions["history"])
    def get(self, request):
        return self.handle_view(request, HistoryForm(request.GET))


class WebLogsView(View):
    def handle_view(self, request):
        data = get_logs_data(request)
        data = paginate(request, data)

        return render_to_html(
            "logs.html",
            {
                "request": request,
                "page": data["page"],
                "paginator": data["paginator"],
                "documents": data["page"],
            },
        )

    @specific_login_required(web_permissions["logs"])
    def get(self, request):
        return self.handle_view(request)


class WebSurveysView(View):
    def handle_view(self, request):
        data = get_surveys_data(request)
        data = paginate(request, data)

        return render_to_html(
            "surveys.html",
            {
                "request": request,
                "page": data["page"],
                "paginator": data["paginator"],
                "documents": data["page"],
            },
        )

    @specific_login_required(web_permissions["surveys"])
    def get(self, request):
        return self.handle_view(request)


class WebSurveyRecordsView(View):
    def handle_view(self, request, id):
        sur = Survey.objects.filter(id=id).first()
        if sur is None:
            return json_response({"error", "Cannot find survey"}, 404)
        data = get_survey_records_data(request, id)
        if request.GET.get("action") == "Export":
            return prepare_survey_records_excel_response(request, data)
        data = paginate(request, data)

        return render_to_html(
            "survey_records.html",
            {
                "request": request,
                "survey": sur,
                "page": data["page"],
                "paginator": data["paginator"],
                "documents": data["page"],
            },
        )

    @specific_login_required(web_permissions["surveys"])
    def get(self, request, id):
        return self.handle_view(request, id)


class WebSurveyAddView(View):
    @specific_login_required(web_permissions["survey_add"])
    def get(self, request):
        form = SurveyAddForm()
        return render_to_html("survey_add.html", {"request": request, "form": form})

    @specific_login_required(web_permissions["survey_add"])
    def post(self, request):
        form = SurveyAddForm(request.POST)
        form.is_valid()
        form_data = form.cleaned_data

        survey = Survey(
            subject=form_data["subject"],
            description=form_data["description"],
            form_url=form_data["form_url"],
            repeating=form_data["repeating"],
            active=form_data["active"],
            courier_centers=CourierCenter.objects.filter(
                center_id__in=form_data["courier_centers"]
            ).all(),
            couriers=Courier.objects.filter(user_id__in=form_data["couriers"]).all(),
        )
        survey.save()

        return redirect("surveys_dashboard")


class WebSurveyEditView(View):
    @specific_login_required(web_permissions["survey_edit"])
    def get(self, request, pk):
        survey = Survey.objects.filter(pk=pk)[0]
        form = SurveyEditForm(
            initial={
                "instance": True,
                "subject": survey.subject,
                "description": survey.description,
                "courier_centers": [c.center_id for c in survey.courier_centers.all()],
                "couriers": [c.user_id for c in survey.couriers.all()],
                "form_url": survey.form_url,
                "repeating": survey.repeating,
                "active": survey.active,
            }
        )

        return render_to_html(
            "survey_edit.html",
            {"request": request, "form": form, "pk": pk},
        )

    @specific_login_required(web_permissions["survey_edit"])
    def post(self, request, pk):
        survey = Survey.objects.filter(pk=pk)[0]
        form = SurveyEditForm(request.POST)
        form.is_valid()
        form_data = form.cleaned_data

        survey.courier_centers.set(
            CourierCenter.objects.filter(
                center_id__in=form_data["courier_centers"]
            ).all()
        )
        survey.couriers.set(
            Courier.objects.filter(user_id__in=form_data["couriers"]).all()
        )
        survey.repeating = form_data["repeating"]
        survey.active = form_data["active"]
        survey.save(
            update_fields=["courier_centers", "couriers", "repeating", "active"]
        )
        return redirect("surveys_dashboard")


class WebSurveyDeleteView(DeleteView):
    model = Survey
    pk_url_kwarg = "pk"
    success_url = "/web/surveys/"

    @specific_login_required(web_permissions["survey_delete"])
    def post(self, request, *args, **kwargs):
        model = self.get_object()
        model.package.delete()
        return super(WebSurveyDeleteView, self).post(request, *args, **kwargs)


class PostSurveyView(TemplateView):
    template_name = "post_survey_page.html"

    def get(self, request, *args, **kwargs):
        return self.render_to_response({}, status=201)


class SettingsView(ListView):
    template_name = "settings.html"
    model = SettySettings

    def get_queryset(self):
        return super(SettingsView, self).get_queryset().order_by("name")

    def get_context_data(self, **kwargs):
        context = super(SettingsView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    @specific_login_required(web_permissions["settings"])
    def get(self, request, *args, **kwargs):
        return super(SettingsView, self).get(request, *args, **kwargs)


class SettingsUpdateView(UpdateView):
    template_name = "settings_update.html"
    form_class = SettingsForm
    success_url = "/web/settings/"

    def get_context_data(self, **kwargs):
        context = super(SettingsUpdateView, self).get_context_data(**kwargs)
        context["menu_"] = get_menu(self.request)
        return context

    def get_object(self, queryset=None):
        return get_object_or_404(SettySettings, name=self.kwargs.get("name"))

    def form_valid(self, form):
        result = super(SettingsUpdateView, self).form_valid(form)
        instance = form.save(commit=False)
        CacheBackend().set_in_cache(instance.name, instance.value)
        return result

    @specific_login_required(web_permissions["settings"])
    def get(self, request, *args, **kwargs):
        return super(SettingsUpdateView, self).get(request, *args, **kwargs)

    @specific_login_required(web_permissions["settings"])
    def post(self, request, *args, **kwargs):
        return super(SettingsUpdateView, self).post(request, *args, **kwargs)


class WebBansView(View):
    def handle_view(self, request, form):
        data = get_bans_data(request, form)

        data = paginate(request, data)
        return render_to_html(
            "bans.html",
            {
                "request": request,
                "form": form,
                "page": data["page"],
                "paginator": data["paginator"],
                "bans": data["page"],
            },
        )

    @specific_login_required(web_permissions["bans"])
    def get(self, request):
        form = BansForm(request.GET)
        return self.handle_view(request, form)


class UnbanVies(View):
    @specific_login_required(web_permissions["bans"])
    def dispatch(self, request, id):
        unban(id)

        return redirect("bans")
