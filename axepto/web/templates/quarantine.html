{% extends 'base.html' %}
{% load url_process %}

{% block nav-quar %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<form method="get">
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <h3>
                    <strong>Quarantine list</strong>
                </h3>
            </div>
            <div class="row mb-3">
                <div class="card py-3">
                    <div class="px-3">
                        <div class="row m-0">
                            {% for field in form %}
                                <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                    {{ field.errors }}
                                    <label class="col-2 col-sm-1 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    <div class="col-10 col-sm-11 p-0">
                                        {{ field }}
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="col-12 col-sm-1 d-flex align-items-center py-2 py-sm-0">
                                <input type='submit' name='action' value='Show' class="btn btn-primary w-100"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div>
                    <div class="card overflow-auto">
                        <table class="table table-striped dataTable w-100">
                            <thead>
                                <tr>
                                    {% include "th_sorting.html" with link='quarantine_dashboard' request=request order_by="" name="ID"%}
                                    {% include "th_sorting.html" with link='quarantine_dashboard' request=request order_by="timestamp" name="Upload date"%}
                                    {% include "th_sorting.html" with link='quarantine_dashboard' request=request order_by="courier__user__username" name="Courier"%}
                                    {% include "th_sorting.html" with link='quarantine_dashboard' request=request order_by="zip_size" name="Size"%}
                                    {% include "th_sorting.html" with link='quarantine_dashboard' request=request order_by="customer__name" name="Customer"%}
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in documents %}
                                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                        <td class="fw-bold text-truncate">{{ document.url_id }}</td>
                                        <td class="text-truncate">{{ document.get_date }}</td>
                                        <td class="text-truncate">
                                            <a href="{% url 'courier_info' document.courier.user.id %}">{{ document.courier.user.username }}</a>
                                        </td>
                                        <td class="text-truncate">{{ document.get_display_size }}</td>
                                        <td class="text-truncate">{{ document.customer.name }}</td>
                                        <td  class="text-truncate">
                                            {% if not document.deleted %}
                                                <a href="{% url 'quarantine' document.id %}" class="btn btn-primary text-truncate w-100">Download</a>
                                            {% endif %}
                                        </td>
                                        <td  class="text-truncate">
                                            {% if not document.deleted %}
                                                <button type="button" data-bs-toggle="modal" data-bs-target="#modal" data-bs-href="{% url 'quarantine_delete' id=document.id %}" class="btn btn-danger text-truncate w-100">Remove</button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% include "pagination.html" with paginator=paginator page=page %}
        </div>
    </main>
</form>
<div class="modal fade" id="modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="modalLabel">Delete quarantined document</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this document from quarantine?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a class="btn btn-danger" id="confirmation">Delete</a>
            </div>
        </div>
    </div>
</div>

<script>
document.querySelector("#modal").addEventListener("show.bs.modal", ({relatedTarget}) => {
  const confirmation = document.querySelector("#confirmation")
  confirmation.href = relatedTarget.getAttribute("data-bs-href")
})
</script>

{% endblock %}
