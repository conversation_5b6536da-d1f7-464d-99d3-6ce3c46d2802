{% extends 'base.html' %}
{% load url_process %}

{% block nav-cours %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<form method="get">
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <h3>
                    <strong>Courier history</strong>
                </h3>
            </div>
            <div class="row mb-3">
                <div class="card py-3">
                    <div class="px-3">
                        <div class="row m-0">
                            {% for field in form %}
                                <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                    {{ field.errors }}
                                    <label class="col-3 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    <div class="col-9 p-0">
                                        {{ field }}
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="col-12 col-sm-1">
                                <input type='submit' name='action' value='Show' class="btn btn-primary w-100"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% include "history_table.html" with page='history' logs=documents %}
            {% include "pagination.html" with paginator=paginator page=page %}
        </div>
    </main>
</form>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true,
        defaultHour: 0,
    })
})
</script>
{% endblock %}