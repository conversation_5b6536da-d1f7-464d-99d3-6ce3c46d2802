{% extends 'base.html' %}
{% load url_process %}

{% block nav-surveys %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="card py-3 px-4">
            <div class="row">
                <div class="mb-3">
                    <h3>
                        <strong>Survey name:</strong>
                        <span>{{ survey.subject }}</span>
                    </h3>
                    <div>
                        <strong>Survey created: </strong>
                        <span>{{ survey.get_date_created }}</span>
                    </div>
                </div>
            </div>
            <div class="row overflow-auto">
                <table class="table table-striped dataTable w-100">
                    <thead>
                        <tr>
                            {% include "th_sorting.html" with link='' request=request order_by="" name="Prewizard Start"%}
                            {% include "th_sorting.html" with th_class="text-center" link='' request=request order_by="" name="Author"%}
                            {% include "th_sorting.html" with th_class="text-center" link='' request=request order_by="" name="Survey Accepted"%}
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in documents %}
                            <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                <td class="text-truncate">{{ document.get_time_formatted }}</td>
                                <td class="text-truncate text-center">{{ document.courier.user.username }}</td>
                                <td class="text-truncate text-center">{{ document.accepted }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include "pagination.html" with paginator=paginator page=page %}
        </div>
    </div>
</main>
{% endblock %}
