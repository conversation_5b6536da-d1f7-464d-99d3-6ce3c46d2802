{% extends 'base.html' %}
{% load url_process %}
{% load filter_helpers %}

{% block nav-bans %}active{% endblock %}

{% block content %}
<form method="get">
    <nav class="navbar navbar-expand">
        <a class="sidebar-toggle js-sidebar-toggle">
            <i class="hamburger align-self-center"></i>
        </a>
        <div class="input-group input-group-navbar">
            {% for field in form %}
                {% if field.label == "Search" %}
                    {{ field }}
                {% endif %}
            {% endfor %}
            <button class="btn" type="submit">
                <i class="fas fa-magnifying-glass text-lg"></i>
            </button>
        </div>
    </nav>
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <h3>
                    <strong>Ban list</strong>
                </h3>
            </div>
            <div class="row mb-3">
                <div>
                    <div class="card py-3">
                        <div class="px-3">
                            <div class="row m-0">
                                {% for field in form %}
                                    {% if field.label != "Search" and not field.is_hidden %}
                                        <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                            {{ field.errors }}
                                            <label class="col-2 col-sm-1 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                            <div class="col-10 col-sm-11 p-0">
                                                {{ field }}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <div class="col-12 col-sm-1 d-flex align-items-center py-2 py-sm-0">
                                    <input type='submit' name='action' value='Show' class="btn btn-primary w-100"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div>
                    <div class="card overflow-auto">
                        <table class="table table-striped dataTable w-100">
                            <thead>
                                <tr>
                                    {% include "th_sorting.html" with request=request order_by="user__username" name="Username"%}
                                    {% include "th_sorting.html" with request=request order_by="ip" name="IP"%}
                                    {% include "th_sorting.html" with request=request order_by="valid_from" name="Valid from"%}
                                    {% include "th_sorting.html" with request=request order_by="valid_to" name="Valid to"%}
                                    {% include "th_sorting.html" with request=request order_by="active" name="Active" th_class="text-center"%}
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ban in bans %}
                                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                        <td class="text-truncate">{{ ban.user.username }}</td>
                                        <td class="text-truncate">{{ ban.ip|default_if_none:"" }}</td>
                                        <td class="text-truncate">{{ ban.banned_from }}</td>
                                        <td class="text-truncate">{{ ban.banned_to }} </td>
                                        <td class="text-truncate text-center">
                                            <i class="{% if ban.active %}fas fa-check text-success{% else %}fas fa-x text-danger{% endif %}"/>
                                        </td>
                                        <td>
                                            {% if ban.active %}
                                                <a href="{% url 'unban' id=ban.id %}" class="btn btn-primary text-truncate w-100">Unban</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% include "pagination.html" with paginator=paginator page=page %}
        </div>
    </main>
</form>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true,
        defaultHour: 0,
    })
})
</script>
{% endblock %}
