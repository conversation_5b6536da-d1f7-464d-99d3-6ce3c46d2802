{% extends 'base.html' %}

{% block nav-cours %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="container-md vw-100 py-4">
    <div class="container-fluid p-0">
        <div class="row">
            <h3><strong>Add courier</strong></h3>
        </div>
        <div class="row">
            <div class="card p-3">
                {% if error != None %}
                    <div class="mb-3">
                        {% for error_message in error %}
                            <div class="badge bg-danger-dark text-danger text-lg my-1 w-100 text-start">
                                {{error_message}}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                <form method="post" autocomplete="off">
                    {% for field in form %}
                        <div class="mb-3">
                            <label class="mb-2" for="{{ field.id_for_label }}">{{ field.label }}</label>
                            {{ field }}
                            {{ field.errors }}
                        </div>
                    {% endfor %}
                    <div class="d-flex justify-content-center">
                        <input type='submit' value='Add' class="btn btn-success"/>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>
{% endblock %}