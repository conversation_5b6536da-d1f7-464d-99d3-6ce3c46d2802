{% extends 'base.html' %}
{% load url_process %}

{% block nav-surveys %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3><strong>Survey list</strong></h3>
        </div>
        <div class="row mt-3">
            <div>
                <div class="card overflow-auto">
                    <table class="table table-striped dataTable w-100">
                        <thead>
                            <tr>
                                {% include "th_sorting.html" with th_class="" link='surveys_dashboard' request=request order_by="" name="Name"%}
                                {% include "th_sorting.html" with th_class="text-center" link='surveys_dashboard' request=request order_by="" name="Active"%}
                                {% include "th_sorting.html" with th_class="text-center" link='surveys_dashboard' request=request order_by="" name="Couriers"%}
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for document in documents %}
                                <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                    <td class="text-truncate"><a href="{% url 'survey_records' document.id %}">{{ document.subject }}</a></td>
                                    <td class="text-truncate text-center">
                                        <i class="fas{% if document.active %} fa-check text-success{% else %} fa-x text-danger{% endif %}"/>
                                    </td>
                                    <td class="text-truncate text-center">{{ document.count_couriers }}</td>
                                    <td>
                                        <a href="{% url 'survey_edit' document.id %}" class="btn btn-primary text-truncate w-100">Edit</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% include "pagination.html" with paginator=paginator page=page %}
        <div class="row mt-3">
            <div>
                <a href="{% url 'survey_add' %}" class="btn btn-success float-end">
                    Add survey
                </a>
            </div>
        </div>
    </div>
</main>
{% endblock %}
