{% extends 'base.html' %}
{% load url_process %}

{% block nav-cours %}active{% endblock %}

{% block content %}
<form method="get">
    <nav class="navbar navbar-expand">
        <a class="sidebar-toggle js-sidebar-toggle">
            <i class="hamburger align-self-center"></i>
        </a>
        <div class="input-group input-group-navbar">
            {% for field in form %}
                {% if field.label == "Search" %}
                    {{ field }}
                {% endif %}
            {% endfor %}
            <button class="btn" type="submit">
                <i class="fas fa-magnifying-glass text-lg"></i>
            </button>
        </div>
    </nav>
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <div>
                    <div class="card py-3">
                        <div class="row px-4">
                            <div class="mb-3">
                                <h2>
                                    <strong>Name:</strong>
                                    <span>{{ courier.name }}</span>
                                </h2>
                                <h3>
                                    <strong>User ID:</strong>
                                    <span>{{ courier.user_id }}</span>
                                </h3>
                            </div>
                            <div class="col-sm-6">
                                <div>
                                    <strong>Created at:</strong>
                                    <span>{{ courier.get_date_created }}</span>
                                </div>
                                <div>
                                    <strong>Last login:</strong>
                                    <span>{{ courier.get_last_login }}</span>
                                </div>
                                <div>
                                    <a href="{% url_query 'history' courier=courier.user_id %}" class="btn btn-primary">Show history</a>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div>
                                    <strong>Active: </strong>
                                    <span {% if not courier.active %}class="text-danger"{% endif %}>{{ courier.active }}</span>
                                </div>
                                <div>
                                    <strong>Deleted: </strong>
                                    <span {% if courier.deleted %}class="text-danger"{% endif %}>{{ courier.deleted }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row my-3">
                <div>
                    <div class="card py-3">
                        <div class="px-3">
                            <div class="row m-0">
                                {% for field in form %}
                                    {% if field.label != "Search" and not field.is_hidden %}
                                        <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                            {{ field.errors }}
                                            <label class="col-3 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                            <div class="col-9 p-0">
                                                {{ field }}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <div class="col-12 col-sm-2 d-flex align-items-center py-2 py-sm-0">
                                    <input type='submit' name='action' value='Show' class="btn btn-primary w-50 me-1"/>
                                    <input type='submit' name='action' value='Export' class="btn btn-primary w-50 ms-1"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% include "document_table.html" with page='dashboard' documents=documents link='document_info' %}
            {% include "pagination.html" with paginator=paginator page=page %}
        </div>
    </main>
</form>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true,
        defaultHour: 0,
    })
})
</script>
{% endblock %}