{% load url_process %}

<div class="row">
    <div>
        <div class="card overflow-auto">
            <table class="table table-striped dataTable w-100">
                <thead>
                    <tr>
                        {% include "th_sorting.html" with request=request order_by="client_id" name="Client ID"%}
                        {% include "th_sorting.html" with request=request order_by="" name="Status"%}
                        {% include "th_sorting.html" with request=request order_by="uploaded" name="Uploaded" th_class="d-none d-sm-table-cell"%}
                        {% include "th_sorting.html" with request=request order_by="signed" name="Signed" th_class="d-none d-sm-table-cell"%}
                        {% include "th_sorting.html" with request=request order_by="city" name="City" th_class="d-none d-sm-table-cell"%}
                        {% include "th_sorting.html" with request=request order_by="authorn" name="Author" th_class="d-none d-sm-table-cell"%}
                        {% include "th_sorting.html" with request=request order_by="" name="Owned by"%}
                        {% include "th_sorting.html" with request=request order_by="deleted" name="Deleted"%}
                        {% include "th_sorting.html" with request=request order_by="zip_size" name="Size" th_class="d-none d-sm-table-cell"%}
                        {% include "th_sorting.html" with request=request order_by="customern" name="Customer"%}
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents %}
                        <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                            <td class="fw-bold text-truncate">
                                <span title="{{ document.client_id }}">
                                    {% if document.shipper_id %}<a href="{% courier_link document.shipper_id document.client_id %}" target="_blank">{% endif %}
                                        {{ document.client_id }}
                                    {% if document.shipper_id %}</a>{% endif %}
                                </span>
                            </td>
                            <td>
                                {% if document.get_status == "signed" %}
                                    <span class="badge bg-success-{% if request.COOKIES.theme == "dark" %}dark{% else %}light{% endif %} text-success">Signed</span>
                                {% else %}
                                    <span class="badge bg-warning-{% if request.COOKIES.theme == "dark" %}dark{% else %}light{% endif %} text-warning">Unsigned</span>
                                {% endif %}
                            </td>
                            <td class="text-truncate d-none d-sm-table-cell">
                                <span title="{{ document.uploaded }}">
                                    {{ document.uploaded }}
                                </span>
                            </td>
                            <td class="text-truncate d-none d-sm-table-cell">
                                <span title="{{ document.signed }}">
                                    {{ document.signed }}
                                </span>
                            </td>
                            <td class="text-truncate d-none d-sm-table-cell">
                                <span title="{{ document.get_city_name }}">
                                    {{ document.get_city_name }}
                                </span>
                            </td>
                            <td class="text-truncate d-none d-sm-table-cell">
                                {% if document.author %}
                                    <a href="{% url 'courier_info' document.author.user_id %}">{{ document.author }}</a>
                                {% else %}
                                    Operator
                                {% endif %}
                            </td>
                            <td class="text-truncate">
                                {% if document.courier_set.all %}
                                    {% for courier in document.courier_set.all %}
                                        <a href="{% url 'courier_info' courier.user_id %}">{{ courier }}</a>,
                                    {% endfor %}
                                {% else %}
                                    Nobody
                                {% endif %}
                            </td>
                            <td>
                                {% if document.deleted %}
                                    <span class="badge bg-danger-{% if request.COOKIES.theme == "dark" %}dark{% else %}light{% endif %} text-danger">Deleted</span>
                                {% endif %}
                            </td>
                            <td class="text-truncate d-none d-sm-table-cell">
                                <span title="{{ document.get_display_size }}">
                                    {{ document.get_display_size }}
                                </span>
                            </td>
                            <td class="text-truncate">
                                <span title="{{ document.customern }}">
                                    {{ document.customern }}
                                </span>
                            </td>
                            <td><a href="{% url link document.id %}" class="btn btn-primary text-truncate w-100">Details</a></td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>