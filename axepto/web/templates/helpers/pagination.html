{% load url_process %}

<div class="row mt-3">
    <div class="col-sm-6" id="summary">
        {{ paginator.summarize }}{% if paginator.has_next and calculate_total %}, <a id="calculate" class>calculate total</a>{% endif %}
    </div>
    <div class="col-sm-6 d-flex justify-content-end align-items-center gap-2">
        {% if paginator.num_pages > 1 %}
            <select id="page_size" class="form-control choices-single w-auto" name="page_size">
                <option value="10"{% if paginator.page_size == 10 %} selected{% endif %}>10</option>
                <option value="25"{% if paginator.page_size == 25 %} selected{% endif %}>25</option>
                <option value="50"{% if paginator.page_size == 50 %} selected{% endif %}>50</option>
            </select>
            <ul class="pagination m-0">
                {% if paginator.has_previous %}
                    <li class="page-item"><a class="page-link" href="{% query_transform request page=paginator.previous_page_number %}">Previous</a></li>
                {% endif %}

                <li class="active page-item">
                    <a class="page-link" href="{% query_transform request page=paginator.page %}">{{paginator.page}}</a>
                </li>

                {% if paginator.has_next %}
                    <li class="page-item"><a class="page-link" href="{% query_transform request page=paginator.next_page_number %}">Next</a></li>
                {% endif %}
            </ul>
        {% endif %}
    </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    document.querySelector("#page_size").addEventListener("change", ({target}) => {
      window.location.replace("{% query_transform request page=1 page_size=paginator.page_size %}".replaceAll("&amp;", "&").replace("page_size={{ paginator.page_size }}", `page_size=${target.value}`))
    })

    {% if calculate_total %}
    const summary = document.querySelector("#summary")
    const displayCount = (response) => {
      summary.innerHTML = `${"{{ paginator.summarize }}".split("entries")[0]} of ${response} entries`
    }
    const calculateTotal = () => {
      summary.innerHTML = `${"{{ paginator.summarize }}".split("entries")[0]} of ... entries`
      fetch("{% query_transform request action="Calculate" %}".replaceAll("&amp;", "&")).then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        return response.text()
      }).then(displayCount)
    }
    {% if paginator.has_next %}
    document.querySelector("#calculate").addEventListener("click", calculateTotal)
    {% if auto_calculate %}
    calculateTotal()
    {% endif %}
    {% else %}
    displayCount("{{ paginator.last_showing }}")
    {% endif %}
    {% endif %}
  })
</script>