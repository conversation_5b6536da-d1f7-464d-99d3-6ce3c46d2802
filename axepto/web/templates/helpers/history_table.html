{% load url_process %}
<div>
    <div class="card overflow-auto">
        <table class="table table-striped dataTable w-100 pt-2">
            <thead>
                <tr>
                    {% include "th_sorting.html" with request=request order_by="action" name="Status history"%}
                    {% include "th_sorting.html" with request=request order_by="" name="Axepto ID"%}
                    {% include "th_sorting.html" with request=request order_by="created_at" name="Date"%}
                    {% include "th_sorting.html" with request=request order_by="" name="Author"%}
                    {% include "th_sorting.html" with request=request order_by="" name="User"%}
                    {% include "th_sorting.html" with request=request order_by="auto" name="Manual"%}
                </tr>
            </thead>
            <tbody>
                {% for log in logs %}
                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                        <td class="fw-bold{% if log.get_action == "DELETE" %} text-danger{% elif log.get_action == "SIGNED REMOTELY" %} text-success{% elif log.get_action == "DOWNLOAD" %} text-info{% elif log.get_action == "ASSIGN" %} text-warning{% endif %}">{{ log.get_action }}</td>
                        <td>{{ log.document.id }}</td>
                        <td>{{ log.get_date_created }}</td>
                        <td>{{ log.author.username }}</td>
                        <td>{{ log.user.username }}</td>
                        <td>{{ log.auto }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>