{% extends 'base.html' %}
{% load qr_code %}
{% load url_process %}

{% block nav-dash %}{% if not archive %}active{% endif %}{% endblock %}
{% block nav-arch %}{% if archive %}active{% endif %}{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <ul class="nav">
                {% for doc in history %}
                    <li class="nav-item">
                        <a href="{% url link doc.id %}" class="nav-link rounded-top {% if doc.get_status|upper == "SIGNED" %} text-success {% if doc.id == document.id %}border-start border-success bg-success-{% endif %}{% else %} text-warning {% if doc.id == document.id %}border-start border-warning bg-warning-{% endif %}{% endif %}{% if request.COOKIES.theme == "dark" %}dark{% else %}light{% endif %}">{{doc.get_status|upper}}</a>
                    </li>
                {% endfor %}
            </ul>
            <div class="card py-3 rounded-0 rounded-bottom rounded-end">
                <div class="row px-4">
                    <div class="mb-2">
                        <h3>
                            <strong>Client ID:</strong>&nbsp;
                            {% if document.customer.shipper_id %}<a href="{% courier_link document.customer.shipper_id document.client_id %}" target="_blank">{% endif %}
                                {{ document.client_id }}
                            {% if document.customer.shipper_id %}</a>{% endif %}
                            <button class="btn clipboard" data-clipboard-text="{{ document.client_id }}">
                                <i class="fas fa-clipboard"></i>
                            </button>
                        </h3>
                        <strong>Axepto ID:</strong> {{ document.id }}
                        <button class="btn clipboard" data-clipboard-text="{{ document.id }}">
                            <i class="fas fa-clipboard"></i>
                        </button>
                        <br>
                    </div>
                    {% if not archive %}
                    <div class="mb-3">
                        <a class="text-dark text-decoration-underline" href="{{ document.get_download_link }}">{{ document.get_download_link }}</a>
                        <button class="btn clipboard" data-clipboard-text="{{ document.get_download_link }}">
                            <i class="fas fa-clipboard"></i>
                        </button>
                    </div>
                    {% endif %}
                    <div class="col-sm-6">
                        {% if document.get_status|upper == "UNSIGNED" and not archive %}
                            <div class="d-inline-block mb-3" style="background-color: white">
                                {% qr_from_text document.get_rep_id size=8 image_format='png' %}
                            </div>
                        {% endif %}
                        <div><strong>Uploaded: </strong>{{ document.get_time_formatted }}</div>
                        <div><strong>Expires: </strong>{{ document.get_expiration_formatted }}</div>
                        <div><strong>Owner: </strong> {% if document.courier_set.all %}
                            {% for courier in document.courier_set.all %}
                                <a class="text-dark text-decoration-underline" href="{% url 'courier_info' courier.user_id %}">{{ courier }}</a>,
                            {% endfor %}
                            {% else %}
                                Nobody
                            {% endif %}
                        </div>
                        <div>
                            <strong>Author: </strong>
                            {% if document.author %}
                                <a class="text-dark text-decoration-underline" href="{% url 'courier_info' document.author.user_id %}">{{ document.author }}</a>
                            {% else %}
                                Operator
                            {% endif %}
                        </div>
                        <div>
                            <strong>Deleted: </strong>
                            {% if document.deleted %}
                                <span class="text-danger">{{ document.deleted }}</span>
                            {% else %}
                                {{ document.deleted }}
                            {% endif %}
                        </div>
                        <div>
                            <strong>Signed locally: </strong>
                            {% if document.signed_locally %}
                                <span class="text-danger">{{ document.signed_locally }}</span>
                            {% else %}
                                {{ document.signed_locally }}
                            {% endif %}
                        </div>
                        <div>
                            <strong>Signed remotely: </strong>
                            {% if document.signed_remotely %}
                                <span class="text-danger">{{ document.signed_remotely }}</span>
                            {% else %}
                                {{ document.signed_remotely }}
                            {% endif %}
                        </div>
                        {% if document.is_error %}
                            <div>
                                <strong>Error message: </strong>
                                <span class="text-danger">{{ document.get_error_code_msg }}</span>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-sm-6">
                        <div>
                            <strong>Wizard Version: </strong>
                            <span>{{ document.version }}</span>
                        </div>
                        <div>
                            <strong>Zip size: </strong>
                            <span>{{ document.get_display_size }}</span>
                        </div>
                        <div>
                            <strong>Customer: </strong>
                            <span>{{ document.customer }}</span>
                        </div>

                        {% for row in document.get_parsed_metadata %}
                            <div>
                                <strong>{{ row.key }}:</strong>
                                <span>{{ row.value }}</span>
                            </div>
                        {% endfor %}
                        {% if show_tracking_data %}
                            {% for row in document.get_tracking_data %}
                                <div>
                                    <strong>{{ row.key }}:</strong>
                                    <span>{{ row.value }}</span>
                                </div>
                            {% endfor %}
                        {% endif %}
                        {% if show_files %}
                            <div>
                                <h4>Documents:</h4>
                                {% for row in files %}
                                    <div>
                                        <strong>{{ row.title }}</strong> | {{ row.required }} | {{ row.file }}
                                        {% if row.done %}
                                            | <i class="fas fa-check"></i>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if show_scans %}
                            <div>
                                <h4>Scans:</h4>
                                {% for row in scans %}
                                    <div>
                                        <strong>{{ row.title }}</strong>
                                        {% if row.description %}
                                            | {{ row.description }}
                                        {% endif %}
                                            | {{ row.status }}
                                        {% if row.done %}
                                            | <i class="fas fa-check"></i>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    {% include "history_table.html" with logs=logs %}
                    {% include "pagination.html" with paginator=paginator page=page %}
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener("DOMContentLoaded", () => {
  new ClipboardJS(".clipboard")
})
</script>
{% endblock %}
