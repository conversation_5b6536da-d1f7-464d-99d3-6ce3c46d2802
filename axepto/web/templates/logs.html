{% extends 'base.html' %}
{% load url_process %}

{% block nav-logs %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3><strong>Log list</strong></h3>
        </div>
    </div>
    <div class="row">
        <div>
            <div class="card overflow-auto">
                <table class="table table-striped dataTable w-100">
                    <thead>
                        <tr>
                            {% include "th_sorting.html" with link='logs_dashboard' request=request order_by="file_name" name="File name"%}
                            {% include "th_sorting.html" with link='logs_dashboard' request=request order_by="courier__user__username" name="Courier"%}
                            {% include "th_sorting.html" with link='logs_dashboard' request=request order_by="created_at" name="Upload date"%}
                            {% include "th_sorting.html" with link='logs_dashboard' request=request order_by="zip_size" name="Size"%}
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in documents %}
                            <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                <td class="text-truncate">{{ document.file_name }}</td>
                                <td class="text-truncate">
                                    <a href="{% url 'courier_info' document.courier.user_id %}">{{ document.courier }}</a>
                                </td>
                                <td class="text-truncate">{{ document.get_date_created }}</td>
                                <td class="text-truncate">{{ document.get_display_size }}</td>
                                <td>
                                    <a href="{% url 'logs' document.id %}" class="btn btn-primary text-truncate w-100">Download</a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% include "pagination.html" with paginator=paginator page=page %}
</main>
{% endblock %}