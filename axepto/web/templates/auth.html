{% extends 'base.html' %}
{% load static %}

{% block navigation %}{% endblock %}

{% block content %}
	<div class="card container-md position-absolute top-50 start-50 translate-middle rounded-3 p-0">
        <div class="d-flex justify-content-center align-items-center py-3 border-bottom border-success">
            <img src="{% static 'logo_dashboard_h.svg' %}" class="w-100 auth-logo" style="max-width: 350px">
            <script>
                const authLogo = document.querySelector(".auth-logo")
                const img = localStorage.getItem("adminkit_config_theme") === "dark" ? "{% static 'logo_dashboard_h.svg' %}" : "{% static 'logo_dashboard_h_light.svg' %}"
                authLogo.setAttribute("src", img)
            </script>
        </div>
        <form method="post" class="p-5">
            {% for field in form %}
                <div class="mb-3">
                    <label class="mb-2" for="{{ field.id_for_label }}">{{ field.label }}</label>
                    {{ field }}
                    {{ field.errors }}
                </div>
            {% endfor %}
            <div class="d-flex justify-content-center mt-5">
                <button type="submit" class="btn btn-primary">Sign in</button>
            </div>
        </form>
    </div>
{% endblock %}
