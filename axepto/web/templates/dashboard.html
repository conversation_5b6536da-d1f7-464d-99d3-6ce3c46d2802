{% extends 'base.html' %}
{% load url_process %}
{% load filter_helpers %}

{% block nav-dash %}{% if not archive %}active{% endif %}{% endblock %}
{% block nav-arch %}{% if archive %}active{% endif %}{% endblock %}

{% block content %}
<form method="get">
    <nav class="navbar navbar-expand">
        <a class="sidebar-toggle js-sidebar-toggle">
            <i class="hamburger align-self-center"></i>
        </a>
        <div class="input-group input-group-navbar">
            {% for field in form %}
                {% if field.label == "Search" %}
                    {{ field }}
                {% endif %}
            {% endfor %}
            <button class="btn" type="submit">
                <i class="fas fa-magnifying-glass text-lg"></i>
            </button>
        </div>
    </nav>
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <h3>
                    <strong>{{ title }}</strong>
                </h3>
                {% if not archive %}<strong>Predefined filters</strong>{% endif %}
            </div>
            <div class="row mb-3">
                <div>
                    <div class="card py-3 m-0">
                        {% if not archive %}
                            <div class="d-flex justify-content-between flex-wrap px-4 gy-2">
                                <a href="{% query_create request prefilter='assigned' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'assigned' %}">
                                    Assigned - Today
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_today' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_today' %}">
                                    Signed - Today
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_last_work_day' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_last_work_day' %}">
                                    Signed - Last Work Day
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_this_week' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_this_week' %}">
                                    Signed - This Week
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_last_weekend' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_last_weekend' %}">
                                    Signed - Last Weekend
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_last_week' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_last_week' %}">
                                    Signed - Last Week
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_this_month' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_this_month' %}">
                                    Signed - This Month
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='signed_last_month' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'signed_last_month' %}">
                                    Signed - Last Month
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='unsigned_today' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'unsigned_today' %}">
                                    Unsigned - Today
                                </a>
                                <span class="opacity-50">|</span>
                                <a href="{% query_create request prefilter='unsigned_last_work_day' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'unsigned_last_work_day' %}">
                                    Unsigned - Last Work Day
                                </a>
                            </div>
                            <div class="d-flex align-items-end justify-content-end pt-2 px-3">
                                <button class="btn border-0 dropdown-toggle text-sm{% if not open_more_filters %} collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#more-options">
                                    MORE FILTERS
                                </button>
                            </div>
                        {% endif %}
                        <div id="more-options" class="collapse row px-3 {% if not archive %}pt-3 {% endif %}m-0{% if open_more_filters or archive %} show{% endif %}">
                            {% for field in form %}
                                {% if field.label != "Search" and not field.is_hidden %}
                                    <div class="col-12 col-sm-6 col-xl-4 col-xxl d-flex align-items-center p-2 py-xxl-0 row m-0">
                                        {{ field.errors }}
                                        <label class="col-4 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                        <div class="col-8 p-0">
                                            {{ field }}
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                            <div class="col-12 col-sm-6 col-xl-4 col-xxl-2 d-flex align-items-center p-2 ps-xxl-0 py-sm-0">
                                <input type='submit' name='action' value='Show' class="btn btn-primary me-1 w-50"/>
                                <input type='submit' name='action' value='Export' class="btn btn-primary ms-1 w-50"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% include "document_table.html" with page='dashboard' documents=documents request=request link=link %}
            {% include "pagination.html" with paginator=paginator page=page calculate_total=True auto_calculate=auto_calculate %}
        </div>
    </main>
</form>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true,
        defaultHour: 0,
    })
})
</script>
{% endblock %}
