{% extends 'base.html' %}

{% block nav-settings %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3>
                <strong>Settings</strong>
            </h3>
        </div>
        <div class="row">
            <div>
                <div class="card overflow-auto">
                    <table class="table table-striped dataTable w-100">
                        <thead>
                            <tr>
                                {% include "th_sorting.html" with request=request name="Name"%}
                                {% include "th_sorting.html" with request=request name="Value"%}
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for obj in object_list %}
                                <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                    <td class="fw-bold">{{ obj.name }}</td>
                                    <td>{{ obj.value }}</td>
                                    <td>
                                        <a href="{% url 'settings_update' name=obj.name %}" class="btn btn-primary text-truncate w-100">Change</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</main>

{% endblock %}