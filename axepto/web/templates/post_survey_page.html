<!DOCTYPE html>
<html>
<head>
    <title>End Of Survey Page</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script type="text/javascript">

        getQueryParam = (paramName) => {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has(paramName))
                return urlParams.get(paramName);
            return false;
        };

        window.checkInSurvey = () => {
            const metadata = {
                action: 'check-in',
                courier_name: getQueryParam('courier_name'),
                timestamp: Math.floor(Date.now()),
                survey_id: getQueryParam('survey_id'),
            };

            $.ajax({
                url: decodeURIComponent(getQueryParam('target_server')) + '/survey/',
                type: 'post',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(metadata),
                success: function (d, s) {
                    console.log(d);
                    console.log(s);
                }
            })
        };

        $(document).ready(window.checkInSurvey)
    </script>
    <style type="text/css">.body {
        font-size: 110%;
        margin: 30px;
        height: 100%;
    }

    .message-holder {
        clear: both;
        float: left;
        height: 400px;
        width: 100%;
    }

    .message {
        border-style: solid;
        border-width: 1px;
        width: 700px;
        margin: 10px auto;
        padding: 30px;
        text-align: center;
        font-size: 110%;
    }

    .message .title {
        font-size: 120%;
        margin-bottom: 30px;
    }

    .message .description {
        font-weight: bold;
        line-height: 1.6;
    }
    </style>
</head>

<body style="">
<div id="app">
    <div class="body">
        <div class="message-holder">
            <div class="message">
                <div class="description">Vaša odpoveď bola zaznamenaná,<br>Prieskum zavriete šípkou späť.</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
