{% extends 'base.html' %}

{% block nav-cours %}active{% endblock %}

{% block content %}
<form method="get">
    <nav class="navbar navbar-expand">
        <a class="sidebar-toggle js-sidebar-toggle">
            <i class="hamburger align-self-center"></i>
        </a>
        <div class="input-group input-group-navbar">
            {% for field in form %}
                {% if field.label == "Search" %}
                    {{ field }}
                {% endif %}
            {% endfor %}
            <button class="btn" type="submit">
                <i class="fas fa-magnifying-glass text-lg"></i>
            </button>
        </div>
    </nav>
    <main class="content">
        <div class="container-fluid p-0">
            <div class="row">
                <h3>
                    <strong>Couriers list</strong>
                </h3>
            </div>
            <div class="row mb-3">
                <div>
                    <div class="card py-3">
                        <div class="px-3">
                            <div class="row m-0">
                                {% for field in form %}
                                    {% if field.label != "Search" and not field.is_hidden %}
                                        <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                            {{ field.errors }}
                                            <label class="col-2 col-xxl-1 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                            <div class="col-10 col-xxl-11 p-0">
                                                {{ field }}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <div class="col-12 col-sm-1 d-flex align-items-center py-2 py-sm-0">
                                    <input type='submit' name='action' value='Show' class="btn btn-primary w-50"/>
                                    <input type='submit' name='action' value='Export' class="btn btn-primary ms-1 w-50"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div>
                    <div class="card overflow-auto">
                        <table class="table table-striped dataTable w-100">
                            <thead>
                                <tr>
                                    {% include "th_sorting.html" with request=request width="15.5%" order_by="user__username" name="Username"%}
                                    {% include "th_sorting.html" with th_class="text-center" request=request order_by="id" name="ID"%}
                                    {% include "th_sorting.html" with th_class="text-center" request=request order_by="assigned_documents" name="Assigned documents"%}
                                    {% include "th_sorting.html" with th_class="text-center" request=request order_by="signed_documents" name="Signed documents"%}
                                    {% include "th_sorting.html" with th_class="text-center" request=request name="Last login"%}
                                    {% include "th_sorting.html" with th_class="text-center" request=request order_by="active" name="Active"%}
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cour in couriers %}
                                    <tr class="{% if forloop.counter|divisibleby:2 %}even{% else %}odd{% endif %}">
                                        <td class="text-truncate">
                                            <a href="{% url 'courier_info' cour.user_id %}">
                                                {{ cour.user.username }}
                                            </a>
                                        </td>
                                        <td class="text-center text-truncate">{{ cour.id }}</td>
                                        <td class="text-center text-truncate">{{ cour.assigned_documents }}</td>
                                        <td class="text-center text-truncate">{{ cour.signed_documents }}</td>
                                        <td class="text-center text-truncate">{{ cour.get_last_login }} </td>
                                        <td class="text-center text-truncate">
                                            <i class="{% if cour.active %}fas fa-check text-success{% else %}fas fa-x text-danger{% endif %}"/>
                                        </td>
                                        <td>
                                            <a href="{% url 'courier_edit' cour.user_id %}" class="btn btn-primary text-truncate w-100">Edit</a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% include "pagination.html" with paginator=paginator page=page %}
            <div class="row mt-3">
                <div>
                    <a href="{% url 'courier_add' %}" class="btn btn-success float-end">
                        Add courier
                    </a>
                </div>
            </div>
        </div>
    </main>
</form>

<script type="text/javascript">
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        enableTime: true,
        dateFormat: "Y/m/d H:i:S",
        time_24hr: true,
        minuteIncrement: 1,
        enableSeconds: true,
        defaultHour: 0,
    })
})
</script>

{% endblock %}