{% extends 'base.html' %}
{% load object_helpers %}
{% load static %}
{% load url_process %}
{% load filter_helpers %}

{% block nav-summ %}active{% endblock %}

{% block content %}
<nav class="navbar navbar-expand">
    <a class="sidebar-toggle js-sidebar-toggle">
        <i class="hamburger align-self-center"></i>
    </a>
</nav>
<main class="content">
    <div class="container-fluid p-0">
        <div class="row">
            <h3>
                <strong>Summary</strong>
            </h3>
            <strong>Predefined filters</strong>
        </div>
        <div class="row mb-3">
            <form method="get">
                <div class="card py-3">
                    <div class="px-3">
                        <div class="d-flex justify-content-between flex-wrap px-4 gy-2">
                            <a href="{% query_create request prefilter='last_work_day' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'last_work_day' %}">
                                Last Work Day
                            </a>
                            <span class="opacity-50">|</span>
                            <a href="{% query_create request prefilter='this_week' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'this_week' %}">
                                This Week
                            </a>
                            <span class="opacity-50">|</span>
                            <a href="{% query_create request prefilter='last_weekend' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'last_weekend' %}">
                                Last Weekend
                            </a>
                            <span class="opacity-50">|</span>
                            <a href="{% query_create request prefilter='last_week' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'last_week' %}">
                                Last Week
                            </a>
                            <span class="opacity-50">|</span>
                            <a href="{% query_create request prefilter='this_month' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'this_month' %}">
                                This Month
                            </a>
                            <span class="opacity-50">|</span>
                            <a href="{% query_create request prefilter='last_month' %}" class="mb-1 mb-xxl-0 {% prefilter_class request 'last_month' %}">
                                Last Month
                            </a>
                        </div>
                        <div class="d-flex align-items-end justify-content-end pt-2 px-3">
                            <button class="btn border-0 dropdown-toggle text-sm{% if not open_more_filters %} collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#more-options">
                                MORE FILTERS
                            </button>
                        </div>
                        <div id="more-options" class="collapse row px-3 pt-3 m-0{% if open_more_filters %} show{% endif %}">
                            {% for field in form %}
                                <div class="col-12 col-sm d-flex align-items-center p-2 py-sm-0 row m-0">
                                    {{ field.errors }}
                                    <label class="col-3 p-0" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    <div class="col-9 p-0">
                                        {{ field }}
                                    </div>
                                </div>
                            {% endfor %}
                            <div class="col-12 col-sm-2 d-flex align-items-center py-2 py-sm-0">
                                <input type='submit' name='action' value='Show' class="btn btn-primary me-1 w-50"/>
                                <input type='submit' name='action' value='Export' class="btn btn-primary ms-1 w-50"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="row">
            <div>
                <div class="card overflow-auto">
                    <table class="table table-striped dataTable w-100" id="summary">
                        <thead>
                            <tr>
                                <th class="text-center">Customer</th>
                                {% for column in header %}
                                    <th class="text-center">{{ column }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer, values in data.items %}
                                <tr class="{% if forloop.counter|divisibleby:2 %}odd{% else %}even{% endif %}">
                                    <td>{{ customer }}</td>
                                    {% for column in header %}
                                        <td class="text-center">{{ values|get:column }}</td>
                                    {% endfor %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    <table class="table dataTable w-100">
                        <tbody>
                            {% if data|length > 0 %}
                                <tr class="odd">
                                    <td>Total</td>
                                    {% for column in header %}
                                        <td class="text-center">{{ total|get:column }}</td>
                                    {% endfor %}
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="{% static 'web/datatables.js' %}"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    flatpickr(".flatpickr-input", {
        dateFormat: "Y/m/d",
    })

    $('#summary').DataTable({
        paging: false,
        searching: false,
        info: false,
    });
})
</script>

{% endblock %}