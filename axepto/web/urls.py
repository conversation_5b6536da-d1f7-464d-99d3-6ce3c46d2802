from django.urls import path

from . import views as v

urlpatterns = [
    path(r"about/", v.AboutAppView.as_view(), name="about"),
    path(r"dashboard/", v.DashboardView.as_view(), name="dashboard"),
    path(
        r"document_info/<str:pk>/", v.DocumentInfoView.as_view(), name="document_info"
    ),
    path(r"courier_info/<str:pk>/", v.CourierInfoView.as_view(), name="courier_info"),
    path(r"login/", v.WebLoginView.as_view(), name="web_login"),
    path(r"logout/", v.WebLogoutView.as_view(), name="web_logout"),
    path(r"summary/", v.WebSummaryView.as_view(), name="web_summary"),
    path(r"couriers/", v.WebCouriersView.as_view(), name="couriers"),
    path(
        r"couriers/edit/<int:id>/", v.WebCourierEditView.as_view(), name="courier_edit"
    ),
    path(
        r"couriers/delete/<int:id>/",
        v.WebCourierDeleteView.as_view(),
        name="courier_delete",
    ),
    path(r"couriers/add/", v.WebCourierAddView.as_view(), name="courier_add"),
    path(r"quarantine/", v.WebQuarantineView.as_view(), name="quarantine_dashboard"),
    path(r"logs/", v.WebLogsView.as_view(), name="logs_dashboard"),
    path(r"history/", v.WebHistoryView.as_view(), name="history"),
    path(r"surveys/", v.WebSurveysView.as_view(), name="surveys_dashboard"),
    path(
        r"surveys/records/<int:id>/",
        v.WebSurveyRecordsView.as_view(),
        name="survey_records",
    ),
    path(r"surveys/add/", v.WebSurveyAddView.as_view(), name="survey_add"),
    path(r"surveys/edit/<int:pk>/", v.WebSurveyEditView.as_view(), name="survey_edit"),
    path(
        r"surveys/delete/<int:pk>/",
        v.WebSurveyDeleteView.as_view(),
        name="survey_delete",
    ),
    path(r"postsurvey/", v.PostSurveyView.as_view(), name="postsurvey"),
    path(r"settings/", v.SettingsView.as_view(), name="settings"),
    path(
        r"settings/update/<str:name>/",
        v.SettingsUpdateView.as_view(),
        name="settings_update",
    ),
    path(r"bans/", v.WebBansView.as_view(), name="bans"),
    path(
        r"unban/<int:id>/",
        v.UnbanVies.as_view(),
        name="unban",
    ),
]
