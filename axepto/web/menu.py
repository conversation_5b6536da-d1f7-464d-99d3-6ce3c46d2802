from django.shortcuts import render
from setty import config

from axepto.constants import permissions_groups, web_permissions


def get_menu(req):
    res = {}
    for gr in permissions_groups:
        if hasattr(req.user, gr):
            for site, allowed in web_permissions.items():
                if gr in allowed:
                    res[site] = True

    return res


def render_to_html(html, dic):
    if "request" in dic:
        dic["menu_"] = get_menu(dic["request"])
        dic["setty"] = config
        return render(dic["request"], html, dic)
