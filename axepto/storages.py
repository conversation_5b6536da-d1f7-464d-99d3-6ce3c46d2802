import mimetypes
from typing import Any, Optional
from wsgiref.util import FileWrapper

from django.core.files.storage import default_storage


def store_get_wrapper(file_name: str) -> Optional[dict[str, Any]]:
    try:
        file = default_storage.open(file_name, mode="rb")
    except FileNotFoundError:
        return None

    return {
        "wrapper": FileWrapper(file),
        "size": default_storage.size(file_name),
        "type": mimetypes.guess_type(file_name)[0],
    }
