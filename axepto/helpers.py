import json
from datetime import datetime, timedelta
from importlib import import_module

import pytz
import requests
from django.conf import settings
from django.contrib import auth
from django.http import HttpResponse
from django.http.request import HttpRequest
from django.utils import timezone
from humanfriendly import format_timespan as hf_format_timespan

from .constants import WEB_DATETIME_FORMAT

HOST_DOMAIN = getattr(settings, "HOST_DOMAIN", "[host]")


def set_cookie_validity(request, location="rest"):
    if hasattr(request.user, "operator"):
        if location == "rest":
            request.session.set_expiry(settings.SESSION_COOKIE_AGE_OPERATOR)
        elif location == "web":
            request.session.set_expiry(settings.SESSION_COOKIE_AGE_OPERATOR_WEB)
    elif hasattr(request.user, "manager"):
        request.session.set_expiry(settings.SESSION_COOKIE_AGE_MANAGER)
    else:
        now = datetime.now()
        ten = now.replace(
            hour=22, minute=0, second=0
        )  # we work with Bratislava timezone
        utc_ten = get_utc_tz(ten)
        utc_ten = utc_ten.replace(tzinfo=None)
        stl = (utc_ten - now).total_seconds()  # seconds to logout
        stl = stl if stl >= 0 else stl + timedelta(days=1).total_seconds()
        request.session.set_expiry(int(stl))
        # request.session.set_expiry(settings.SESSION_COOKIE_AGE_MANAGER)


def json_response(object, status, **kwargs):
    return HttpResponse(
        json.dumps(object),
        status=status,
        content_type="application/json; charset=utf-8",
        **kwargs,
    )


def get_ba_tz(datetime):
    current_tz = timezone.get_current_timezone()
    ba_tz = pytz.timezone("Europe/Bratislava")

    datetime = current_tz.localize(datetime.replace(tzinfo=None))

    return ba_tz.normalize(datetime.astimezone(ba_tz))


# from bratislava timezone to UTC
def get_utc_tz(datetime):
    current_tz = timezone.get_current_timezone()
    ba_tz = pytz.timezone("Europe/Bratislava")

    datetime = ba_tz.localize(datetime.replace(tzinfo=None))

    return current_tz.normalize(datetime.astimezone(current_tz))


def date_to_timestamp(date):
    current_tz = timezone.get_current_timezone()
    ba_tz = pytz.timezone("Europe/Bratislava")

    date = date.replace(tzinfo=None)

    date = current_tz.normalize(ba_tz.localize(date)).astimezone(current_tz)

    return int(date.timestamp() * 1000)


def auth_required(func):
    def call(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return func(self, request, *args, **kwargs)
        else:
            return json_response({"error": "not logged in"}, status=401)

    return call


# accepts list of strings representing user types allowed
def auth_type_required(allowed_logins):
    def login_decorator(func):
        def func_wrapper(self, request, *args, **kwargs):
            if request.user.is_authenticated:
                for it in allowed_logins:
                    if hasattr(request.user, it):
                        return func(self, request, *args, **kwargs)

            return json_response(
                {"error": "Not logged in as " + ", ".join(allowed_logins)}, status=401
            )

        return func_wrapper

    return login_decorator


# in the most generic way does the core steps what
# django.contrib.sessions.middleware.SessionMiddleware and django.contrib.auth
# do to find supplied session cookie's user
def user_from_session_token(token):
    engine = import_module(settings.SESSION_ENGINE)
    session = engine.SessionStore(token)
    dummy_request = HttpRequest()
    dummy_request.session = session
    return auth.get_user(dummy_request)


# converts timestamp to web format


def format_time(timestamp, dt=False):
    if timestamp is None:
        return ""

    upd = get_ba_tz(datetime.fromtimestamp(timestamp / 1000))

    if dt:
        # xlwt cant work with tz aware datetime
        return upd.replace(tzinfo=None)
    else:
        return upd.strftime(WEB_DATETIME_FORMAT)


def get_absolute_url(relative_url):
    return HOST_DOMAIN + relative_url


# converts timespan in ms to human friendly format
def format_timespan(ms):
    val = ms / 1000

    return hf_format_timespan(val, False)


def send_slack_notification(title: str, text: str, color: str, slack_webhook_url: str):
    message = json.dumps(
        {
            "attachments": [
                {
                    "fallback": f"{title}: {text}",
                    "color": color,
                    "fields": [
                        {
                            "title": title,
                            "value": text,
                            "short": False,
                        }
                    ],
                }
            ]
        }
    )
    requests.post(slack_webhook_url, data=message)
