MZONE_SCHEMA = {
    "type": "object",
    "required": True,
    "properties": {
        "type": {
            "type": "string",
            "required": True,
        },
        "client_id": {
            "type": "string",
            "required": True,
            "maxLength": 100,
        },
        "customer_name": {
            "type": "string",
            "required": <PERSON>als<PERSON>,
        },
        "customer_surname": {
            "type": "string",
            "required": <PERSON>alse,
        },
        "customer_idcard": {
            "type": "string",
            "required": False,
        },
        "documents_info": {
            "type": "array",
            "minItems": 1,
            "required": True,
            "items": {
                "type": "object",
                "required": True,
                "properties": {
                    "title": {
                        "type": "string",
                        "required": True,
                    },
                    "file": {
                        "type": "string",
                        "required": True,
                    },
                    "required": {
                        "type": "boolean",
                        "required": True,
                    },
                },
            },
        },
    },
}
