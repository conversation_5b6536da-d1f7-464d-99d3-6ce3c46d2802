DIGI_SCHEMA = {
    "type": "object",
    "required": True,
    "properties": {
        "client_id": {
            "type": "string",
            "required": True,
            "maxLength": 100,
        },
        "company_id_number": {
            "type": ["string", "null"],
            "required": False,
        },
        "contact_number": {
            "type": ["string", "null"],
            "required": False,
        },
        "customer_type": {"enum": ["Residential", "", None], "required": False},
        "customer_id": {
            "type": ["string", "null"],
            "required": False,
        },
        "identification_type": {
            "type": ["string", "null"],
            "required": False,
        },
        "shipment_id": {
            "type": ["string", "null"],
            "required": False,
        },
        "shipping_name": {
            "type": ["string", "null"],
            "required": False,
        },
        "type": {"type": "string", "required": True},
        "zakaznik_meno": {
            "type": ["string", "null"],
            "required": False,
        },
        "zakaznik_priezvisko": {
            "type": ["string", "null"],
            "required": False,
        },
        "zakaznik_ulica": {
            "type": ["string", "null"],
            "required": False,
        },
        "zakaznik_cislo_domu": {
            "type": ["string", "null"],
            "required": False,
        },
        "zakaznik_psc": {
            "type": ["string", "null"],
            "required": False,
        },
        "zakaznik_mesto": {
            "type": ["string", "null"],
            "required": False,
        },
        "documents_info": {
            "type": "array",
            "minItems": 1,
            "required": True,
            "items": {
                "type": "object",
                "required": True,
                "properties": {
                    "title": {"type": "string", "required": True},
                    "file": {"type": "string", "required": True},
                    "fileId": {
                        "type": ["string", "null"],
                        "required": False,
                    },
                    "required": {"type": "boolean", "required": True},
                },
            },
        },
        "orders": {
            "type": ["array", "null"],
            "minItems": 0,
            "required": False,
            "items": {
                "type": ["string", "null"],
                "required": False,
            },
        },
    },
}
