BAN_PROPERTIES = {
    "type": "object",
    "required": True,
    "properties": {
        "fail_limit": {
            "type": "int",
            "required": True,
        },
        "cooloff_time": {
            "type": "int",
            "required": True,
        },
        "ban_duration": {
            "type": "int",
            "required": True,
        },
    },
}

BAN_CONFIG_SCHEMA = {
    "type": "object",
    "required": True,
    "properties": {
        "courier": BAN_PROPERTIES,
        "client": BAN_PROPERTIES,
        "operator": BAN_PROPERTIES,
        "manager": BAN_PROPERTIES,
        "ip": BAN_PROPERTIES,
    },
}
