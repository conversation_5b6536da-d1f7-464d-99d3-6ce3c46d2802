ORANGE_SCHEMA = {
    "type": "object",
    "required": True,
    "properties": {
        "client_id": {
            "type": "string",
            "required": True,
            "maxLength": 100,
        },
        "customer": {
            "type": "object",
            "required": True,
            "properties": {
                "fullname": {"type": "string", "required": True},
                "id_card": {
                    "type": "object",
                    "required": True,
                    "properties": {
                        "identifier": {"type": "string", "required": True},
                        "expiration": {"type": "string", "required": False},
                    },
                },
                "contact_phone": {"type": "string", "required": True},
                "address": {
                    "type": "object",
                    "required": False,
                    "properties": {
                        "street": {"type": "string", "required": False},
                        "street_no": {"type": "string", "required": False},
                        "zipcode": {"type": "string", "required": False},
                        "city": {"type": "string", "required": False},
                        "country": {"type": "string", "required": False},
                    },
                },
            },
        },
    },
}
