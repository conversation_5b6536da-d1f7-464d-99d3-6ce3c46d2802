O2_SCHEMA = {
    "type": "object",
    "required": True,
    "properties": {
        "client_id": {
            "type": "string",
            "required": True,
            "maxLength": 100,
        },
        "zakaznik_meno": {"type": "string", "required": False},
        "zakaznik_priezvisko": {"type": "string", "required": False},
        "zakaznik_ulica": {"type": "string", "required": False},
        "zakaznik_cislo_domu": {"type": "string", "required": False},
        "zakaznik_psc": {"type": "string", "required": False},
        "zakaznik_mesto": {"type": "string", "required": False},
        "type": {"type": "string", "required": True},
        "error_info": {
            "type": "array",
            "required": True,
            "items": {
                "type": "object",
                "required": False,
                "properties": {
                    "type": {"required": True},
                    "text": {"type": "string", "required": True},
                },
            },
        },
        "documents_info": {
            "type": "array",
            "minItems": 1,
            "required": True,
            "items": {
                "type": "object",
                "required": False,
                "properties": {
                    "title": {"type": "string", "required": True},
                    "file": {"type": "string", "required": True},
                    "required": {"type": "boolean", "required": True},
                },
            },
        },
        "scans_info": {
            "type": "object",
            "required": True,
            "properties": {
                "mandatory": {
                    "type": "array",
                    "required": False,
                    "items": {
                        "type": "object",
                        "required": False,
                        "properties": {
                            "text": {"type": "string", "required": True},
                            "description": {"type": "string", "required": True},
                            "folder": {"type": "string", "required": True},
                        },
                    },
                },
                "conditional": {
                    "type": "array",
                    "required": False,
                    "items": {
                        "type": "object",
                        "required": False,
                        "properties": {
                            "text": {"type": "string", "required": True},
                            "description": {"type": "string", "required": True},
                            "folder": {"type": "string", "required": True},
                        },
                    },
                },
                "optional": {
                    "type": "array",
                    "required": False,
                    "items": {
                        "type": "object",
                        "required": False,
                        "properties": {
                            "text": {"type": "string", "required": True},
                            "description": {"type": "string", "required": True},
                            "folder": {"type": "string", "required": True},
                        },
                    },
                },
            },
        },
    },
}
