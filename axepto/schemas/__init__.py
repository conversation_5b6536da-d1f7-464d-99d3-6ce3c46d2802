from typing import Any, Dict

from axepto.rest.models import Customer
from axepto.schemas.bans import BAN_CONFIG_SCHEMA
from axepto.schemas.digi import DIGI_SCHEMA
from axepto.schemas.mzone import M<PERSON><PERSON>E_SCHEMA
from axepto.schemas.o2 import O2_SCHEMA
from axepto.schemas.orange import ORANGE_SCHEMA
from axepto.schemas.telekom import TELEKOM_SCHEMA
from axepto.schemas.union import UNION_SCHEMA

schemas = {
    "mzone": MZONE_SCHEMA,
    "o2": O2_SCHEMA,
    "orange": ORANGE_SCHEMA,
    "telekom": TELEKOM_SCHEMA,
    "union": UNION_SCHEMA,
    "digi": DIGI_SCHEMA,
    "BAN_CONFIG": BAN_CONFIG_SCHEMA,
}


def get_schema(schema: str) -> Dict[str, Any]:
    return schemas[schema]


def get_customer_schema(customer: Customer) -> Dict[str, Any]:
    return schemas[customer.id]
