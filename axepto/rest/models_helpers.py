from axepto.helpers import format_time
from axepto.useful import get_in


def prepare_tracking_data(tracking):
    parsed = []

    val = get_in(tracking, ["wizard_start"], "")
    if val:
        parsed.append({"key": "Wizard Start", "value": format_time(val)})

    for it in get_in(tracking, ["signature_times"], []):
        print("test ", it)
        parsed.append(
            {
                "key": " ".join([it["name"], it["status"]]).title(),
                "value": format_time(it["time"]),
            }
        )

    val = get_in(tracking, ["wizard_close"], "")
    if val:
        parsed.append({"key": "Wizard Close", "value": format_time(val)})

    return parsed


def get_title(json_meta, customer=None):
    if customer is not None and customer.id == "orange":
        return "Zmluva"
    return get_in(json_meta, ["type"], "")


def get_address(json_meta, customer=None):
    address = {}
    if customer is not None and customer.id == "orange":
        address["street"] = get_in(json_meta, ["customer", "address", "street"], "")
        address["hn"] = get_in(json_meta, ["customer", "address", "street_no"], "")
        address["city"] = get_in(json_meta, ["customer", "address", "city"], "")
        address["psc"] = get_in(json_meta, ["customer", "address", "zipcode"], "")
    else:
        address["street"] = get_in(json_meta, ["zakaznik_ulica"], "")
        address["hn"] = get_in(json_meta, ["zakaznik_cislo_domu"], "")
        address["city"] = get_in(json_meta, ["zakaznik_mesto"], "")
        address["psc"] = get_in(json_meta, ["zakaznik_psc"], "")

    st = []
    if address["street"]:
        st.append(address["street"])
    if address["hn"]:
        st.append(address["hn"])

    address["street"] = " ".join(st)

    res = []
    if address["street"]:
        res.append(address["street"])
    if address["city"]:
        res.append(address["city"])
    if address["psc"]:
        res.append(address["psc"])

    return ", ".join(res)


def get_city(json_meta, customer=None):
    if customer is not None and customer.id == "orange":
        return get_in(json_meta, ["customer", "address", "city"], "")

    return get_in(json_meta, ["zakaznik_mesto"], "")


def get_zip(json_meta, customer=None):
    if customer is not None and customer.id == "orange":
        return get_in(json_meta, ["customer", "address", "zipcode"], "")

    return get_in(json_meta, ["zakaznik_psc"], "")


def get_fullname(json_meta, customer=None):
    if customer is not None and customer.id == "orange":
        return get_in(json_meta, ["customer", "fullname"], "")

    res = []
    name = get_in(json_meta, ["zakaznik_meno"], "")
    surname = get_in(json_meta, ["zakaznik_priezvisko"], "")
    if name:
        res.append(name)
    if surname:
        res.append(surname)

    return " ".join(res)


def get_power_of_attorney(json_meta):
    if "zakaznik_op_suhlas" not in json_meta:
        return "N/A"
    return get_in(json_meta, ["zakaznik_op_suhlas"], "") == "plna_moc"


def get_scans_info(json_meta):
    return get_in(json_meta, ["scans_info"], {})
