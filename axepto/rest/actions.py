import logging
from typing import Optional

from django.conf import settings
from django.contrib.auth.models import User
from django.core.files.storage import default_storage
from django.db import transaction

from axepto.constants import ASSIGN, DELETE, DOWNLOAD, LOGIN_REST
from axepto.rest.models import Courier, Document, History, Quarantine
from axepto.rest.useful import id_to_filename

logger = logging.getLogger(__name__)

# determines if documents should be deleted from S3 storage as well
DELETE_FROM_STORAGE = getattr(settings, "DELETE_FROM_STORAGE", True)


def assign_document(
    assignee: User,
    document: Document,
    author: User,
    auto: bool = False,
    save: bool = True,
):
    if getattr(assignee, "courier", None):
        logger.info("[%s] ASSIGNING %s to %s" % (ASSIGN, document.id, assignee.id))

        # resetting signed_remotely for packages that's signed version was lost
        # and are reassigned
        document.signed_remotely = False
        document.save(update_fields=["signed_remotely"])

        assignment = Courier.documents.through(
            document=document, courier=assignee.courier
        )
        history = History(
            document=document, author=author, user=assignee, action=ASSIGN, auto=auto
        )

        if save:
            assignment.save()
            history.save()

        return assignment, history


# Purpose of method is to delete given document and documents
# that are in relation with this document - parent, children
# it also creates logs and history entry
def delete_document(
    document: Document,
    user: Optional[User] = None,
    auto: bool = False,
) -> None:
    logger.info(
        "[%s] DELETING DOCUMENT %s BY USER %s"
        % (DELETE, document.id, user.id if user else "None")
    )

    with transaction.atomic():
        document.deleted = True
        if DELETE_FROM_STORAGE:
            document.hard_delete = True
        document.safe_delete_rest()
        document.save(update_fields=["deleted", "hard_delete", "rest"])
    doc_state = Document.objects.get(id=document.id)
    logger.info(
        f"Delete state of document {doc_state.id} is delete={doc_state.deleted},"
        f" hard_delete={doc_state.hard_delete}"
    )

    if DELETE_FROM_STORAGE:
        logger.warning("~~~before s3 call~~~")
        default_storage.delete(id_to_filename(document.id))
        default_storage.delete(id_to_filename(document.id_v2))
        logger.warning("~#~after s3 call~#~")

    # we never delete signed packages automatically with unsigned package delete
    if not auto:
        for ch in document.children.active().all():
            delete_document(ch, user, auto)

    if document.parent is not None:
        delete_document(document.parent, user, auto)

    History(document=document, author=user, action=DELETE, auto=auto).save()


def mark_unsigned_download(user, document):
    logger.info(
        "[%s] DOWNLOADING DOCUMENT %s BY USER %s" % (DOWNLOAD, document.id, user.id)
    )

    History(document=document, author=user, action=DOWNLOAD, auto=False).save()


def mark_login(user):
    if getattr(user, "courier", None):
        logger.info("[%s] LOGGING USER %s" % (LOGIN_REST, user.id))

        History(author=user, user=user, action=LOGIN_REST).save()
