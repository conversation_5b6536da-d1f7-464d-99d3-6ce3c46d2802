from django.urls import re_path

from . import views as v

urlpatterns = [
    re_path(r"^login/$", v.LoginView.as_view(), name="login"),
    re_path(r"^logout/$", v.LogoutView.as_view(), name="logout"),
    re_path(r"^active/$", v.DriverListView.as_view(), name="driver_list"),
    re_path(
        r"^active/(?P<version>v1|v2)/$",
        v.DriverListView.as_view(),
        name="driver_list_v2",
    ),
    re_path(r"^active/add/$", v.DriverListAddView.as_view(), name="driver_list_add"),
    re_path(
        r"document/unsigned/(?P<id>[0-9a-zA-Z-_$]+)/ack/$",
        v.DocumentUnsignedAckView.as_view(),
        name="document_unsigned_ack",
    ),
    re_path(
        r"document/unsigned/ack/(?P<id>[0-9a-zA-Z-_$]+)/$",
        v.DocumentUnsignedAckView.as_view(),
        name="document_unsigned_ack",
    ),
    re_path(
        r"document/unsigned/ack/$",
        v.DocumentUnsignedAckView.as_view(),
        name="document_unsigned_ack",
    ),
    re_path(
        r"^document/unsigned/(?P<id>[0-9a-zA-Z-_$]+)/$",
        v.HandleDocumentUnsignedView.as_view(),
        name="document_unsigned_get",
    ),
    re_path(
        r"^document/unsigned/(?P<id>[0-9a-zA-Z-_$]+)/(?P<version>v1|v2)/$",
        v.HandleDocumentUnsignedView.as_view(),
        name="document_unsigned_get",
    ),
    re_path(
        r"^document/unsigned/$",
        v.HandleDocumentUnsignedView.as_view(),
        name="document_unsigned_post",
    ),
    re_path(
        r"^document/signed/since/([0-9]+)/$",
        v.ListSignedSinceView.as_view(),
        name="list_signed_since",
    ),
    re_path(
        r"^document/unsigned/since/([0-9]+)/$",
        v.ListUnsignedSinceView.as_view(),
        name="list_unsigned_since",
    ),
    re_path(
        r"^document/signed/(?P<id>[0-9a-zA-Z-_$]+)/$",
        v.HandleDocumentSignedView.as_view(),
        name="document_signed_get",
    ),
    re_path(
        r"^document/signed/(?P<id>[0-9a-zA-Z-_$]+/)?$",
        v.HandleDocumentSignedView.as_view(),
        name="document_signed_post",
    ),
    re_path(r"^assign/$", v.AssignView.as_view(), name="assign_post"),
    re_path(r"^assign2/$", v.Assign2View.as_view(), name="assign2_post"),
    re_path(
        r"^quarantine/(?P<id>[0-9a-zA-Z-_]+)/$",
        v.QuarantineView.as_view(),
        name="quarantine",
    ),
    re_path(
        r"^quarantine/delete/(?P<id>[0-9a-zA-Z]+)/$",
        v.WebQuarantineDeleteView.as_view(),
        name="quarantine_delete",
    ),
    re_path(r"^logs/(?P<id>[0-9a-zA-Z-_]+)/$", v.LogsView.as_view(), name="logs"),
    re_path(r"^logs/$", v.LogsView.as_view(), name="logs"),
    re_path(r"^survey/$", v.SurveyView.as_view(), name="survey"),
    re_path(r"^auth/$", v.AuthView.as_view(), name="auth"),
    re_path(
        r"^document/mark-signed/$",
        v.MarkRemotelySignedView.as_view(),
        name="mark_remotely_signed",
    ),
    re_path(r"^register/$", v.RegisterView.as_view(), name="register"),
    re_path(r"^changepassword/$", v.ChangePassword.as_view(), name="change_password"),
]
