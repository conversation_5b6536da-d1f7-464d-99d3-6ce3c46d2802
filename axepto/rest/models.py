import datetime

from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import (
    CASCADE,
    PROTECT,
    SET_NULL,
    BigIntegerField,
    BooleanField,
    Char<PERSON>ield,
    DateField,
    DateTimeField,
    ForeignKey,
    GenericIPAddressField,
    IntegerField,
    JSONField,
    ManyToManyField,
    Model,
    OneToOneField,
    Q,
    QuerySet,
    UniqueConstraint,
)
from humanfriendly import format_size

from axepto.constants import SUMMARY_TYPES, WEB_DATETIME_FORMAT
from axepto.helpers import get_ba_tz
from axepto.useful import get_in

from .model.courier_center import CourierCenter
from .model.model_customer import Customer
from .model.model_history import History
from .model.model_log import Log
from .model.model_notification import Notification
from .model.model_quarantine import Quarantine
from .model.model_survey import Survey, SurveyRecord
from .models_helpers import (
    get_address,
    get_city,
    get_fullname,
    get_power_of_attorney,
    get_scans_info,
    get_title,
    get_zip,
    prepare_tracking_data,
)


def empty(json={}):
    return ""


def empty_rest(rest):
    return {}


def get_wizard_start(rest):
    return get_in(rest, ["wizard_start"], "")


def get_wizard_close(rest):
    return get_in(rest, ["wizard_close"], "")


def get_signature_times(rest):
    return get_in(rest, ["signature_times"], [])


# returns deleted value of rest field after delete call
deleted_rest = getattr(settings, "GET_DELETED_REST", empty_rest)

# determines if documents should be deleted from S3 storage as well
DELETE_FROM_STORAGE = getattr(settings, "DELETE_FROM_STORAGE", True)


def get_field(field, json_meta, customer):
    if field == "fullname":
        return get_fullname(json_meta, customer)

    elif field == "address":
        return get_address(json_meta, customer)

    elif field == "wizard-title":
        return get_title(json_meta, customer)

    elif field == "city":
        return get_city(json_meta, customer)

    elif field == "wizard-start":
        return get_wizard_start(json_meta)

    elif field == "wizard-close":
        return get_wizard_close(json_meta)

    elif field == "signature-times":
        return get_signature_times(json_meta)

    elif field == "zip":
        return get_zip(json_meta, customer)

    elif field == "power-of-attorney":
        return get_power_of_attorney(json_meta)

    elif field == "scans-info":
        return get_scans_info(json_meta)


class DocumentQuerySet(QuerySet):
    def signed(self, value=True):
        if value:
            return self.exclude(parent=None)
        else:
            return self.filter(parent=None)

    def filter_type(self, document_type):
        if document_type == SUMMARY_TYPES.signed:
            return self.signed(True)
        if document_type == SUMMARY_TYPES.unsigned:
            return self.signed(False)
        if document_type == SUMMARY_TYPES.assign:
            return self.signed(False).exclude(courier=None)
        return self

    def active(self, value=True):
        return self.filter(deleted=(not value))

    def error(self, value=True):
        if value:
            return self.exclude(error__startswith="{}")
        else:
            return self.filter(error__startswith="{}")

    def since(self, value):
        return self.filter(timestamp__gte=value)

    def until(self, value):
        return self.filter(timestamp__lte=value)

    def with_id(self, value):
        return self.filter(Q(client_id=value) | Q(id=value) | Q(id_v2=value))

    def with_ids(self, values):
        return self.filter(
            Q(client_id__in=values) | Q(id__in=values) | Q(id_v2__in=values)
        )

    def with_courier(self, idx):
        return self.filter(Q(author__user__id=idx) | Q(courier__user__id=idx))

    def with_customer(self, customer):
        if customer:
            return self.filter(customer=customer)
        return self

    def text_query(self, value):
        return self.filter(
            Q(client_id__istartswith=value)
            | Q(id__istartswith=value)
            | Q(id_v2__istartswith=value)
        )

    def has_children(self, value=True):
        return self.filter(children__isnull=(not value))


class CourierQuerySet(QuerySet):
    def available(self):
        return self.filter(deleted=False)

    def active(self, value=True):
        return self.filter(active=value, deleted=False)

    def text_query(self, value):
        return self.filter(Q(user__username__icontains=value) | Q(id__icontains=value))


class Document(Model):
    objects = DocumentQuerySet.as_manager()

    id: CharField = CharField(
        max_length=100,
        blank=True,
        unique=True,
        default="",
        primary_key=True,
        db_index=True,
    )

    id_v2: CharField = CharField(max_length=100, blank=True, default="", db_index=True)

    timestamp: BigIntegerField = BigIntegerField(db_index=True, default=0)

    client_id: CharField = CharField(
        max_length=100, blank=True, default="", db_index=True
    )

    error: JSONField = JSONField(default=dict, null=True, blank=True)

    rest: JSONField = JSONField(default=dict, null=True, blank=True)

    tracking: JSONField = JSONField(default=dict, null=True, blank=True)

    parent: ForeignKey = ForeignKey(
        "self", null=True, blank=True, related_name="children", on_delete=CASCADE
    )

    signed_locally: BooleanField = BooleanField(
        null=False,
        blank=True,
        default=False,
    )

    signed_remotely: BooleanField = BooleanField(
        null=False,
        blank=True,
        default=False,
    )

    author: ForeignKey = ForeignKey(
        "Courier",
        related_name="author_of",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    customer: Customer = ForeignKey(
        "Customer",
        null=True,
        blank=True,
        related_name="documents",
        on_delete=SET_NULL,
    )

    version: CharField = CharField(max_length=15, blank=True, default="")

    original_file_name: CharField = CharField(max_length=120, blank=True, default="")

    zip_size: BigIntegerField = BigIntegerField(db_index=True, default=0)

    deleted: BooleanField = BooleanField(default=False)

    hard_delete: BooleanField = BooleanField(default=False)

    notified: BooleanField = BooleanField(default=False)

    operator_notification: BooleanField = BooleanField(default=False)

    internal_id: CharField = CharField(max_length=100, blank=True, default="")

    def get_rep_id(self):
        return self.client_id if self.client_id else self.id

    def is_unsigned(self):
        return self.parent is None

    def is_error(self):
        return bool(self.error)

    def is_signed(self):
        return not self.is_error() and not self.is_unsigned()

    def get_error_code(self):
        if self.is_error():
            return self.error["code"]
        else:
            return None

    def get_error_code_msg(self):
        if self.is_error():
            return self.error["text"]
        else:
            return None

    # Returns time of upload in Bratislava, in format Y/M/D - H:M:S if dt is false
    # if dt is true returns time in datetime format
    def get_time_formatted(self, dt=False):

        res = get_ba_tz(datetime.datetime.fromtimestamp(self.timestamp / 1000))

        if dt:
            # xlwt cant work with tz aware datetime
            return res.replace(tzinfo=None)

        return res.strftime(WEB_DATETIME_FORMAT)

    def get_expiration_formatted(self, dt=False):

        res = get_ba_tz(
            datetime.datetime.fromtimestamp(self.timestamp / 1000).replace(
                hour=21, minute=0, second=0
            )
            + datetime.timedelta(days=self.customer.old_unsigned_packages_removal_limit)
        )

        if dt:
            # xlwt cant work with tz aware datetime
            return res.replace(tzinfo=None)

        return res.strftime(WEB_DATETIME_FORMAT)

    def get_display_name(self):
        return get_field("fullname", self.rest, self.customer)

    def get_city_name(self):
        return get_city(self.rest, self.customer)

    def get_display_size(self):
        return format_size(self.zip_size)

    def get_data(self, field, property_name):
        if field == "rest":
            return get_field(property_name, self.rest, self.customer)
        if field == "tracking":
            return get_field(property_name, self.tracking, self.customer)

        return None

    @property
    def get_parsed_metadata(self):
        parsed = []
        for key in ["wizard-title", "address", "power-of-attorney"]:
            parsed.append(
                {
                    "key": key.replace("-", " ").title(),
                    "value": get_field(key, self.rest, self.customer),
                }
            )
        return parsed

    def get_tracking_data(self):
        return prepare_tracking_data(self.tracking)

    # If we interpret 'parent' relation as oriented edge, the representing
    # document is the most relevant document from the oriented star containing
    # this document. Resolved in this order:
    # 1. the newest signed version, if such version exists
    # 2. the newest error version, if such version exists
    # 3. the unsigned document itself
    def get_representing_doc(self):
        if self.is_unsigned():
            # Primarily, return last successfully signed document
            signed_doc = (
                self.children.all().filter(error={}).order_by("-timestamp").first()
            )
            if signed_doc is not None:
                return signed_doc
            else:
                # If there are no successfully signed documents, return
                # newest error document
                error_doc = self.children.all().order_by("-timestamp").first()
                if error_doc is not None:
                    return error_doc
                else:
                    # There is no signed or error document for this
                    return self
        else:
            return self.parent.get_representing_doc()

    def get_status(self):
        if self.is_error():
            return "error"
        elif self.is_unsigned():
            return "unsigned"
        else:
            return "signed"

    def get_download_link(self):
        if self.is_unsigned():
            return "%s/document/unsigned/%s/" % (settings.HOST_DOMAIN, self.id)
        else:
            return "%s/document/signed/%s/" % (settings.HOST_DOMAIN, self.id)

    # A document is finished, if it is either signed or has a signed version
    def is_finished(self):
        def has_signed_child(doc):
            if doc.children.filter(error={}).exists():
                return True
            else:
                return False

        if self.is_unsigned():
            return has_signed_child(self)
        elif self.is_error():
            return has_signed_child(self.parent)
        else:
            # Document is signed => finished
            return True

    def get_parent(self):
        # if unsigned return own timestamp
        # if signed/error(has parent) return parens timestamp

        if self.parent is None:
            return self

        else:
            return self.parent

    # returns parent time if doesnt have parent returns own time
    # datetime - if true date returned in datetime format instead of string
    def get_parent_time(self, dt=False):
        return self.get_parent().get_time_formatted(dt)

    def get_signed_time(self, dt=False):
        if self.parent is not None:
            return self.get_time_formatted(dt)
        else:
            return ""

    def safe_delete_rest(self):
        self.rest = deleted_rest(self.rest)

    def __str__(self):
        return "%s-%s" % (self.id, self.get_status())


class Courier(Model):
    objects = CourierQuerySet.as_manager()

    id: CharField = CharField(max_length=20, db_index=True, default="", blank=True)

    user: OneToOneField = OneToOneField(User, primary_key=True, on_delete=CASCADE)

    documents: ManyToManyField = ManyToManyField(Document, blank=True)

    active: BooleanField = BooleanField(default=True)

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    updated_at: DateTimeField = DateTimeField(auto_now=True)

    deleted: BooleanField = BooleanField(default=False)

    center: ForeignKey = ForeignKey(
        CourierCenter,
        null=True,
        blank=True,
        on_delete=SET_NULL,
    )

    def assign_center(self):
        # This should assign center to courier based on his ID.
        # Courier ID should contain center id on positions 4 and 5
        if self.id[4:6].isdigit():
            center_id = int(self.id[4:6])
            center = CourierCenter.objects.filter(center_id=center_id).first()
            if center:
                self.center = center
                self.save(update_fields=["center"])

    def get_date_created(self):
        return get_ba_tz(self.created_at).strftime(WEB_DATETIME_FORMAT)

    def get_last_login(self):
        if self.user.last_login is None:
            return ""
        return get_ba_tz(self.user.last_login).strftime(WEB_DATETIME_FORMAT)

    # Documents assigned to this courier, which do not have signed version
    def get_unfinished_documents(self):
        return [
            doc
            for doc in self.documents.filter(deleted=False).all()
            if not doc.is_finished()
        ]

    def get_driver_list(self):
        return list(
            self.documents.filter(
                children__isnull=True, parent=None, deleted=False, signed_remotely=False
            ).all()
        )

    def __str__(self):
        return str(self.user)

    @property
    def name(self):
        return self.user.username


class Client(Model):
    user: OneToOneField = OneToOneField(
        User,
        primary_key=True,
        on_delete=CASCADE,
    )

    def __str__(self):
        return str(self.user)


class Operator(Model):
    user: OneToOneField = OneToOneField(
        User,
        primary_key=True,
        on_delete=CASCADE,
    )

    customer: ForeignKey = ForeignKey(
        "Customer",
        null=True,
        blank=True,
        related_name="operators",
        on_delete=PROTECT,
    )

    def __str__(self):
        return str(self.user)


class Manager(Model):
    user: OneToOneField = OneToOneField(
        User,
        primary_key=True,
        on_delete=CASCADE,
    )

    def __str__(self):
        return str(self.user)


class Metric(Model):
    customer: ForeignKey = ForeignKey(
        "Customer",
        null=True,
        blank=True,
        on_delete=SET_NULL,
    )

    date: DateField = DateField(default=datetime.date.today)

    executed: DateTimeField = DateTimeField(default=datetime.datetime.now)

    type: CharField = CharField(
        max_length=20,
        choices=[
            (summary_type.value, summary_type.value) for summary_type in SUMMARY_TYPES
        ],
    )

    value: IntegerField = IntegerField(default=0)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=["customer", "date", "type"], name="unique_customer_date_type"
            )
        ]


class LoginAttempt(Model):
    username: CharField = CharField(
        max_length=150,
        db_index=True,
    )

    ip: GenericIPAddressField = GenericIPAddressField(protocol="IPv4")

    timestamp: DateTimeField = DateTimeField(default=datetime.datetime.now)

    successful: BooleanField = BooleanField(
        null=False,
        blank=True,
        default=False,
    )


class BanQuerySet(QuerySet):
    def search(self, query: str):
        return self.filter(Q(user__username__icontains=query) | Q(ip__icontains=query))


class Ban(Model):
    objects = BanQuerySet.as_manager()

    user: ForeignKey = ForeignKey(
        User,
        related_name="bans",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    ip: GenericIPAddressField = GenericIPAddressField(
        protocol="IPv4", blank=True, null=True
    )

    active: BooleanField = BooleanField(default=True)

    valid_from: DateTimeField = DateTimeField(default=datetime.datetime.now)

    valid_to: DateTimeField = DateTimeField(default=datetime.datetime.now)

    @property
    def banned_from(self):
        return get_ba_tz(self.valid_from).strftime(WEB_DATETIME_FORMAT)

    @property
    def banned_to(self):
        return get_ba_tz(self.valid_to).strftime(WEB_DATETIME_FORMAT)
