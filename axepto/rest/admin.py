from django.contrib import admin

from axepto.rest.models import (
    Ban,
    Client,
    Courier,
    CourierCenter,
    Customer,
    Document,
    History,
    Log,
    LoginAttempt,
    Manager,
    Notification,
    Operator,
    Quarantine,
    Survey,
    SurveyRecord,
)


class SignedCategoryListFilter(admin.SimpleListFilter):
    title = "Signed"
    parameter_name = "signed_category"

    def lookups(self, request, model_admin):
        return (("signed", "Signed"), ("unsigned", "Unsigend"))

    def queryset(self, request, queryset):
        if self.value() == "unsigned":
            return queryset.filter(parent=None)
        if self.value() == "signed":
            return queryset.exclude(parent=None)


class ErrorCategoryListFilter(admin.SimpleListFilter):
    title = "Error"
    parameter_name = "error_category"

    def lookups(self, request, model_admin):
        return (("yes", "Yes"), ("no", "No"))

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.exclude(error__startswith="{}")
        if self.value() == "no":
            return queryset.filter(error__startswith="{}")


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    readonly_fields = ("download_link",)

    def download_link(self, object):
        return object.get_download_link()

    exclude = ("parent",)

    list_display = ("__str__", "timestamp", "get_time_formatted")

    list_filter = (SignedCategoryListFilter, ErrorCategoryListFilter)


@admin.register(Courier)
class CourierAdmin(admin.ModelAdmin):
    readonly_fields = ("associated_user", "assigned_documents")
    raw_id_fields = ("documents",)

    def associated_user(self, object):
        return str(object.user)

    def assigned_documents(self, object):
        return ", ".join(list(map(lambda doc: str(doc), object.documents.all())))

    def assigned_surveys(self, object):
        return ", ".join(list(map(lambda doc: str(doc), object.surveys.all())))


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    readonly_fields = ("associated_user",)

    def associated_user(self, object):
        return str(object.user)


@admin.register(Operator)
class OperatorAdmin(admin.ModelAdmin):
    readonly_fields = ("associated_user",)

    def associated_user(self, object):
        return str(object.user)


@admin.register(Manager)
class ManagerAdmin(admin.ModelAdmin):
    readonly_fields = ("associated_user",)

    def associated_user(self, object):
        return str(object.user)


@admin.register(Quarantine)
class QuarantineAdmin(admin.ModelAdmin):
    readonly_fields = ("id", "timestamp", "zip_size")


@admin.register(History)
class HistoryAdmin(admin.ModelAdmin):
    list_display = ("__str__", "created_at", "author")


@admin.register(Log)
class LogAdmin(admin.ModelAdmin):
    list_display = ("__str__", "created_at", "courier")


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ("__str__", "created_at", "type", "sent")


@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ("__str__", "active", "repeating")


@admin.register(SurveyRecord)
class SurveyRecordAdmin(admin.ModelAdmin):
    list_display = ("survey", "timestamp", "get_time_formatted")
    raw_id_fields = ("courier",)


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ("__str__", "active")


@admin.register(CourierCenter)
class CourierCenterAdmin(admin.ModelAdmin):
    list_display = ("id", "center_id", "name", "code")


@admin.register(LoginAttempt)
class LoginAttemptAdmin(admin.ModelAdmin):
    list_display = ("username", "ip", "timestamp", "successful")


@admin.register(Ban)
class BanAdmin(admin.ModelAdmin):
    list_display = ("username", "ip", "valid_from", "valid_to", "active")

    def username(self, object):
        return str(object.user)
