from django.conf import settings

from axepto.useful import get_in

ERROR_CODES = {
    0: "Technik nedorazil na sjednanou návštěvu (nehoda, zdržení, … )",
    1: "Technik nenašel adresu zákazníka",
    2: "Z technických důvodů nemohly být práce provedeny",
    3: "Zákazník nebyl v domluvené době na nahlášené adrese",
    4: "Zákazník nesouhlasil s cenou revize (u nadstandartních prací)",
    5: "Zákazník nesouhlasil s podpisem dodatku",
    6: "Zákazník neměl připravenou plnou moc",
    None: "",
}

ALLOWED_INFO_KEYS = [
    "zakaznik_meno",
    "zakaznik_priezvisko",
    "zakaznik_ulica",
    "zakaznik_cislo_domu",
    "zakaznik_psc",
    "zakaznik_mesto",
    "zakaznik_telefon",
    "zakaznik_email",
    "zakaznik_poznamka",
]

wizard_documents = {
    "amendment": "dodatek_digi.pdf",
    "contract": "smlouva_digi.pdf",
    "contract_tech": "smlouva_digi_tech.pdf",
    "contract_dealer": "smlouva_digi_dealer.pdf",
    "final": "formular_o_provedeni_digi.pdf",
    "internet": "smlouva_digi_internet.pdf",
}
