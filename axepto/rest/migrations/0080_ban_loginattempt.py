# Generated by Django 3.2.8 on 2023-05-26 08:36

import datetime

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("rest", "0079_auto_20220516_1101"),
    ]

    operations = [
        migrations.CreateModel(
            name="LoginAttempt",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(db_index=True, max_length=150)),
                ("ip", models.GenericIPAddressField(protocol="IPv4")),
                ("timestamp", models.DateTimeField(default=datetime.datetime.now)),
                ("successful", models.BooleanField(blank=True, default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Ban",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ip",
                    models.GenericIPAddressField(
                        blank=True, null=True, protocol="IPv4"
                    ),
                ),
                ("active", models.BooleanField(default=True)),
                ("valid_from", models.DateTimeField(default=datetime.datetime.now)),
                ("valid_to", models.DateTimeField(default=datetime.datetime.now)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="bans",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
