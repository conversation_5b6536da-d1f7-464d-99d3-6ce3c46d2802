# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-01-31 07:57
from __future__ import unicode_literals

from django.db import migrations

from axepto.rest.models import Document


def set_signed_attributes(apps, schema_editor):
    Document.objects.filter(parent__isnull=False).update(
        signed_locally=True, signed_remotely=True
    )


class Migration(migrations.Migration):
    dependencies = [
        ("rest", "0065_auto_20200131_0757"),
    ]

    operations = [migrations.RunPython(set_signed_attributes)]
