# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-04-01 14:20
from __future__ import unicode_literals

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0049_document_operator_notification"),
    ]

    operations = [
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=100)),
                ("active", models.BooleanField(default=False)),
            ],
        ),
        migrations.AddField(
            model_name="document",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="documents",
                to="rest.Customer",
            ),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="operator",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="operators",
                to="rest.Customer",
            ),
        ),
    ]
