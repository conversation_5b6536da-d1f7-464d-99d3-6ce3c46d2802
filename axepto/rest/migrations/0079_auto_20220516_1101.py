# Generated by Django 3.2.8 on 2022-05-16 11:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0078_document_original_file_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="customer",
            name="old_quarantine_packages_removal_limit",
            field=models.IntegerField(default=30),
        ),
        migrations.AddField(
            model_name="customer",
            name="old_signed_packages_removal_limit",
            field=models.IntegerField(default=30),
        ),
        migrations.AddField(
            model_name="customer",
            name="old_unsigned_packages_removal_limit",
            field=models.IntegerField(default=40),
        ),
        migrations.AddField(
            model_name="quarantine",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="quarantine",
                to="rest.customer",
            ),
        ),
    ]
