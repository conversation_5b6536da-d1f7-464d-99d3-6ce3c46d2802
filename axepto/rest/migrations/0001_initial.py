# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import jsonfield.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Courier",
            fields=[
                (
                    "id",
                    models.AutoField(
                        primary_key=True,
                        serialize=False,
                        auto_created=True,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.CharField(
                        blank=True,
                        unique=True,
                        db_index=True,
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                        default="",
                    ),
                ),
                ("timestamp", models.BigIntegerField(db_index=True, default=0)),
                ("client_id", models.<PERSON>r<PERSON><PERSON>(max_length=100, default="", blank=True)),
                ("error", models.CharField(max_length=500, default="", blank=True)),
                ("rest", jsonfield.fields.JSONField(null=True, default={}, blank=True)),
                (
                    "author",
                    models.ForeignKey(
                        null=True,
                        blank=True,
                        to="rest.Courier",
                        related_name="author_of",
                        on_delete=models.SET_NULL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        null=True,
                        blank=True,
                        to="rest.Document",
                        related_name="children",
                        on_delete=models.CASCADE,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Operator",
            fields=[
                (
                    "id",
                    models.AutoField(
                        primary_key=True,
                        serialize=False,
                        auto_created=True,
                        verbose_name="ID",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="courier",
            name="documents",
            field=models.ManyToManyField(blank=True, to="rest.Document"),
        ),
        migrations.AddField(
            model_name="courier",
            name="user",
            field=models.OneToOneField(
                to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE
            ),
        ),
    ]
