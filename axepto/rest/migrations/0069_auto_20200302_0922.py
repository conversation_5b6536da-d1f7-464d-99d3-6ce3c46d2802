# Generated by Django 2.2.10 on 2020-03-02 09:22

import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0068_auto_20200302_0919"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="document",
            name="error",
            field=django.contrib.postgres.fields.jsonb.JSONField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="document",
            name="rest",
            field=django.contrib.postgres.fields.jsonb.JSONField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="document",
            name="tracking",
            field=django.contrib.postgres.fields.jsonb.JSONField(
                blank=True, default=dict, null=True
            ),
        ),
    ]
