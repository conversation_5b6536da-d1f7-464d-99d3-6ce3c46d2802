from django.db import migrations
from setty.models import SettySettings, TypeChoices


def create_ban_settings(_, __):
    if not SettySettings.objects.filter(name__exact="BAN_CONFIG").exists():
        SettySettings.objects.create(
            name="BAN_CONFIG",
            type=TypeChoices.DICT,
            value={
                "courier": {
                    "fail_limit": 10,
                    "cooloff_time": 600,
                    "ban_duration": 600,
                },
                "client": {
                    "fail_limit": 10,
                    "cooloff_time": 600,
                    "ban_duration": 600,
                },
                "operator": {
                    "fail_limit": 10,
                    "cooloff_time": 600,
                    "ban_duration": 600,
                },
                "manager": {
                    "fail_limit": 10,
                    "cooloff_time": 600,
                    "ban_duration": 600,
                },
                "ip": {
                    "fail_limit": 25,
                    "cooloff_time": 600,
                    "ban_duration": 600,
                },
            },
        )


def delete_ban_settings(_, __):
    SettySettings.objects.filter(name__exact="BAN_CONFIG").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("rest", "0080_ban_loginattempt"),
        ("setty", "0002_settysettings_app_name"),
    ]

    operations = [migrations.RunPython(create_ban_settings, delete_ban_settings)]
