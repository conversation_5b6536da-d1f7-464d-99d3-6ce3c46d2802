# -*- coding: utf-8 -*-
# Generated by Django 1.11.11 on 2018-10-01 18:22
from __future__ import unicode_literals

from django.contrib.postgres.fields.jsonb import <PERSON><PERSON><PERSON><PERSON>
from django.db import migrations


def from_text_json(apps, schema_editor):

    Document = apps.get_model("rest", "Document")

    for doc in Document.objects.all():
        doc.error = doc.error_old
        doc.rest = doc.rest_old
        doc.tracking = doc.tracking_old
        doc.save()


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0043_auto_20171025_1208"),
    ]

    operations = [
        # rename old fields
        migrations.RenameField(
            model_name="document",
            old_name="error",
            new_name="error_old",
        ),
        migrations.RenameField(
            model_name="document",
            old_name="rest",
            new_name="rest_old",
        ),
        migrations.RenameField(
            model_name="document",
            old_name="tracking",
            new_name="tracking_old",
        ),
        # add new fields
        migrations.AddField(
            model_name="document",
            name="error",
            field=<PERSON><PERSON><PERSON>ield(blank=True, default={}, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="document",
            name="rest",
            field=JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name="document",
            name="tracking",
            field=JSONField(blank=True, default={}, null=True),
        ),
        # migrate data
        migrations.RunPython(from_text_json),
        # we cant delete old fields as nn PostgreSQL you must not update the table
        # and then alter the table schema in one transaction
    ]
