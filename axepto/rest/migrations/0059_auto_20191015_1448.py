# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-10-15 14:48
from __future__ import unicode_literals

from django.db import migrations

from axepto.rest.models import CourierCenter

centers = [
    [10, "SPS Bratislava", "BA"],
    [11, "SPS Dunajská Streda", "DS"],
    [12, "SPS Komárno", "KN"],
    [20, "SPS Košice", "KE"],
    [21, "SPS Bardejov", "BJ"],
    [22, "SPS Humenné", "HE"],
    [23, "SPS Michalovce", "MI"],
    [24, "SPS Stará Ľubovňa", "SL"],
    [25, "SPS Svidník", "SK"],
    [26, "SPS Trebišov", "TV"],
    [30, "SPS Žilina", "ZA"],
    [31, "SPS Trnava", "TT"],
    [32, "SPS Trenčín", "TN"],
    [33, "SPS Liptovský Mikuláš", "LM"],
    [34, "SPS Martin", "MT"],
    [35, "SPS Poprad", "PP"],
    [37, "SPS Ružomberok", "RK"],
    [38, "SPS Spišská Nová Ves", "SN"],
    [40, "SPS Banská Bystrica", "BB"],
    [41, "SPS Nitra", "NR"],
    [42, "SPS Lučenec", "LC"],
    [43, "SPS Prievidza", "PD"],
    [44, "SPS Rožňava", "RV"],
    [45, "SPS Senica", "SE"],
    [46, "SPS Levice", "LV"],
    [47, "SPS Bánovce n/B", "BN"],
    [48, "SPS Topoľčany", "TO"],
]


def forward(apps, schema_editor):
    for center in centers:
        CourierCenter(
            center_id=center[0],
            name=center[1],
            code=center[2],
        ).save()


def backward(apps, schema_editor):
    for center in centers:
        CourierCenter.objects.filter(center_id=center[0]).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("rest", "0058_couriercenter"),
    ]

    operations = [migrations.RunPython(forward, backward)]
