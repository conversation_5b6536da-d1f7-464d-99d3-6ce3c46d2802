# Generated by Django 3.2.8 on 2021-11-15 09:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0073_auto_20210906_1317"),
    ]

    operations = [
        migrations.CreateModel(
            name="Metric",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(auto_now_add=True)),
                ("executed", models.DateTimeField(auto_now_add=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("UNSIGNED", "Unsigned"),
                            ("SIGNED", "Signed"),
                            ("ASSIGNED", "Assigned"),
                        ],
                        max_length=20,
                    ),
                ),
                ("value", models.IntegerField(default=0)),
                (
                    "customer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="rest.customer",
                    ),
                ),
            ],
        ),
    ]
