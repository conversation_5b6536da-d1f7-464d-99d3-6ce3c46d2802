# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0030_document_tracking"),
    ]

    operations = [
        migrations.CreateModel(
            name="Log",
            fields=[
                (
                    "id",
                    models.CharField(
                        max_length=100,
                        db_index=True,
                        default="",
                        serialize=False,
                        blank=True,
                        primary_key=True,
                        unique=True,
                    ),
                ),
                ("timestamp", models.BigIntegerField(db_index=True, default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("zip_size", models.BigIntegerField(db_index=True, default=0)),
                ("file_name", models.CharField(max_length=100, blank=True, default="")),
                (
                    "courier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        to="rest.Courier",
                        related_name="logs",
                        on_delete=models.SET_NULL,
                    ),
                ),
            ],
        ),
    ]
