import math
from datetime import datetime

from django.db import migrations


class Migration(migrations.Migration):

    now = datetime.now()

    dependencies = [
        ("rest", "0070_auto_20200302_0922"),
    ]

    operations = [
        migrations.RunSQL(
            "CREATE UNIQUE INDEX unique_client_id on rest_document (client_id, "
            + "(parent_id IS NULL)) WHERE parent_id IS NULL AND timestamp > {};".format(
                math.floor(now.timestamp() * 1000)
            )
        )
    ]
