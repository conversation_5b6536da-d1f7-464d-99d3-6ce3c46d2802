# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0032_log_device_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        primary_key=True,
                        auto_created=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("send_at", models.DateTimeField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("REPORT_DAILY", "Report daily"),
                            ("REPORT_WEEKLY", "Report weekly"),
                        ],
                        default=None,
                        max_length=25,
                    ),
                ),
                ("sent", models.BooleanField(default=False)),
                ("send_to_courier", models.BooleanField(default=False)),
                ("subject", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("text", models.TextField()),
            ],
        ),
    ]
