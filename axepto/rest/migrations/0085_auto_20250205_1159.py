# Generated by Django 3.2.8 on 2025-02-05 11:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rest", "0084_customer_shipper_id"),
    ]

    operations = [
        migrations.RunSQL(
            "CREATE INDEX id_ops_idx ON rest_document "
            "(UPPER(id) varchar_pattern_ops);",
            "DROP INDEX id_ops_idx;",
        ),
        migrations.RunSQL(
            "CREATE INDEX id_v2_ops_idx ON rest_document "
            "(UPPER(id_v2) varchar_pattern_ops);",
            "DROP INDEX id_v2_ops_idx;",
        ),
        migrations.RunSQL(
            "CREATE INDEX client_id_ops_idx ON rest_document "
            "(UPPER(client_id) varchar_pattern_ops);",
            "DROP INDEX client_id_ops_idx;",
        ),
    ]
