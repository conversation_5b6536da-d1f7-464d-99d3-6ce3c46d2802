# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0036_auto_20170906_0826"),
    ]

    operations = [
        migrations.CreateModel(
            name="Survey",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        verbose_name="ID",
                        serialize=False,
                        primary_key=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("subject", models.CharField(max_length=100)),
                ("active", models.BooleanField(default=False)),
                ("file", models.CharField(max_length=100)),
                (
                    "couriers",
                    models.ManyToManyField(
                        related_name="surveys", blank=True, to="rest.Courier"
                    ),
                ),
            ],
        ),
    ]
