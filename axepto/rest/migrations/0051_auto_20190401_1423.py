# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-04-01 14:23
from __future__ import unicode_literals

from django.db import migrations


def create_default_customer(apps, schema_editor):
    Customer = apps.get_model("rest", "Customer")
    Document = apps.get_model("rest", "Document")
    o2_customer = Customer.objects.create(id="o2", name="O2", active=True)
    Document.objects.filter(customer__isnull=True).update(customer=o2_customer)


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0050_auto_20190401_1420"),
    ]

    operations = [
        migrations.RunPython(create_default_customer),
    ]
