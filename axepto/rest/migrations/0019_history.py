# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0018_quarantine_url_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="History",
            fields=[
                (
                    "id",
                    models.AutoField(
                        primary_key=True,
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("AUTO_ASSIGN", "Auto assign"),
                            ("ASSIGN", "Assign"),
                            ("DELETE_REQUEST", "Delete request"),
                            ("DELETE_OLD", "Delete bacouse of age"),
                        ],
                        default=None,
                        max_length=25,
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        null=True,
                        blank=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="history",
                        to="rest.Courier",
                    ),
                ),
            ],
        ),
    ]
