import base64
import logging
import uuid
from datetime import datetime

from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth import login as auth_login
from django.contrib.auth import logout as auth_logout
from django.contrib.auth.models import User
from django.core.files.storage import default_storage
from django.db import transaction
from django.db.models import Max, Q
from django.http import HttpResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.views.generic import View

from axepto.constants import (
    ACKNOWLEDGED,
    SIGNED_REMOTELY,
    permissions_groups,
    rest_permissions,
)
from axepto.fail2ban import can_user_login, is_banned
from axepto.helpers import (
    auth_type_required,
    json_response,
    set_cookie_validity,
    user_from_session_token,
)

from .actions import assign_document, mark_login, mark_unsigned_download
from .helpers import (
    clear_documents_handler,
    create_courier,
    download_document,
    download_file,
    download_log,
    download_package,
    get_list_line,
    load_json,
    log_document_id,
    save_survey_record,
)
from .library.upload_signed import upload_signed
from .library.upload_unsigned import upload_document
from .models import (
    Ban,
    Courier,
    Document,
    History,
    Log,
    LoginAttempt,
    Quarantine,
    Survey,
    SurveyRecord,
)
from .notifications import notify_logs
from .useful import id_to_logname

logger = logging.getLogger(__name__)


class MarkRemotelySignedView(View):
    @auth_type_required(rest_permissions["document_signed_post"])
    def post(self, request, **kwargs):
        idx = request.POST["id"]
        document = Document.objects.with_id(idx).first()
        if document and document.parent is None:
            document.signed_remotely = True
            document.save(update_fields=["signed_remotely"])
            History.objects.create(
                author=request.user,
                document=document,
                action=SIGNED_REMOTELY,
                auto=False,
            )
        return HttpResponse(status=200)


class ListSignedSinceView(View):
    @auth_type_required(rest_permissions["list_signed_since"])
    def get(self, request, timestamp):
        # this can be called by operators only
        return get_since_list(timestamp, True, request.user.operator.customer)


class ListUnsignedSinceView(View):
    @auth_type_required(rest_permissions["list_unsigned_since"])
    def get(self, request, timestamp):
        # this can be called by operators only
        return get_since_list(timestamp, False, request.user.operator.customer)


def get_since_list(timestamp, signed, customer):
    result_docs = []

    db_docs = (
        Document.objects.all()
        .active()
        .with_customer(customer)
        .exclude(timestamp__lt=timestamp)
    )

    if signed:
        db_docs = db_docs.exclude(parent=None)
    else:
        db_docs = db_docs.filter(parent=None)

    db_docs = db_docs.order_by("-timestamp")

    if len(db_docs) == 0:
        # There are no documents newer than specified timestamp
        max_db_ts = Document.objects
        if signed:
            max_db_ts = max_db_ts.exclude(parent=None)
        else:
            max_db_ts = max_db_ts.filter(parent=None)

        max_db_ts = max_db_ts.aggregate(Max("timestamp")).get("timestamp__max")
        if max_db_ts is None:
            # There are no entries in db
            max_timestamp = 0
        else:
            max_timestamp = max_db_ts
    else:
        max_timestamp = db_docs[0].timestamp
    for doc in db_docs:
        result_docs.append({"id": doc.id, "timestamp": doc.timestamp})
    return json_response(
        {"documents": result_docs, "next_timestamp": max_timestamp + 1}, status=200
    )


class DriverListAddView(View):
    @auth_type_required(rest_permissions["driver_list_add"])
    def post(self, request):
        if "id" not in request.POST:
            return json_response({"error": '"id" parameter is required'}, status=400)

        id = request.POST.get("id")

        document = Document.objects.signed(False).active(True).with_id(id).first()

        if document is None:
            return json_response(
                {"error": "Unsigned document with id %s does not exist" % id}, 400
            )

        if getattr(request.user, "courier", None):
            if not request.user.courier.documents.with_id(document.id).exists():
                assign_document(request.user, document, request.user, True)

            return json_response({"response": "Document assigned"}, 200)

        else:
            return json_response({"error": "User must be courier"}, 400)


class DriverListView(View):
    @auth_type_required(rest_permissions["driver_list"])
    def get(self, request, *args, **kwargs):
        courier_drlist = request.user.courier.get_driver_list()
        if len(courier_drlist) > 0:
            logger.warning(str(courier_drlist))
            logger.warning(
                str(request.user.courier.name) + " " + str(len(courier_drlist))
            )
        surveys_qs = Survey.objects.filter(
            Q(couriers__user_id=request.user.id)
            | Q(
                courier_centers=request.user.courier.center,
                courier_centers__isnull=False,
            ),
            active=True,
        )
        surveys = [
            {
                "id": it.identifier,
                "client_id": it.subject,
                "sender": "",
                "filename": it.filename,
                "url": request.build_absolute_uri(
                    reverse("document_unsigned_get", kwargs={"id": it.identifier})
                ),
                "text": it.subject,
            }
            for it in surveys_qs.distinct()
        ]

        version = "v2" if kwargs.get("version", "") == "v2" else "v1"
        docs = [
            {
                "id": doc.id,
                "client_id": doc.client_id if doc.client_id else "",
                "sender": doc.customer.name,
                "url": request.build_absolute_uri(
                    reverse(
                        "document_unsigned_get",
                        kwargs={"id": doc.id, "version": version},
                    )
                ),
                "filename": doc.id + ".zip",
                "text": get_list_line(doc.rest, doc.customer),
            }
            for doc in courier_drlist
        ]

        if version == "v2":
            return json_response(docs + surveys, 200)
        else:
            return json_response({"documents": docs + surveys}, 200)


class DocumentUnsignedAckView(View):
    @auth_type_required(rest_permissions["acknowledge_document"])
    @log_document_id
    def post(self, request, *args, **kwargs):
        if "id" in kwargs:
            idx = kwargs["id"]
        elif "id" in request.POST:
            idx = request.POST["id"]
        else:
            return json_response({"error": "id missing from url and body"}, status=400)
        document = Document.objects.with_id(idx).first()
        if document is None:
            return json_response(
                {"error": "Document with id {} does not exist".format(idx)}, status=404
            )
        History.objects.create(
            author=request.user,
            document=document,
            action=ACKNOWLEDGED,
            auto=False,
        )
        return json_response({"response": "acknowledged"}, status=200)


class HandleDocumentUnsignedView(View):
    @auth_type_required(rest_permissions["document_unsigned_post"])
    def post(self, request, *args, **kwargs):
        # Unsigned document is being uploaded
        return upload_document(request)

    @auth_type_required(rest_permissions["document_unsigned_get"])
    @log_document_id
    def get(self, request, *args, **kwargs):
        if "id" in kwargs:
            # Unsigned document from storage is requested
            return download_unsigned(
                request, kwargs["id"], kwargs.get("version", None) == "v2"
            )
        else:
            return json_response(
                {"error": "Improper use of route - GET with <id> in route "}, status=400
            )

    @auth_type_required(rest_permissions["document_unsigned_delete"])
    @log_document_id
    @transaction.atomic
    def delete(self, request, *args, **kwargs):
        return clear_unsigned(request, kwargs["id"])


class HandleDocumentSignedView(View):
    @auth_type_required(rest_permissions["document_signed_post"])
    @log_document_id
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        pk = (
            kwargs.get("id", None)
            if kwargs.get("id", None)
            else request.POST.get("id", None)
        )

        if pk is None:
            return json_response({"error": "id missing from url and body"}, status=400)

        return upload_signed(request, pk)

    @auth_type_required(rest_permissions["document_signed_get"])
    @log_document_id
    def get(self, request, *args, **kwargs):
        # Signed document is requested
        return download_signed(request, kwargs["id"])

    @auth_type_required(rest_permissions["document_signed_delete"])
    @log_document_id
    @transaction.atomic
    def delete(self, request, *args, **kwargs):
        return clear_signed(request, kwargs["id"])


def download_signed(request, id):
    docs = Document.objects.signed(True).with_id(id)
    # only manager and operator can access this, if operator
    # we have to check if package belongs to same customer as operator
    if not getattr(request.user, "manager", None):
        docs = docs.with_customer(request.user.operator.customer).filter(deleted=False)

    document = docs.first()

    if document is None:
        return json_response(
            {"error": "Signed document with id %s does not exist" % id}, 400
        )

    return download_document(document.id, False)


def download_unsigned(request, id, v2=False):
    docs = Document.objects.signed(False).active(True).with_id(id)

    if not getattr(request.user, "manager", None):
        docs = docs.has_children(False)

    if len(docs) == 0:
        if getattr(request.user, "courier", None):
            surveys = Survey.objects.filter(
                Q(couriers__user_id=request.user.id)
                | Q(
                    courier_centers=request.user.courier.center,
                    courier_centers__isnull=False,
                ),
                active=True,
                package__contains=id,
            ).first()
            if surveys is not None:
                return download_file(surveys.package.name, surveys.filename)
        return json_response(
            {"error": "Unsigned document with id %s does not exist" % id}, 400
        )

    logger.info(f"Document queryset length: {len(docs)}")
    document = docs[0]
    # Now there's proper document in variable document

    # Add download to history
    mark_unsigned_download(request.user, document)

    # If courier tries to download this document, it must be assigned
    if (
        getattr(request.user, "courier", None)
        and not request.user.courier.documents.with_id(document.id).exists()
    ):
        assign_document(request.user, document, request.user, True)

    if v2:
        return download_package(document.id_v2)
    return download_package(document.id)


def clear_signed(request, id):
    documents = (
        Document.objects.filter(Q(client_id=id) | Q(id=id) | Q(id_v2=id), deleted=False)
        .exclude(parent=None)
        .with_customer(request.user.operator.customer)
        .all()
    )
    return clear_documents_handler(request.user, documents)


def clear_unsigned(request, id):
    documents = (
        Document.objects.filter(Q(client_id=id) | Q(id=id), deleted=False, parent=None)
        .with_customer(request.user.operator.customer)
        .all()
    )
    return clear_documents_handler(request.user, documents)


class LoginView(View):
    def get(self, request):
        auth_header = request.META.get("HTTP_AUTHORIZATION", "")
        token_type, _, credentials = auth_header.partition(" ")

        if token_type == "Basic":
            string_credentials = base64.b64decode(credentials).decode()
            username, password = string_credentials.split(":")

            response = is_banned(request, username)
            if response:
                return response

            user = authenticate(username=username, password=password)
            if can_user_login(request, username, user):
                auth_login(request, user)
                set_cookie_validity(request)
                mark_login(user)

                return json_response({"response": "Logged in successfully"}, 200)
            else:
                return json_response({"response": "authentication failed"}, 401)
        else:
            return json_response({"error": "some parameters are missing"}, 400)

    def post(self, request):
        if ("username" in request.POST) and ("password" in request.POST):
            username = request.POST.get("username")
            password = request.POST.get("password")

            response = is_banned(request, username)
            if response:
                return response

            user = authenticate(username=username, password=password)
            if can_user_login(request, username, user):
                auth_login(request, user)
                set_cookie_validity(request, "rest")
                mark_login(user)

                return json_response({"response": "Logged in successfully"}, 200)
            else:
                return json_response({"response": "authentication failed"}, 401)
        else:
            return json_response({"error": "some parameters are missing"}, 400)


class LogoutView(View):
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            auth_logout(request)
            return json_response({"response": "Logged out."}, 200)
        else:
            return json_response({"response": "User was not logged in."}, 200)


class AssignView(View):
    def get_documents(self, json_list):
        return Document.objects.signed(False).active().with_ids(json_list)

    @auth_type_required(rest_permissions["assign"])
    @transaction.atomic
    def post(self, request):
        json_meta = load_json(request.body)

        if ("id" in json_meta) and ("documents" in json_meta):
            try:
                cour = Courier.objects.get(id=json_meta["id"])
            except Courier.DoesNotExist:
                return json_response(
                    {
                        "error": "The courier with id %s does not exist"
                        % json_meta["id"]
                    },
                    400,
                )
            except Courier.MultipleObjectsReturned:
                return json_response(
                    {"error": "Multiple couriers with id %s exist" % json_meta["id"]},
                    400,
                )

            only_cour = json_meta.get("only_courier", False)

            if ("operation" not in json_meta) or (
                json_meta["operation"] not in ["add"]
            ):
                cour.documents.clear()

            documents = self.get_documents(json_meta["documents"])
            new_assignments, new_histories = [], []

            for document in documents:
                assignment, history = assign_document(
                    cour.user, document, request.user, False, save=False
                )
                new_assignments.append(assignment)
                new_histories.append(history)

            if only_cour:
                Courier.documents.through.objects.filter(
                    document_id__in=list(doc.id for doc in documents)
                ).delete()

            Courier.documents.through.objects.bulk_create(new_assignments)
            History.objects.bulk_create(new_histories)

            return json_response({"result": "OK"}, 200)

        else:
            return json_response({"error": "id or documents not in POST"}, 400)


class Assign2View(AssignView):
    def get_documents(self, json_list):
        id_to_internal_id = {
            item["doc"]: item.get("cislo_zasielky", "")
            for item in json_list
            if "doc" in item
        }

        documents = (
            Document.objects.signed(False).active().with_ids(id_to_internal_id.keys())
        )
        documents_for_update = []
        for doc in documents:
            internal_id = (
                id_to_internal_id.get(doc.id)
                or id_to_internal_id.get(doc.id_v2)
                or id_to_internal_id.get(doc.client_id)
            )

            if internal_id:
                doc.internal_id = internal_id
                documents_for_update.append(doc)

        Document.objects.bulk_update(documents_for_update, ["internal_id"])
        return documents


class QuarantineView(View):
    @auth_type_required(rest_permissions["quarantine"])
    def get(self, request, id):
        q = Quarantine.objects.filter(id=id, deleted=False)
        if q.count() == 0:
            return json_response({"error": "File with id %s does not exist" % id}, 400)

        return download_package(id)


class WebQuarantineDeleteView(View):
    @auth_type_required(rest_permissions["quarantine_delete"])
    def dispatch(self, request, id):
        quarantine_document = Quarantine.objects.filter(id=id)[0]
        quarantine_document.remove()

        return redirect("quarantine_dashboard")


class LogsView(View):
    @auth_type_required(rest_permissions["logs_post"])
    def post(self, request):
        if "file" not in request.FILES:
            return json_response(
                {"error": '"file" parameter is required in request'}, status=400
            )

        if request.FILES["file"].name.split(".")[-1] != "zip":
            return json_response({"error": "Filetype not supported"}, 400)

        zip = request.FILES["file"]
        uid = uuid.uuid4()

        courier = None
        if getattr(request.user, "courier", None):
            courier = request.user.courier

        device_id = ""
        zip_name = ".".join(zip.name.split(".")[0:-1])
        split_name = zip_name.split("_")
        if len(split_name) >= 2:
            device_id = split_name[1]

        timestamp = 0
        if len(split_name) >= 3:
            ts = split_name[2]
            timestamp = int(ts) if ts.isdigit() else 0

        default_storage.save(id_to_logname(uid.hex), zip)

        # Save to database
        log = Log(
            id=uid.hex,
            timestamp=timestamp,
            file_name=zip_name,
            courier=courier,
            device_id=device_id,
            zip_size=zip.size,
        )

        log.save()

        notify_logs(log)

        return json_response({"id": uid.hex}, status=200)

    @auth_type_required(rest_permissions["logs_get"])
    def get(self, request, id):
        q = Log.objects.filter(id=id).first()

        if q is None:
            return json_response({"error": "File with id %s does not exist" % id}, 400)

        return download_log(q.id, q.file_name)


class SurveyView(View):
    def post(self, request, *args, **kwargs):
        try:
            if "application/json" not in request.content_type:
                raise ValueError("expected application/json content type")

            data = load_json(request.body)
            courier = Courier.objects.filter(
                user__username=data["courier_name"]
            ).first()
            survey = Survey.objects.filter(id=data["survey_id"], active=True).first()
            record = SurveyRecord.objects.active_record(courier, survey).first()

            authenticated_actions = ["pre", "post", "cancel"]
            if data["action"] in authenticated_actions:
                user = user_from_session_token(data["token"])
                if not courier or user.id != courier.user.id:
                    raise ValueError("invalid token for courier")

            if not survey:
                raise ValueError("survey does not exist")

            if not courier or (
                courier not in survey.couriers.all()
                and courier.center not in survey.courier_centers.all()
            ):
                raise ValueError("survey is not assigned to courier")

            if data["action"] == "pre":
                return self.pre_survey(data, survey, record, courier)
            elif data["action"] == "post":
                return self.post_survey(data, survey, record, courier)
            elif data["action"] == "cancel":
                return self.cancel(data, survey, record, courier)
            elif data["action"] == "check-in":
                return self.check_in(record)
            else:
                raise ValueError("Unknown action")

        except (KeyError, ValueError) as ex:
            return json_response({"error": str(ex)}, status=400)

    def pre_survey(self, data, survey, record, courier):
        if record:
            logger.warning("Called pre-survey with existing record.")
            record.close(accepted=False)
        save_survey_record(survey, courier, data)
        return json_response({"result": "OK"}, 200)

    def post_survey(self, data, survey, record, courier):
        if not record:
            raise ValueError("Opened survey record does not exist")
        if not record.finished:
            record.close(accepted=False)
            return json_response({"result": "OK"}, 200)
        record.data.update(data)
        record.close()
        return json_response({"result": "OK"}, 200)

    def cancel(self, data, survey, record, courier):
        if record:
            record.close(accepted=False)
            logger.error("Called cancel with existing record.")
        record = save_survey_record(survey, courier, data)
        record.close(accepted=False)
        return json_response({"result": "OK"}, 200)

    def check_in(self, record):
        record.finished = True
        record.save(update_fields=["finished"])
        return json_response({"result": "OK"}, 200)


class AuthView(View):
    @auth_type_required(rest_permissions["auth"])
    def get(self, request):
        return json_response(
            {"result": request.COOKIES[settings.SESSION_COOKIE_NAME]}, 200
        )


class RegisterView(View):
    @auth_type_required(rest_permissions["register"])
    def post(self, request, *args, **kwargs):
        error = []  # list containing error messages

        for field in ("username", "password", "id"):
            if field not in request.POST:
                error.append(f"{field} not specified")

        username = request.POST.get("username")
        password = request.POST.get("password")
        courier_id = request.POST.get("id")
        if User.objects.filter(username=username).count() != 0:
            error.append(f"User with username {username} already exists.")
        if Courier.objects.filter(id=courier_id).count() != 0:
            error.append(f"Courier with id {courier_id} already exists.")

        if error:
            return json_response({"error": error}, 400)

        create_courier(username, password, courier_id)
        return json_response({"result": "OK"}, 200)


class ChangePassword(View):
    @auth_type_required(rest_permissions["change_password"])
    def post(self, request, *args, **kwargs):
        error = []  # list containing error messages

        for field in ("username", "password"):
            if field not in request.POST:
                error.append(f"{field} not specified")

        username = request.POST.get("username")
        password = request.POST.get("password")
        courier = Courier.objects.filter(user__username=username).first()
        if not courier:
            error.append(f"Courier with username {username} does not exists.")

        if error:
            return json_response({"error": error}, 400)

        courier.user.set_password(password)
        courier.user.save(update_fields=["password"])
        return json_response({"result": "OK"}, 200)
