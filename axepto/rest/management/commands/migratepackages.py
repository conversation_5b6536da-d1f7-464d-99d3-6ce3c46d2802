import os
import zipfile

from django.conf import settings
from django.core.files import File
from django.core.files.storage import FileSystemStorage, default_storage
from django.core.management.base import BaseCommand

from axepto.rest.helpers import id_to_filename
from axepto.rest.models import Document


class Command(BaseCommand):
    def handle(self, *args, **options):
        documents = Document.objects.signed(False).filter(
            id_v2__isnull=False, deleted=False, hard_delete=False
        )
        print(len(documents))
        fs = FileSystemStorage(location="./documents-tmp/")
        for document in documents:
            print(document.id_v2, end="")
            handler = default_storage.open(id_to_filename(document.id_v2))
            fs.save(id_to_filename(document.id_v2), handler.file)
            with zipfile.ZipFile(
                "./documents-tmp/{}".format(id_to_filename(document.id_v2)), "r"
            ) as input_zip:
                with zipfile.ZipFile(
                    "./documents-tmp/n/{}".format(id_to_filename(document.id_v2)), "w"
                ) as output_zip:
                    for item in input_zip.infolist():
                        if item.filename != "index.html":
                            output_zip.writestr(item, input_zip.read(item.filename))

                    output_zip.write(
                        settings.ASSETS_DIR_V2 + "/index.html", "index.html"
                    )
            fs.delete(id_to_filename(document.id_v2))
            with open(
                "./documents-tmp/n/{}".format(id_to_filename(document.id_v2)), "rb"
            ) as new_zip:
                default_storage.save(id_to_filename(document.id_v2), File(new_zip))
            os.remove("./documents-tmp/n/{}".format(id_to_filename(document.id_v2)))
            print(".....done")
