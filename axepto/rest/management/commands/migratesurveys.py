from django.core.files import File
from django.core.files.storage import default_storage
from django.core.management.base import BaseCommand

from axepto.rest.helpers import id_to_filename
from axepto.rest.model.model_survey import Survey


class Command(BaseCommand):
    def handle(self, *args, **options):
        qs = Survey.objects.all()
        for item in qs:
            if item.file:
                filename = id_to_filename(item.file)
                print(filename)
                file_handler = default_storage.open(filename)
                item.package.save(filename, File(file_handler))
                default_storage.delete(filename)
                item.file = None
                item.save()
