from django.core.files.storage import default_storage
from django.core.management.base import BaseCommand

from axepto.rest.helpers import id_to_filename
from axepto.rest.models import Document


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "-y",
            "--yes",
            action="store_true",
            help="Don't ask for confirmation",
        )

    def handle(self, *args, **options):
        qs = Document.objects.filter(deleted=False, hard_delete=False)
        for item in qs:
            if not default_storage.exists(id_to_filename(item.pk)):

                confirmation = True
                if not options["yes"]:
                    self.stdout.write(
                        "Mark document {pk} as deleted? [y/N]:".format(pk=item.pk),
                        ending="",
                    )
                    confirmation = True if input() == "y" else False

                if confirmation:
                    item.deleted = True
                    item.hard_delete = True
                    item.save(update_fields=["deleted", "hard_delete"])
                    self.stdout.write(
                        "Document {pk} marked as deleted".format(pk=item.pk)
                    )
