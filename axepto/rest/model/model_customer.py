from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>teger<PERSON>ield, Model


class Customer(Model):
    # Works as directory name too
    id: CharField = Char<PERSON>ield(
        max_length=100,
        blank=True,
        unique=True,
        default="",
        primary_key=True,
        db_index=True,
    )

    shipper_id: Char<PERSON><PERSON> = Char<PERSON>ield(max_length=100, blank=True)
    """Customer's ID in the courier's system"""

    name: CharField = CharField(max_length=100, blank=True)

    old_unsigned_packages_removal_limit: IntegerField = IntegerField(default=40)
    """A day limit after which unsigned packages will be automatically deleted"""

    old_signed_packages_removal_limit: IntegerField = IntegerField(default=30)
    """A day limit after which signed packages will be automatically deleted"""

    old_quarantine_packages_removal_limit: IntegerField = IntegerField(default=30)
    """A day limit after which quarantine packages will be automatically deleted"""

    has_password_protected_packages: BooleanField = BooleanField(default=False)

    active: BooleanField = <PERSON><PERSON>anF<PERSON>(default=False)

    def __str__(self):
        return str(self.name)
