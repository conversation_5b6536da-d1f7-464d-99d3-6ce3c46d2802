import datetime
import os

from django.core.validators import MinValueValidator
from django.db.models import (
    SET_NULL,
    BigIntegerField,
    BooleanField,
    CharField,
    DateTimeField,
    FileField,
    ForeignKey,
    IntegerField,
    JSONField,
    ManyToManyField,
    Model,
    QuerySet,
    TextField,
    URLField,
)

from axepto.constants import WEB_DATETIME_FORMAT
from axepto.helpers import get_ba_tz
from axepto.rest.model.courier_center import CourierCenter


class Survey(Model):
    created_at: DateTimeField = DateTimeField(
        auto_now_add=True,
    )

    subject: CharField = CharField(
        max_length=100,
    )

    couriers: ManyToManyField = ManyToManyField(
        "Courier",
        related_name="surveys",
        blank=True,
    )

    courier_centers: ManyToManyField = ManyToManyField(
        CourierCenter,
        related_name="surveys",
        blank=True,
    )

    active: BooleanField = BooleanField(
        default=False,
    )

    repeating: IntegerField = IntegerField(
        default=None,
        null=True,
        blank=True,
        validators=[MinValueValidator(limit_value=1)],
    )

    description: TextField = TextField(
        null=True,
        blank=False,
        help_text="You can separate different points with end of the line.",
    )

    form_url: URLField = URLField(
        null=True,
        blank=False,
    )

    package: FileField = FileField(
        upload_to="surveys",
        null=True,
        blank=False,
    )

    def __str__(self):
        return str(self.subject)

    def get_date_created(self):
        return get_ba_tz(self.created_at).strftime(WEB_DATETIME_FORMAT)

    @property
    def identifier(self):
        return os.path.splitext(self.filename)[0]

    @property
    def filename(self):
        return os.path.basename(self.package.name)


class SurveyRecordQueryset(QuerySet):
    def active_record(self, courier, survey):
        return self.filter(courier=courier, survey=survey, closed_at=None)


class SurveyRecord(Model):
    objects = SurveyRecordQueryset.as_manager()

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    closed_at: DateTimeField = DateTimeField(
        null=True,
        blank=True,
        default=None,
    )

    courier: ForeignKey = ForeignKey(
        "Courier", on_delete=SET_NULL, blank=False, null=True
    )

    survey: ForeignKey = ForeignKey(
        "Survey", on_delete=SET_NULL, blank=False, null=True
    )

    timestamp: BigIntegerField = BigIntegerField(db_index=True, default=0)

    accepted: BooleanField = BooleanField(null=False, default=None)

    data: JSONField = JSONField(default=dict, null=True, blank=True)

    finished: BooleanField = BooleanField(null=False, default=False, blank=True)

    def get_time_formatted(self, dt=False):
        res = get_ba_tz(datetime.datetime.fromtimestamp(self.timestamp / 1000))

        if dt:
            # xlwt cant work with tz aware datetime
            return res.replace(tzinfo=None)

        return res.strftime(WEB_DATETIME_FORMAT)

    def close(self, accepted=True):
        self.accepted = accepted
        self.closed_at = datetime.datetime.now()
        self.save()
