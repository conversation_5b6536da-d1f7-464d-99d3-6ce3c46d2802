from django.db.models import <PERSON><PERSON><PERSON><PERSON>, IntegerField, Model


class CourierCenter(Model):
    center_id: IntegerField = IntegerField(
        verbose_name="Center ID",
        null=False,
        blank=False,
    )

    name: CharField = <PERSON>r<PERSON><PERSON>(
        verbose_name="Center name",
        max_length=100,
        null=False,
        blank=False,
    )

    code: CharField = CharField(
        verbose_name="Center code",
        max_length=2,
        null=False,
        blank=False,
    )

    def __str__(self):
        return "{0} ({1})".format(self.center_id, self.name)

    class Meta:
        verbose_name = "Courier center"
        verbose_name_plural = "Courier centers"
