from django.db.models import (
    SET_NULL,
    BigIntegerField,
    BooleanField,
    CharField,
    DateTimeField,
    ForeignKey,
    Model,
    TextField,
)

from axepto.constants import EMAIL_NOTIFICATION_KIND_CHOICES, EMAIL_NOTIFICATION_TYPES


class Notification(Model):

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    type: Char<PERSON>ield = Char<PERSON>ield(
        max_length=25, choices=EMAIL_NOTIFICATION_TYPES, default=None, null=True
    )

    kind: CharField = Char<PERSON>ield(
        max_length=25, choices=EMAIL_NOTIFICATION_KIND_CHOICES, default=None, null=True
    )

    sent: BooleanField = BooleanField(default=False)

    send_to_courier: BooleanField = BooleanField(default=False)

    subject: Char<PERSON>ield = CharField(max_length=100)

    text: TextField = TextField()

    value: BigIntegerField = BigIntegerField(default=0)

    document: ForeignKey = Foreign<PERSON>ey(
        "Document",
        related_name="notifications",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    courier: ForeignKey = ForeignKey(
        "Courier",
        related_name="notifications",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    def __str__(self):
        return str(self.kind)
