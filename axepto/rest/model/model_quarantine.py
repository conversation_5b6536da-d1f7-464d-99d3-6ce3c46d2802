import logging

from django.core.files.storage import default_storage
from django.db.models import (
    SET_NULL,
    BigIntegerField,
    BooleanField,
    CharField,
    ForeignKey,
    Model,
)
from humanfriendly import format_size

from axepto.constants import DELETE
from axepto.helpers import format_time
from axepto.rest.useful import id_to_filename

logger = logging.getLogger(__name__)


class Quarantine(Model):
    id: CharField = CharField(
        max_length=100,
        blank=True,
        unique=True,
        default="",
        primary_key=True,
        db_index=True,
    )

    timestamp: BigIntegerField = BigIntegerField(db_index=True, default=0)

    zip_size: BigIntegerField = BigIntegerField(db_index=True, default=0)

    url_id: CharField = CharField(max_length=100, blank=True, default="")

    courier: ForeignKey = ForeignKey(
        "Courier",
        related_name="quarantine_items",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    customer: ForeignKey = ForeignKey(
        "Customer",
        null=True,
        blank=True,
        related_name="quarantine",
        on_delete=SET_NULL,
    )

    deleted: BooleanField = BooleanField(default=False)

    def get_date(self):
        return format_time(self.timestamp)

    def get_display_size(self):
        return format_size(self.zip_size)

    def remove(self):
        logger.info(f"[{DELETE}] DELETING QUARANTINE DOCUMENT {self.id}")
        default_storage.delete(id_to_filename(self.id))

        self.deleted = True
        self.save(update_fields=["deleted"])

    def __str__(self):
        return self.id
