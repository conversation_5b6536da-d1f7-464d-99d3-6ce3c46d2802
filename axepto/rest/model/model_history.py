import datetime

from django.contrib.auth.models import User
from django.db.models import (
    SET_NULL,
    BooleanField,
    CharField,
    DateTimeField,
    ForeignKey,
    Model,
    QuerySet,
)

from axepto.constants import ACTION_CHOICES, ACTION_TRANS, WEB_DATETIME_FORMAT
from axepto.helpers import format_time, get_ba_tz


class HistoryQuerySet(QuerySet):
    def since(self, value):
        return self.filter(created_at__gte=value)

    def until(self, value):
        return self.filter(created_at__lte=value)


class History(Model):
    objects = HistoryQuerySet.as_manager()

    author: ForeignKey = ForeignKey(
        User,
        related_name="history_author",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    user: ForeignKey = ForeignKey(
        User,
        related_name="history_user",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    document: ForeignKey = ForeignKey(
        "Document",
        related_name="history",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    action: CharField = CharField(max_length=25, choices=ACTION_CHOICES, default=None)

    auto: BooleanField = BooleanField(blank=True, null=True)

    def get_action(self):
        return ACTION_TRANS[self.action] if self.action in ACTION_TRANS else ""

    def get_date_created(self):
        return get_ba_tz(self.created_at).strftime(WEB_DATETIME_FORMAT)

    def __str__(self):
        return self.action
