import datetime

from django.db.models import (
    SET_NULL,
    BigIntegerField,
    CharField,
    DateTimeField,
    ForeignKey,
    Model,
)
from humanfriendly import format_size

from axepto.constants import WEB_DATETIME_FORMAT
from axepto.helpers import format_time, get_ba_tz


class Log(Model):

    id: Char<PERSON>ield = CharField(
        max_length=100,
        blank=True,
        unique=True,
        default="",
        primary_key=True,
        db_index=True,
    )

    timestamp: BigIntegerField = BigIntegerField(db_index=True, default=0)

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    zip_size: BigIntegerField = BigIntegerField(db_index=True, default=0)

    device_id: CharField = CharField(max_length=100, blank=True, default="")

    file_name: CharField = CharField(max_length=100, blank=True, default="")

    courier: ForeignKey = ForeignKey(
        "Courier", related_name="logs", on_delete=SET_NULL, blank=True, null=True
    )

    def get_date(self):
        return format_time(self.timestamp)

    def get_date_created(self):
        return get_ba_tz(self.created_at).strftime(WEB_DATETIME_FORMAT)

    def get_display_size(self):
        return format_size(self.zip_size)

    def __str__(self):
        return str(self.id)
