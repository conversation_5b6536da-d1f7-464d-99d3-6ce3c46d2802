import io
import zipfile
from io import By<PERSON><PERSON>
from typing import Any, Dict

from django.core.files.base import File


def extract_zip(input_zip: File) -> Dict[str, Any]:
    input_zip = zipfile.ZipFile(input_zip, mode="r", allowZip64=False)
    return {name: input_zip.read(name) for name in input_zip.namelist()}


def list_zip_files(input_zip: bytes) -> list[str]:
    zip_file = zipfile.ZipFile(io.BytesIO(input_zip), mode="r", allowZip64=False)
    return zip_file.namelist()


def is_zip_password_protected(input_zip: bytes) -> bool:
    zip_file = zipfile.ZipFile(io.BytesIO(input_zip), mode="r", allowZip64=False)

    # check if zip file includes encrypted flag
    for zip_info in zip_file.infolist():
        if zip_info.flag_bits & 0x1:
            return True

    return False


class InMemoryZip(object):
    def __init__(self, **kwargs):
        # Create the in-memory file-like object
        self.im_zip = kwargs["zip_file"] if "zip_file" in kwargs else BytesIO()

    def append_file(self, filename, filename_in_zip):
        zf = zipfile.ZipFile(
            self.im_zip, mode="a", compression=zipfile.ZIP_STORED, allowZip64=False
        )
        zf.write(filename, arcname=filename_in_zip)
        zf.close()

    def append_str(self, arcname, str_data):
        zf = zipfile.ZipFile(
            self.im_zip, mode="a", compression=zipfile.ZIP_STORED, allowZip64=False
        )
        zf.writestr(arcname, str_data)
        zf.close()

    def get_size(self):
        return self.im_zip.getbuffer().nbytes
