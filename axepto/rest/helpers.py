import json
import logging
import random
import re
import string
import time
import uuid
from datetime import datetime, timedelta
from os.path import join as pjoin
from typing import Any, Dict, Optional

import sentry_sdk  # type: ignore
from django.conf import settings
from django.contrib.auth.models import User
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.storage import default_storage
from django.db import IntegrityError, transaction
from django.db.models import Max
from django.http import HttpResponse

from axepto.helpers import json_response
from axepto.rest.library.unsigned_helpers import MissingFile, UploadException
from axepto.rest.models import Courier, Document, Quarantine, SurveyRecord, get_field
from axepto.storages import store_get_wrapper

from ..constants import ALLOWED_FILES_IN_DOCUMENT_ZIP
from .actions import delete_document
from .model.model_customer import Customer
from .useful import id_to_filename, id_to_logname
from .zip_tools import InMemoryZip, extract_zip, list_zip_files

logger = logging.getLogger(__name__)


def default_client_id(json_):
    if "client_id" in json_:
        return json_["client_id"]
    else:
        return ""


def get_client_id(json_meta: Dict[str, Any], customer: Customer) -> str:
    # this is only for transition faze and should be deleted
    if customer.id == "o2":
        client_id = json_meta["client_id"]
        client_id = client_id.split("_")
        if len(client_id) >= 2:
            return client_id[1]

    customers = ["telekom", "orange", "mzone", "union", "o2", "digi"]

    if customer.id in customers:
        return json_meta["client_id"]

    raise KeyError("unknown customer")


def invalid_metadata_response():
    return json_response({"error": "metadata.json is not a valid json"}, 400)


def get_random_string(n):
    seq = string.ascii_uppercase + string.digits
    arr = [random.choice(seq) for _ in range(n)]
    return "".join(arr)


def load_json(data):
    return json.loads(data.decode("utf-8"))


def read_metadata(extracted_zip: Dict[str, Any]) -> Dict[str, Any]:
    metadata_filename = "metadata.json"

    try:
        return load_json(extracted_zip[metadata_filename])
    except KeyError:
        raise MissingFile("metadata.json")


def read_tracking_data(extracted_zip):
    tracking_filename = "tracking.json"
    json_data = {}

    if tracking_filename in extracted_zip:
        json_data = load_json(extracted_zip[tracking_filename])
    return json_data


def extract_package(zip_file):
    reduced_zip = InMemoryZip()
    extracted_zip = extract_zip(zip_file)

    for file_name, file_content in extracted_zip.items():
        if "tracking.json" != file_name:
            reduced_zip.append_str(file_name, file_content)

    return [
        read_metadata(extracted_zip),
        read_tracking_data(extracted_zip),
        reduced_zip,
        extracted_zip,
    ]


def get_list_line(rest, customer):
    return ", ".join(
        get_field(field, rest, customer)
        for field in ["fullname", "address", "wizard-title"]
    )


def get_metadata_line(json_meta, customer):
    meta_html = open(settings.ASSETS_DIR + "/metadata.html", "r").read()
    for field in ["fullname", "address", "wizard-title"]:
        meta_html = meta_html.replace(
            "@@inject-" + field, get_field(field, json_meta, customer)
        )
    return meta_html


def download_package(id):
    filename = id_to_filename(id)
    return download_file(filename)


def download_log(id, rename=None):
    filename = id_to_logname(id)
    return download_file(filename, rename)


def download_file(filename, rename=None):
    # download from S3 to a temporary file, get its wrapper and size
    wrapperfile = store_get_wrapper(filename)
    if wrapperfile:
        # set headers appropriately
        response = HttpResponse(
            wrapperfile["wrapper"], content_type=wrapperfile["type"]
        )
        response["Content-Length"] = wrapperfile["size"]
        response["Content-Disposition"] = "attachment; filename=%s" % (
            rename if rename else filename
        )
    else:
        response = json_response({"error": "The document does not exist"}, status=400)
    return response


def download_document(id, is_unsigned):
    try:
        # Test existence
        doc = Document.objects.get(id=id, deleted=False)
        if doc.is_unsigned() == is_unsigned:
            return download_package(id)
        else:
            raise Document.DoesNotExist("The document does not exist")
    except Document.DoesNotExist as e:
        return json_response({"error": str(e)}, status=400)


@transaction.atomic
def save_on_newest_timestamp(document: Document) -> Document:
    # timestamp in milliseconds
    ts = int(time.time() * 1000)
    try:
        highest_ts = (
            Document.objects.all().aggregate(Max("timestamp")).get("timestamp__max")
        )
    except ObjectDoesNotExist:
        highest_ts = ts

    # new timestamp is the larger value
    # from timestamp and highest timestamp in db +1
    document.timestamp = ts if ts > highest_ts else highest_ts + 1
    try:
        document.save()
    except IntegrityError:
        raise UploadException(
            "Document with client ID {} already exists".format(document.client_id)
        )

    return document


# removes personal data and sets delete to true
def remove_documents(
    documents: list[Document],
    auto: bool,
    user: Optional[User] = None,
) -> None:
    for document in documents:
        delete_document(document, user, auto)


def clear_documents_handler(user, documents):
    if len(documents) == 0:
        return json_response({"response": "Documents not found"}, status=400)

    remove_documents(documents, False, user)

    return json_response({"response": "Deleted successfully"}, status=200)


def remove_old_unsigned_documents(customers: list[Customer]) -> None:
    """Delete unsigned documents that are older than n-days"""
    now = datetime.now()

    for customer in customers:
        past = int(
            (
                now - timedelta(days=customer.old_unsigned_packages_removal_limit)
            ).timestamp()
            * 1000
        )
        old_documents = (
            Document.objects.with_customer(customer).signed(False).active().until(past)
        )
        remove_documents(old_documents, True)


def clean_driver_list():
    couriers = Courier.objects.all()
    for cour in couriers:
        cour.documents.clear()
        cour.save()


def get_version(customer):
    version = ""
    with open(pjoin(settings.ASSETS_DIR, customer.id, "wizard_data.json"), "r") as f:
        js = json.load(f)
        if "version" in js:
            version = js["version"]

    return version


def add_to_quarantine(
    zip_file: InMemoryZip,
    id: str,
    courier: Courier,
    customer: Optional[Customer] = None,
) -> Quarantine:
    uid = uuid.uuid4()
    ts = int(time.time() * 1000)

    zip_size = zip_file.get_size()

    default_storage.save(id_to_filename(uid.hex), zip_file.im_zip)

    q = Quarantine(
        id=uid.hex,
        url_id=id,
        timestamp=ts,
        zip_size=zip_size,
        courier=courier,
        customer=customer,
    )

    q.save()

    return q


@transaction.atomic
def save_survey_record(survey, courier, data):
    sur_rep = survey.repeating
    if sur_rep:
        records_already = SurveyRecord.objects.filter(
            courier=courier, survey=survey
        ).count()
        if records_already >= sur_rep:
            raise ValueError("courier already reached survey records limit")
        elif records_already + 1 >= sur_rep:
            survey.couriers.remove(courier)

    record = SurveyRecord(
        courier=courier,
        survey=survey,
        data=data,
        accepted=data["accepted"],
        timestamp=data["timestamp"],
    )
    record.save()

    return record


def log_id(id: str):
    logger.info(f"Handling document with id {id}")


def log_document_id(func):
    def inner(self, request, *args, **kwargs):
        if "id" in kwargs:
            log_id(kwargs["id"])
        elif "id" in request.POST:
            log_id(request.POST["id"])

        return func(self, request, *args, **kwargs)

    return inner


def create_courier(username: str, password: str, courier_id: str) -> None:
    user = User.objects.create_user(username, password=password)
    courier = Courier(user=user, id=courier_id)
    courier.save()
    courier.assign_center()


class InvalidZipFile(Exception):
    pass


def _check_for_extra_files(
    filenames: list[str], regex_filter: re.Pattern[str], document_id: str
) -> None:
    for filename in filenames:
        if regex_filter.fullmatch(filename) is None:
            raise InvalidZipFile("invalid zip file")


def _check_for_missing_files(
    filenames: list[str], required_files: tuple[str, ...] | list[str], document_id: str
) -> None:
    for filename in required_files:
        if filename not in filenames:
            raise MissingFile(filename)


def validate_package_files(
    ext_zip: Dict[str, Any],
    regex_filter: re.Pattern[str],
    required_files: tuple[str, ...] | list[str],
    json_meta: Dict[str, Any],
    document_prefix: str = "",
) -> None:
    zip_files = list(ext_zip.keys())
    _check_for_extra_files(zip_files, regex_filter, json_meta["client_id"])
    _check_for_missing_files(zip_files, required_files, json_meta["client_id"])

    regex_str = ALLOWED_FILES_IN_DOCUMENT_ZIP.format(r".*\.pdf")
    required_documents = []
    if "documents_info" in json_meta:
        required_documents = [
            f"{document_prefix}{document['file']}"
            for document in json_meta["documents_info"]
            if document["required"]
        ]
        regex_str = ALLOWED_FILES_IN_DOCUMENT_ZIP.format(
            "|".join(
                [
                    f"(?:signed_)?{re.escape(document['file'])}"
                    for document in json_meta["documents_info"]
                ]
            )
        )

    zip_files = list_zip_files(ext_zip["document.zip"])
    _check_for_extra_files(zip_files, re.compile(regex_str), json_meta["client_id"])
    _check_for_missing_files(zip_files, required_documents, json_meta["client_id"])
