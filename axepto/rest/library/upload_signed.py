import logging
import uuid
from typing import Optional

import sentry_sdk  # type: ignore
from django.conf import settings
from django.core.files.storage import default_storage
from django.http import HttpRequest, HttpResponse

from axepto.audit.helpers import add_audit_entry
from axepto.constants import (
    ALLOWED_FILES_IN_SIGNED_DOCUMENTS,
    FILENAME_VALIDATION_PATTERN,
    REQUIRED_FILES_IN_SIGNED_DOCUMENTS,
)
from axepto.helpers import json_response
from axepto.rest.helpers import (
    InvalidZipFile,
    add_to_quarantine,
    default_client_id,
    extract_package,
    id_to_filename,
    invalid_metadata_response,
    save_on_newest_timestamp,
    validate_package_files,
)
from axepto.rest.library.unsigned_helpers import MissingFile
from axepto.rest.models import Document
from axepto.rest.notifications import (
    notify_if_delayed,
    notify_if_deleted,
    notify_if_small,
    notify_if_twice,
    notify_long_signing,
    notify_quarantine,
    notify_time_tablet,
)
from axepto.rest.zip_tools import is_zip_password_protected

logger = logging.getLogger(__name__)


def check_file(request: HttpRequest) -> str | None:
    if not getattr(request.user, "courier", None):
        return "Not logged in as courier"
    if "file" in request.FILES:
        if FILENAME_VALIDATION_PATTERN.fullmatch(request.FILES["file"].name) is None:
            return (
                f"Filename {request.FILES['file'].name} is invalid. "
                f"Correct format: [a-zA-Z0-9]+.zip"
            )

    elif "filedata" in request.FILES:
        if (
            FILENAME_VALIDATION_PATTERN.fullmatch(request.FILES["filedata"].name)
            is None
        ):
            return (
                f"Filename {request.FILES['filedata'].name} is invalid. "
                f"Correct format: [a-zA-Z0-9]+.zip"
            )
    else:
        return '"file" parameter is required in request'
    return None


def upload_signed(request: HttpRequest, id: str) -> HttpResponse:
    check = check_file(request)
    if check:
        return json_response({"error": check}, status=400)

    if "file" in request.FILES:
        uploaded_zip = request.FILES["file"]
    else:
        uploaded_zip = request.FILES["filedata"]

    try:
        [json_meta, tracking, reduced_zip, ext_zip] = extract_package(uploaded_zip)

    except Exception:
        return invalid_metadata_response()

    error = json_meta["error"] if "error" in json_meta else {}

    client_id = default_client_id(json_meta)

    uid = uuid.uuid4()

    document = (
        Document.objects.select_for_update()
        .signed(False)
        .with_id(id)
        .order_by("deleted")
        .first()
    )

    if document is None:
        qo = add_to_quarantine(reduced_zip, id, request.user.courier)
        notify_quarantine(qo)
        return json_response(
            {
                "error": "Unsigned document with id %s does not exist. Document moved "
                "to quarantine." % id
            },
            status=200,
        )

    try:
        validate_package_files(
            ext_zip,
            ALLOWED_FILES_IN_SIGNED_DOCUMENTS,
            REQUIRED_FILES_IN_SIGNED_DOCUMENTS,
            json_meta,
            "signed_" if document.customer.name == "O2" else "",
        )

        if (
            is_zip_password_protected(ext_zip["document.zip"])
            != document.customer.has_password_protected_packages
        ):
            raise InvalidZipFile("invalid zip file")

    except (InvalidZipFile, MissingFile) as e:
        return json_response({"error": str(e)}, 400)

    if document.children.count() > 0:  # if it has children already
        qo = add_to_quarantine(reduced_zip, id, request.user.courier, document.customer)
        notify_if_twice(qo)
        return json_response(
            {
                "error": "Document with id %s is already signed. Document moved to "
                "quarantine." % id
            },
            status=200,
        )

    document.courier_set.clear()
    document.save()

    tracking = add_audit_entry(tracking, request.user.username, document.customer.name)

    zip_size = reduced_zip.get_size()

    default_storage.save(id_to_filename(uid.hex), reduced_zip.im_zip)

    doc = Document(
        id=uid.hex,
        client_id=client_id,
        rest=json_meta,
        tracking=tracking,
        error=error,
        parent=document,
        author=request.user.courier,
        customer=document.customer,
        original_file_name=document.original_file_name,
        zip_size=zip_size,
        signed_locally=True,
    )

    # Save to database
    doc = save_on_newest_timestamp(doc)

    wizard_start = doc.get_data("tracking", "wizard-start")  # ts
    wizard_close = doc.get_data("tracking", "wizard-close")  # ts
    recieved = doc.timestamp

    notify_if_small(doc)
    notify_if_deleted(doc)
    # we may not have this value as most apps curently dont support this feature
    if wizard_start:
        notify_if_delayed(wizard_close, recieved, doc)
    if wizard_close:
        notify_time_tablet(wizard_close, recieved, doc)
    if wizard_start and wizard_close:
        notify_long_signing(wizard_start, wizard_close, doc)
    return json_response({"id": uid.hex}, status=200)
