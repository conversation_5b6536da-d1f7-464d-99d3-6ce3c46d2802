import json
import os
from base64 import b64decode
from os.path import isdir, isfile
from os.path import join as pjoin
from typing import Any, Dict

from django.conf import settings

from axepto.rest.model.model_customer import Customer
from axepto.rest.models import get_field
from axepto.rest.zip_tools import InMemoryZip

# To stay compatible with old for a while not removing 'SignDoc4ADS-public'
parcel_assets = [
    "signing.properties",
    "index.html",
    "wizard_data.json",
]


class UploadException(Exception):
    pass


class InvalidFilename(UploadException):
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return str(self.value)


class MissingFile(UploadException):
    def __init__(self, value):
        self.message = 'Missing file "%s"' % value

    def __str__(self):
        return self.message


class MissingAssets(UploadException):
    def __init__(self, value=""):
        self.message = "Missing some of assets contact Admin, %s" % value

    def __str__(self):
        return self.message


def get_metadata_line(json_meta: Dict[str, Any], customer: Customer) -> str:
    template_path = pjoin(settings.ASSETS_DIR, "metadata.html")

    with open(template_path, "r") as f:
        meta_html = f.read()

    for field in ["fullname", "address", "wizard-title"]:
        meta_html = meta_html.replace(
            "@@inject-" + field, get_field(field, json_meta, customer)
        )

    return meta_html


def check_common_assets(assets_path: str) -> None:
    if not isfile(pjoin(assets_path, "metadata.html")):
        raise MissingAssets("common")


def check_customer_assets(customer_assets_path: str) -> None:
    if not isdir(customer_assets_path):
        raise MissingAssets("customer dir")

    for file in parcel_assets:
        path = pjoin(customer_assets_path, file)
        if not isfile(path):
            raise MissingAssets("customer file %s" % path)


def check_assets(assets_path: str, customer: Customer) -> None:
    check_common_assets(assets_path)
    customer_assets_dir = pjoin(assets_path, customer.id)
    check_customer_assets(customer_assets_dir)


def generate_unsigned_zip(
    json_meta: Dict[str, Any],
    customer: Customer,
    zip: Dict[str, bytes],
    v2: bool = False,
) -> InMemoryZip:
    assets_path = settings.ASSETS_DIR_V2 if v2 else settings.ASSETS_DIR
    check_assets(assets_path, customer)
    customer_assets_dir = pjoin(assets_path, customer.id)

    # everything ok so far, lets create zip
    imz = InMemoryZip()
    imz.append_str("metadata.json", json.dumps(json_meta))
    imz.append_str("metadata.html", get_metadata_line(json_meta, customer))

    try:
        imz.append_str("document.zip", zip["document.zip"])
    except KeyError:
        raise MissingFile("document.zip")

    for file in parcel_assets:
        imz.append_file(pjoin(customer_assets_dir, file), file)

    certificate = os.getenv(f"DOCUMENT_SIGN_CERTIFICATE_{customer.name.upper()}")
    if not certificate:
        raise MissingAssets("customer certificate")
    imz.append_str("public.der", b64decode(certificate))

    return imz
