import logging
import uuid
from copy import deepcopy
from json import JSONDecodeError
from typing import Any, Dict, Optional
from zipfile import BadZipFile

import jsonschema
import sentry_sdk  # type: ignore
from django.core.files.storage import default_storage
from django.http import HttpRequest, HttpResponse

from axepto.helpers import json_response
from axepto.rest.helpers import (
    InvalidZipFile,
    MissingFile,
    get_client_id,
    get_version,
    log_id,
    read_metadata,
    save_on_newest_timestamp,
    validate_package_files,
)
from axepto.rest.model.model_customer import Customer
from axepto.rest.models import Document
from axepto.rest.useful import id_to_filename
from axepto.rest.zip_tools import extract_zip, is_zip_password_protected
from axepto.schemas import get_customer_schema

from ...constants import (
    ALLOWED_FILES_IN_UNSIGNED_DOCUMENTS,
    FILENAME_VALIDATION_PATTERN,
    REQUIRED_FILES_IN_UNSIGNED_DOCUMENTS,
)
from .unsigned_helpers import (
    InvalidFilename,
    MissingAssets,
    UploadException,
    generate_unsigned_zip,
)

# we need to use uwsgi import only when application is in uwsgi
try:
    import uwsgi
except ImportError:
    print("uwsgi import error")

logger = logging.getLogger(__name__)


def validate_data(data_: Dict[str, Any], schema_: Dict[str, Any]) -> None:
    """Validates data against given schema and raises
    jsonschema.ValidationError in case of invalid data
    """
    jsonschema.Draft3Validator(schema_).validate(data_)


def process_zip_request(request: HttpRequest) -> Dict[str, Any]:
    try:
        zip_file = request.FILES["file"]
    except KeyError:
        raise MissingFile("package.zip")

    if FILENAME_VALIDATION_PATTERN.fullmatch(zip_file.name) is None:
        raise InvalidFilename(
            f"Filename {zip_file.name} is invalid. Correct format: [a-zA-Z0-9]+.zip"
        )

    ext_zip = extract_zip(zip_file)

    json_meta = read_metadata(ext_zip)

    validate_data(json_meta, get_customer_schema(request.user.operator.customer))

    validate_package_files(
        ext_zip,
        ALLOWED_FILES_IN_UNSIGNED_DOCUMENTS,
        REQUIRED_FILES_IN_UNSIGNED_DOCUMENTS,
        json_meta,
    )
    if (
        is_zip_password_protected(ext_zip["document.zip"])
        != request.user.operator.customer.has_password_protected_packages
    ):
        raise InvalidZipFile("invalid zip file")

    return {"name": zip_file.name, "json_meta": json_meta, "ext_zip": ext_zip}


def post_process(
    id: str,
    id_v2: str,
    client_id: str,
    rest: Dict[str, Any],
    original_file_name: str,
    zip_size: int = 0,
    customer: Optional[Customer] = None,
) -> HttpResponse:
    try:
        uwsgi.lock()
    except Exception:
        pass

    try:
        save_on_newest_timestamp(
            Document(
                id=id,
                id_v2=id_v2,
                client_id=client_id,
                rest=rest,
                version=get_version(customer),
                original_file_name=original_file_name,
                zip_size=zip_size,
                customer=customer,
            )
        )

    finally:
        try:
            uwsgi.unlock()
        except Exception:
            pass

    return json_response({"id": id}, status=200)


def upload_document(request: HttpRequest) -> HttpResponse:
    try:
        data = process_zip_request(request)
    except UnicodeDecodeError:
        return json_response({"error": "metadata.json is not utf-8 encoded"}, 400)
    except JSONDecodeError as e:
        return json_response({"error": f"metadata.json decode error: {str(e)}"}, 400)
    except jsonschema.ValidationError as e:
        return json_response(
            {"error": f"{'.'.join([str(key) for key in e.path])}: {e.message}"}, 400
        )
    except ValueError:
        return json_response({"error": "invalid zip file"}, 400)
    except (MissingFile, BadZipFile, InvalidFilename, InvalidZipFile) as e:
        return json_response({"error": str(e)}, 400)

    uid = uuid.uuid4()
    uid_v2 = uuid.uuid4()

    try:
        client_id = get_client_id(data["json_meta"], request.user.operator.customer)
    except KeyError as e:
        return json_response({"error": str(e)}, 400)

    log_id(client_id)

    rest = deepcopy(data["json_meta"])

    data["json_meta"]["parent_id"] = uid.hex

    try:
        zip = data.get("ext_zip", {})
        zip_file = generate_unsigned_zip(
            data["json_meta"], request.user.operator.customer, zip
        )

        zip_file_v2 = generate_unsigned_zip(
            data["json_meta"],
            request.user.operator.customer,
            zip,
            v2=True,
        )

    except (MissingAssets, MissingFile) as e:
        return json_response({"error": str(e)}, status=400)

    try:
        response = post_process(
            uid.hex,
            uid_v2.hex,
            client_id,
            rest,
            data["name"],
            zip_file.get_size(),
            request.user.operator.customer,
        )

        default_storage.save(id_to_filename(uid.hex), zip_file.im_zip)

        default_storage.save(id_to_filename(uid_v2.hex), zip_file_v2.im_zip)

        return response

    except UploadException as e:
        return json_response({"error": str(e)}, 400)
