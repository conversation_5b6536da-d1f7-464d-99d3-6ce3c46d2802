import base64
import csv
import json
import os
from copy import deepcopy
from datetime import datetime, timedelta
from typing import Any

from django.conf import settings
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from setty import config

from axepto.audit.helpers import create_exports
from axepto.audit.models import AuditEntry, AuditExport
from axepto.library.tasks import (
    remove_old_quarantine_documents,
    remove_old_signed_documents,
)
from axepto.rest.helpers import remove_old_unsigned_documents
from axepto.rest.models import (
    Ban,
    Client,
    Courier,
    Customer,
    Document,
    Log,
    Manager,
    Operator,
    Quarantine,
)
from axepto.rest.zip_tools import InMemoryZip


class ApiTest(TestCase):
    # TODO: `o2`, `telekom` must match keys from `schemas.py`,
    # make them constants and import them
    def setUp(self):
        # Note: `o2` customer is already created using migration
        self.customer1 = Customer.objects.filter(id="o2")[0]
        self.customer2 = Customer.objects.create(
            id="telekom",
            name="telekom",
            active=True,
            has_password_protected_packages=True,
        )
        self.customer3 = Customer.objects.create(
            id="orange",
            name="orange",
            active=True,
            has_password_protected_packages=True,
        )
        self.customer4 = Customer.objects.create(id="mzone", name="mzone", active=True)
        self.customer5 = Customer.objects.create(
            id="union", name="union", active=True, has_password_protected_packages=True
        )

        self.operator1 = Operator.objects.create(
            user=User.objects.create_user(username="operator1", password="pass"),
            customer=self.customer1,
        )

        self.operator2 = Operator.objects.create(
            user=User.objects.create_user(username="operator2", password="pass"),
            customer=self.customer2,
        )

        self.operator3 = Operator.objects.create(
            user=User.objects.create_user(username="operator3", password="pass"),
            customer=self.customer3,
        )

        self.operator4 = Operator.objects.create(
            user=User.objects.create_user(username="operator4", password="pass"),
            customer=self.customer4,
        )

        self.operator5 = Operator.objects.create(
            user=User.objects.create_user(username="operator5", password="pass"),
            customer=self.customer5,
        )

        self.courier1 = Courier.objects.create(
            user=User.objects.create_user(username="courier1", password="pass"),
            id="courier1",
        )
        self.courier2 = Courier.objects.create(
            user=User.objects.create_user(username="courier2", password="pass"),
            id="courier2",
        )
        self.courier3 = Courier.objects.create(
            user=User.objects.create_user(username="courier3", password="pass"),
            id="courier3",
        )
        self.client1 = Client.objects.create(
            user=User.objects.create_user(username="client", password="pass")
        )
        self.manager = Manager.objects.create(
            user=User.objects.create_user(username="manager", password="pass")
        )

        self.rest_template: dict[str, Any] = {
            "o2": {
                "client_id": "Ro_07fb7efc-1a8f-11ea-8901-b4b676cf4cb1",
                "documents_info": [
                    {
                        "title": "Zmluva 1",
                        "fileId": "ID123",
                        "file": "document1.pdf",
                        "required": True,
                    },
                    {
                        "title": "Zmluva 2",
                        "fileId": "ID321",
                        "file": "document2.pdf",
                        "required": False,
                    },
                ],
                "type": "Zmluva",
                "zakaznik_meno": "Jožko",
                "zakaznik_priezvisko": "Mrkvička",
                "zakaznik_ulica": "Malinova",
                "zakaznik_cislo_domu": "23",
                "zakaznik_psc": "012 34",
                "zakaznik_mesto": "Bratislava",
                "error_info": [],
                "scans_info": {},
            },
            "telekom": {
                "documents_info": [
                    {
                        "title": "Zmluva 1",
                        "fileId": "ID123",
                        "file": "document1.pdf",
                        "required": True,
                    },
                    {
                        "title": "Zmluva 2",
                        "fileId": "ID321",
                        "file": "document2.pdf",
                        "required": False,
                    },
                ],
                "type": "Nova zmluva",
                "client_id": "P2432342",
                "zakaznik_meno": "Jožko",
                "zakaznik_priezvisko": "Mrkvička",
                "zakaznik_ulica": "Malinova",
                "zakaznik_cislo_domu": "23",
                "zakaznik_psc": "012 34",
                "zakaznik_mesto": "NYC",
                "shipment_id": "SID1234",
                "customer_id": "CID123",
                "orders": ["OID1", "OID2"],
                "packages": ["PID1", "PID1"],
                "customer_type": "Residential",
                "identification_type": "Rozšírená",
                "shipping_name": "Best Company s.r.o.",
                "contact_number": "0902 123 456",
                "company_id_number": "*********",
            },
            "orange": {
                "client_id": "Ro_07fb7efc-1a8f-11ea-8901-b4b676cf4cb1",
                "customer": {
                    "fullname": "Irena Anakinov\u00e1",
                    "id_card": {"identifier": "passport"},
                    "contact_phone": "0905905905",
                    "address": {
                        "street": "Gandalfova",
                        "street_no": "17",
                        "zipcode": "231 01",
                        "city": "Upper Lower",
                        "country": "Slovakia",
                    },
                },
            },
            "mzone": {
                "documents_info": [
                    {
                        "title": "Zmluva 1",
                        "fileId": "ID123",
                        "file": "document1.pdf",
                        "required": True,
                    },
                    {
                        "title": "Zmluva 2",
                        "fileId": "ID321",
                        "file": "document2.pdf",
                        "required": False,
                    },
                ],
                "type": "Nova zmluva",
                "client_id": "P2432342",
                "customer_name": "Jožko",
                "customer_surname": "Mrkvička",
                "customer_idcard": "123456789",
            },
            "union": {
                "client_id": "fab23548ec4",
                "zakaznik_meno": "Jožko",
                "zakaznik_priezvisko": "Mrkvička",
                "zakaznik_psc": "123 450",
                "zakaznik_mesto": "Upper Lower",
                "type": "Zmluva",
            },
        }
        self.document1_parent = Document.objects.create(
            id="abc_p", client_id="abc$1", customer=self.customer1
        )
        self.document2_parent = Document.objects.create(
            id="xyz_p", client_id="xyz$1", customer=self.customer2
        )
        self.document1 = Document.objects.create(
            id="abc",
            client_id="abc$1",
            customer=self.customer1,
            parent=self.document1_parent,
            timestamp=3,
        )
        self.document2 = Document.objects.create(
            id="xyz",
            client_id="xyz$1",
            customer=self.customer2,
            parent=self.document2_parent,
            timestamp=4,
        )
        self.document1_unsig = Document.objects.create(
            id="klm",
            client_id="Pklm",
            customer=self.customer1,
            rest=self.rest_template["o2"],
        )
        self.document2_unsig = Document.objects.create(
            id="efg", client_id="efg$1", customer=self.customer2
        )
        self.document3_unsig = Document.objects.create(
            id="wtf",
            client_id="919248",
            customer=self.customer3,
            rest=self.rest_template["orange"],
        )

        customers = ["o2", "telekom", "orange", "mzone", "union"]
        for customer in customers:
            self.create_assets_if_not_found(os.path.join(settings.ASSETS_DIR, customer))
            self.create_assets_if_not_found(
                os.path.join(settings.ASSETS_DIR_V2, customer)
            )

    def status_check(self, response, status_code=200):
        self.assertEqual(response.status_code, status_code)
        return response

    def get_test(self, path, data=None, status_code=200):
        return self.status_check(self.client.get(path, data, follow=True), status_code)

    def post_test(self, path, data=None, status_code=200):
        return self.status_check(self.client.post(path, data, follow=True), status_code)

    def delete_test(self, path, data=None, status_code=200):
        return self.status_check(
            self.client.delete(path, data, follow=True), status_code
        )

    def login(self, username, password="pass", status_code=200):
        self.post_test(
            "/login/",
            {"username": username, "password": password},
            status_code=status_code,
        )

    def make_sample_unsigned_package(self, metadata, encrypted=False):
        imz = InMemoryZip()
        imz.append_str("metadata.json", json.dumps(metadata))
        imz.append_file(
            f"{os.path.dirname(os.path.realpath(__file__))}"
            f"/document{'_encrypted' if encrypted else ''}.zip",
            "document.zip",
        )
        return SimpleUploadedFile("package.zip", imz.im_zip.getvalue())

    def make_sample_signed_package(self, metadata, encrypted=False):
        imz = InMemoryZip()
        imz.append_str("metadata.json", json.dumps(metadata))
        imz.append_str("tracking.json", "{}")
        imz.append_file(
            f"{os.path.dirname(os.path.realpath(__file__))}"
            f"/document{'_encrypted' if encrypted else ''}.zip",
            "document.zip",
        )
        return SimpleUploadedFile("package.zip", imz.im_zip.getvalue())

    def create_assets_if_not_found(self, assets_dir):
        if not os.path.exists(assets_dir):
            os.mkdir(assets_dir)

        if not os.path.exists(os.path.join(assets_dir, "wizard_data.json")):
            with open(os.path.join(assets_dir, "wizard_data.json"), "w") as f:
                f.write("{}")

        parcel_assets = [
            "SignDoc4ADS-public.key",
            "signing.properties",
            "index.html",
            "public.der",
        ]
        for asset in parcel_assets:
            open(os.path.join(assets_dir, asset), "a").close()

    # LoginView, LogoutView
    def test_login_logout(self):
        """Testing auth, only couriers can use this endpoint."""
        self.post_test("/login/", {"username": "operator1", "password": "bad"}, 401)

        self.login("operator1")

        response = self.get_test("/logout/")
        self.assertEqual(response.json()["response"], "Logged out.")

        response = self.get_test("/logout/")
        self.assertEqual(response.json()["response"], "User was not logged in.")

    # AuthView
    def test_auth(self):
        """Testing auth, only couriers can use this endpoint."""
        self.login("operator1")

        self.get_test("/auth/", status_code=401)

        self.login("courier1")

        self.get_test("/auth/")

    def test_upload_errors(self):
        self.login("operator3")

        response = self.post_test("/document/unsigned/", {"file": ""}, status_code=400)
        self.assertEqual(response.json()["error"], 'Missing file "package.zip"')

        package = SimpleUploadedFile("package.zip", b"")
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(response.json()["error"], "File is not a zip file")

        package = SimpleUploadedFile("document.txt", b"")
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "Filename document.txt is invalid. Correct format: [a-zA-Z0-9]+.zip",
        )

        imz = InMemoryZip()
        imz.append_str("document.zip", "")
        package = SimpleUploadedFile("package.zip", imz.im_zip.getvalue())
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(response.json()["error"], 'Missing file "metadata.json"')

        imz = InMemoryZip()
        imz.append_str("metadata.json", b"\xf0")
        imz.append_str("document.zip", "")
        package = SimpleUploadedFile("package.zip", imz.im_zip.getvalue())
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "metadata.json is not utf-8 encoded",
        )

        imz = InMemoryZip()
        imz.append_str("metadata.json", "")
        imz.append_str("document.zip", "")
        package = SimpleUploadedFile("package.zip", imz.im_zip.getvalue())
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "metadata.json decode error: Expecting value: line 1 column 1 (char 0)",
        )

        metadata = self.rest_template["orange"]

        imz = InMemoryZip()
        imz.append_str("metadata.json", json.dumps(metadata))
        package = SimpleUploadedFile("package.zip", imz.im_zip.getvalue())
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            'Missing file "document.zip"',
        )

        imz = InMemoryZip()
        imz.append_str("metadata.json", json.dumps(metadata))
        imz.append_str("extra_file.txt", "")
        package = SimpleUploadedFile("package.zip", imz.im_zip.getvalue())
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "invalid zip file",
        )

        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "invalid zip file",
        )

    def test_o2_schema(self):
        self.login("operator1")

        o2 = self.rest_template["o2"]

        package = self.make_sample_unsigned_package(o2)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(o2)
        metadata["zakaznik_meno"] = 123
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"], "zakaznik_meno: 123 is not of type 'string'"
        )

        metadata = deepcopy(o2)
        metadata["error_info"] = {"type": "test", "text": "test1234"}
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "error_info: {'type': 'test', 'text': 'test1234'} is not of type 'array'",
        )

        metadata = deepcopy(o2)
        metadata["scans_info"] = []
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "scans_info: [] is not of type 'object'",
        )

        metadata = deepcopy(o2)
        metadata["documents_info"][0]["required"] = 5
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info.0.required: 5 is not of type 'boolean'",
        )

        metadata = deepcopy(o2)
        metadata["documents_info"] = []
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info: [] is too short",
        )

        metadata = deepcopy(o2)
        metadata["error_info"] = [{"type": "test"}]
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "error_info.0.text: 'text' is a required property",
        )

    def test_telekom_schema(self):
        self.login("operator2")

        telekom = self.rest_template["telekom"]

        package = self.make_sample_unsigned_package(telekom, True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(telekom)
        metadata["client_id"] = "1" * 100
        package = self.make_sample_unsigned_package(metadata, encrypted=True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata["client_id"] = "1" * 101
        package = self.make_sample_unsigned_package(metadata, encrypted=True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            f"client_id: '{'1' * 101}' is too long",
        )

        metadata["client_id"] = "P123456789"
        metadata["company_id_number"] = None
        package = self.make_sample_unsigned_package(metadata, True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(telekom)
        metadata["client_id"] = "P12345678"
        metadata["customer_type"] = None
        package = self.make_sample_unsigned_package(metadata, True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(telekom)
        metadata["zakaznik_meno"] = 123
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "zakaznik_meno: 123 is not of type 'string', 'null'",
        )

        metadata = deepcopy(telekom)
        metadata["orders"] = "mobil"
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "orders: 'mobil' is not of type 'array', 'null'",
        )

        metadata = deepcopy(telekom)
        metadata["documents_info"][0] = []
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info.0: [] is not of type 'object'",
        )

        metadata = deepcopy(telekom)
        metadata["documents_info"][0]["required"] = 5
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info.0.required: 5 is not of type 'boolean'",
        )

        metadata = deepcopy(telekom)
        metadata["documents_info"] = []
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info: [] is too short",
        )

        metadata = deepcopy(telekom)
        metadata["documents_info"][0] = {
            "title": "Zmluva 1",
            "fileId": "ID123",
            "required": True,
        }
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info.0.file: 'file' is a required property",
        )

    def test_orange_schema(self):
        self.login("operator3")

        orange = self.rest_template["orange"]

        package = self.make_sample_unsigned_package(orange, True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(orange)
        metadata["customer"]["fullname"] = 123
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"], "customer.fullname: 123 is not of type 'string'"
        )

        metadata = deepcopy(orange)
        metadata["customer"]["address"] = []
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "customer.address: [] is not of type 'object'",
        )

        metadata = deepcopy(orange)
        metadata["customer"]["id_card"] = {}
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "customer.id_card.identifier: 'identifier' is a required property",
        )

    def test_mzone_schema(self):
        self.login("operator4")

        mzone = self.rest_template["mzone"]

        package = self.make_sample_unsigned_package(mzone)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(mzone)
        metadata["type"] = 123
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(response.json()["error"], "type: 123 is not of type 'string'")

        metadata = deepcopy(mzone)
        metadata["documents_info"] = {}
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info: {} is not of type 'array'",
        )

        metadata = deepcopy(mzone)
        metadata["documents_info"] = []
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info: [] is too short",
        )

        metadata = deepcopy(mzone)
        metadata["documents_info"][0] = {"title": "Zmluva 1", "required": True}
        package = self.make_sample_unsigned_package(metadata)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "documents_info.0.file: 'file' is a required property",
        )

    def test_union_schema(self):
        self.login("operator5")

        union = self.rest_template["union"]

        package = self.make_sample_unsigned_package(union, True)
        self.post_test("/document/unsigned/", {"file": package})

        metadata = deepcopy(union)
        metadata["zakaznik_priezvisko"] = True
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "zakaznik_priezvisko: True is not of type 'string'",
        )

        metadata = deepcopy(union)
        metadata.pop("zakaznik_psc")
        package = self.make_sample_unsigned_package(metadata, True)
        response = self.post_test(
            "/document/unsigned/", {"file": package}, status_code=400
        )
        self.assertEqual(
            response.json()["error"],
            "zakaznik_psc: 'zakaznik_psc' is a required property",
        )

    # HandleDocumentUnsignedView
    def test_upload_and_delete(self):
        """Testing upload, view and delete seal.

        Operator has permission to upload and delete, but not view document.
        Operator from another customer(company) should not see that document exists.
        """
        # prepare zip
        rest = self.rest_template["o2"]
        rest["client_id"] = "doc_doc1"
        to_send = self.make_sample_unsigned_package(rest)

        self.login("operator1")

        self.post_test("/document/unsigned/", {"file": to_send})
        self.assertEqual(Document.objects.filter(client_id="doc1").count(), 1)

        self.get_test("/document/unsigned/doc1/", status_code=401)

        self.login("operator2")

        self.delete_test("/document/unsigned/doc1/", status_code=400)

        self.login("operator1")

        self.delete_test("/document/unsigned/doc1/")
        self.assertEqual(Document.objects.filter(client_id="doc1")[0].hard_delete, True)

    # HandleDocumentUnsignedView
    def test_upload_and_download(self):
        """Checking if courier can view/download document uploaded by operator.

        Courier has permission to view/download unsigned document.
        Operator has permission to upload and delete unsigned document.
        """
        # prepare zip
        rest = self.rest_template["orange"]
        rest["client_id"] = "doc2"
        to_send = self.make_sample_unsigned_package(rest, True)

        self.login("operator3")

        self.post_test("/document/unsigned/", {"file": to_send})
        self.assertEqual(Document.objects.filter(client_id="doc2").count(), 1)

        self.get_test("/document/unsigned/doc2/", status_code=401)

        self.login("courier1")

        self.courier1.documents.add(Document.objects.get(client_id="doc2"))

        self.get_test("/document/unsigned/doc2/", status_code=200)

    # ListUnsignedSinceView
    def test_unsigned_since(self):
        """List unsigned documents since specified date.

        Only operators have permission to list unsigned documents.
        """
        self.login("courier1")
        self.get_test("/document/unsigned/since/0/", status_code=401)

        self.login("operator1")

        response = self.get_test("/document/unsigned/since/0/")
        docs = response.json()["documents"]
        self.assertEqual(len(docs), 2)
        self.assertTrue(any(doc["id"] == "klm" for doc in docs))
        self.assertTrue(any(doc["id"] == "abc_p" for doc in docs))

    # ListSignedSinceView
    def test_signed_since(self):
        """List signed documents since specified date.

        Only operators have permission to list signed documents.
        """
        self.login("operator1")

        response = self.get_test("/document/signed/since/0/")
        data = response.json()
        self.assertTrue(all(item["id"] != "xyz" for item in data["documents"]))
        self.assertTrue(any(item["id"] == "abc" for item in data["documents"]))

    # DriverListView, DriverListAddView
    def test_add_driverlist(self):
        """Manualy adding packages by courier with either document id or client id."""
        self.login("courier1")

        self.post_test("/active/add/", {"id": "bad"}, 400)

        self.post_test("/active/add/", {"id": "klm"})

        response = self.get_test("/active/")
        data = response.json()
        self.assertEqual(len(data["documents"]), 1)

        self.post_test("/active/add/", {"id": "efg$1"})

        response = self.get_test("/active/")
        data = response.json()
        self.assertEqual(len(data["documents"]), 2)

        self.post_test("/active/add/", {"id": "bad"}, status_code=400)
        self.assertEqual(
            Courier.objects.filter(id=self.courier1.id).first().documents.count(), 2
        )

    # DriverListView
    def test_driverlist(self):
        """Listing active packages through v1 and v2 endpoint.

        V1 endpoint returns list of documents wrapped in dictionary with key
        'documents'.
        V2 endpoint returns list of documents.
        """
        self.login("operator2")
        self.get_test("/active/", status_code=401)

        self.courier1.documents.add(self.document1_unsig, self.document2_unsig)
        self.login("courier1")

        response = self.get_test("/active/v2/")
        docs = response.json()
        self.assertEqual(len(docs), 2)
        self.assertTrue(any(doc["id"] == "klm" for doc in docs))
        self.assertTrue(any(doc["id"] == "efg" for doc in docs))

        self.courier1.documents.remove(self.document2_unsig)

        response = self.get_test("/active/")
        docs = response.json()["documents"]
        self.assertEqual(len(docs), 1)
        self.assertEqual(docs[0]["id"], "klm")
        self.assertEqual(docs[0]["client_id"], "Pklm")
        self.assertEqual(docs[0]["sender"], "O2")
        self.assertEqual(
            docs[0]["text"], "Jožko Mrkvička, Malinova 23, Bratislava, 012 34, Zmluva"
        )
        self.assertEqual(docs[0]["url"], "http://testserver/document/unsigned/klm/v1/")
        self.assertEqual(docs[0]["filename"], "klm.zip")

    # AssignView
    def test_courier_assign(self):
        """Assigning packages to courier by operator with V1.

        Daily package assignment.
        """
        self.login("courier2")
        data = {"documents": ["klm"], "id": "courier1"}
        self.status_check(
            self.client.post(
                "/assign/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            ),
            401,
        )

        self.login("operator1")

        data = {"documents": ["klm"], "id": "bad"}
        self.status_check(
            self.client.post(
                "/assign/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            ),
            400,
        )

        data = {"documents": ["klm"], "id": "courier1"}
        self.status_check(
            response=self.client.post(
                "/assign/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            )
        )

        data = {"documents": ["efg$1"], "id": "courier2"}
        self.status_check(
            self.client.post(
                "/assign/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            )
        )

        self.assertEqual(self.courier1.documents.count(), 1)
        self.assertEqual(self.courier1.documents.first().client_id, "Pklm")

        self.assertEqual(self.courier2.documents.count(), 1)
        self.assertEqual(self.courier2.documents.first().id, "efg")

    # Assign2View
    def test_courier_assign2(self):
        """Assigning packages to courier by operator with V2.

        Daily package assignment.
        """
        self.login("client")

        data: dict[str, Any] = {}
        self.status_check(
            self.client.post(
                "/assign2/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            ),
            401,
        )

        self.login("operator1")

        data = {
            "id": "courier1",
            "documents": [{"doc": "klm", "cislo_zasielky": "i_klm"}],
        }
        self.status_check(
            self.client.post(
                "/assign2/",
                json.dumps(data),
                content_type="application/json",
                follow=True,
            )
        )

        self.assertEqual(self.courier1.documents.count(), 1)
        self.assertEqual(self.courier1.documents.all()[0].internal_id, "i_klm")

    # DriverListView
    def test_orange_driverlist(self):
        """Listing active packages."""
        self.login("operator3")
        response = self.get_test("/active/", status_code=401)

        self.login("courier3")

        self.courier3.documents.add(self.document3_unsig)

        response = self.get_test("/active/")
        docs = response.json()["documents"]
        self.assertEqual(len(docs), 1)
        self.assertEqual(docs[0]["id"], "wtf")
        self.assertEqual(docs[0]["client_id"], "919248")
        self.assertEqual(docs[0]["sender"], "orange")
        self.assertEqual(
            docs[0]["text"],
            "Irena Anakinová, Gandalfova 17, Upper Lower, 231 01, Zmluva",
        )
        self.assertEqual(docs[0]["url"], "http://testserver/document/unsigned/wtf/v1/")
        self.assertEqual(docs[0]["filename"], "wtf.zip")

    # DocumentUnsignedAckView
    def test_unsigned_acknowledged(self):
        """Acknowlidging document with id passed through body and url path.

        There is no check on backend if document exists.
        """
        # No user is not logged in
        # any user has permission to acknowledge document
        self.post_test("/document/unsigned/ack/", {"id": self.document3_unsig.id}, 401)

        self.login("courier3")

        self.courier3.documents.add(self.document3_unsig)

        self.post_test("/document/unsigned/ack/", {"id": self.document3_unsig.id})

        self.post_test("/document/unsigned/ack/", {"id": "bad"}, 404)

        self.post_test("/document/unsigned/ack/{}/".format(self.document3_unsig.id))

        self.post_test("/document/unsigned/ack/", status_code=400)

    # MarkRemotelySignedView
    def test_remotely_signed(self):
        """Marking document as signed from location with bad internet connection."""
        self.login("operator1")
        self.post_test("/document/mark-signed/", {"id": self.document3_unsig.id}, 401)

        self.login("courier1")

        self.courier1.documents.add(self.document3_unsig)

        self.post_test("/document/mark-signed/", {"id": self.document3_unsig.id})
        self.assertEqual(
            Document.objects.with_id(self.document3_unsig.id).first().signed_remotely,
            True,
        )

    # HandleDocumentSignedView
    def test_sing_document(self):
        """Upload signed document."""
        rest = self.rest_template["telekom"]
        rest["client_id"] = "efg_c"
        to_send = self.make_sample_signed_package(rest, encrypted=True)

        self.login("courier1")

        response = self.post_test("/document/signed/efg/", {"file": to_send})
        data = response.json()
        self.assertTrue("id" in data)
        id = data["id"]
        self.assertEqual(Document.objects.with_id(id).count(), 1)
        self.assertEqual(
            Document.objects.with_id(id).first().parent,
            Document.objects.with_id("efg").first(),
        )

        response = self.post_test("/document/signed/klm/", status_code=400)
        self.assertEqual(
            response.json()["error"], '"file" parameter is required in request'
        )

        response = self.post_test(
            "/document/signed/klm/",
            {"file": SimpleUploadedFile("package.tar", "")},
            400,
        )
        self.assertEqual(
            response.json()["error"],
            "Filename package.tar is invalid. Correct format: [a-zA-Z0-9]+.zip",
        )

        self.get_test("/logout/")

        self.post_test("/document/signed/klm/", status_code=401)

    # QuarantineView, WebQuarantineDeleteView
    def test_quarantine(self):
        """Adding documents to quarantine when unsigned document doesn't exist
        or signed document is uploaded multiple times.
        """
        rest = self.rest_template["telekom"]
        rest["client_id"] = "efg_c"
        to_send = self.make_sample_signed_package(rest, encrypted=True)

        self.login("courier1")

        response = self.post_test("/document/signed/bad/", {"file": to_send})
        self.assertEqual(
            response.json()["error"],
            "Unsigned document with id bad does not exist. Document moved to "
            "quarantine.",
        )

        quarantine = Quarantine.objects.filter(url_id="bad")
        self.assertEqual(len(quarantine), 1)

        self.login("manager")

        quarantine_id = quarantine[0].id
        self.get_test("/quarantine/{}/".format(quarantine_id))

        self.delete_test(f"/quarantine/delete/{quarantine_id}/")
        self.assertEqual(Quarantine.objects.get(id=quarantine_id).deleted, True)

        self.get_test(f"/quarantine/{quarantine_id}/", status_code=400)

        self.login("courier1")

        self.courier1.documents.add(self.document1_unsig)

        to_send = self.make_sample_signed_package(rest, encrypted=True)
        response = self.post_test("/document/signed/efg/", {"file": to_send})
        self.assertTrue("id" in response.json())

        to_send = self.make_sample_signed_package(rest, encrypted=True)
        response = self.post_test("/document/signed/efg/", {"file": to_send})
        self.assertEqual(
            response.json()["error"],
            "Document with id efg is already signed. Document moved to quarantine.",
        )

        quarantine = Quarantine.objects.filter(url_id="efg")
        self.assertEqual(len(quarantine), 1)

        self.login("manager")

        self.get_test("/quarantine/{}/".format(quarantine[0].id))

        Quarantine.objects.create(
            id="test147",
            timestamp=int((datetime.now() - timedelta(days=2)).timestamp() * 1000),
            customer=self.customer1,
        )
        Quarantine.objects.create(
            id="test258",
            timestamp=int((datetime.now() - timedelta(hours=23)).timestamp() * 1000),
            customer=self.customer1,
        )
        Quarantine.objects.create(
            id="test148",
            timestamp=int((datetime.now() - timedelta(days=31)).timestamp() * 1000),
        )
        Quarantine.objects.create(
            id="test259",
            timestamp=int(
                (datetime.now() - timedelta(days=29, hours=23)).timestamp() * 1000
            ),
        )

        self.customer1.old_quarantine_packages_removal_limit = 1
        self.customer1.save()
        remove_old_quarantine_documents([self.customer1])
        self.assertEqual(Quarantine.objects.filter(deleted=False).count(), 3)
        self.assertEqual(Quarantine.objects.get(id="test147").deleted, True)
        self.assertEqual(Quarantine.objects.get(id="test258").deleted, False)
        self.assertEqual(Quarantine.objects.get(id="test148").deleted, True)
        self.assertEqual(Quarantine.objects.get(id="test259").deleted, False)

    # LogsView
    def test_upload_and_view_of_logs(self):
        """Uploading logs from courier tablet in cases when courier says he signed
        document but we haven't received it.

        Courier has permission to upload logs.
        Manager has permission to view logs.
        """
        to_upload = SimpleUploadedFile(
            "log1_tablet1_5.zip", InMemoryZip().im_zip.getvalue()
        )

        self.login("operator1")
        self.post_test("/logs/", {"file": to_upload}, 401)

        self.login("courier1")

        response = self.post_test("/logs/", {"file": to_upload})
        data = response.json()
        self.assertTrue("id" in data)
        id = data["id"]
        log = Log.objects.get(id=id)
        self.assertEqual(log.timestamp, 5)
        self.assertEqual(log.device_id, "tablet1")
        self.assertEqual(log.file_name, "log1_tablet1_5")
        self.assertEqual(log.courier, self.courier1)

        response = self.post_test("/logs/", status_code=400)
        self.assertEqual(
            response.json()["error"], '"file" parameter is required in request'
        )

        response = self.post_test(
            "/logs/",
            {"file": SimpleUploadedFile("bad.tar", InMemoryZip().im_zip.getvalue())},
            status_code=400,
        )
        self.assertEqual(response.json()["error"], "Filetype not supported")

        self.login("manager")

        self.get_test("/logs/{}/".format(id))

        response = self.get_test("/logs/bad/", status_code=400)
        self.assertEqual(response.json()["error"], "File with id bad does not exist")

    def test_dashboard_export(self):
        header = [
            "ID",
            "Client ID",
            "Uploaded",
            "Signed",
            "City",
            "ZIP",
            "Status",
            "Author",
            "Owned by",
            "Deleted",
            "Size (KB)",
            "Wizard Version",
            "Power of attorney",
            "Mandatory scans",
            "Optional scans",
            "Custom scans",
        ]

        self.login("operator1")

        export1 = [
            header,
            [
                "abc",
                "abc$1",
                "1970-01-01 01:00:00",
                "1970-01-01 01:00:00.003000",
                "",
                "",
                "SIGNED",
                "",
                "",
                "No",
                "0.0",
                "",
                "",
                "0",
                "0",
                "0",
            ],
            [
                "klm",
                "Pklm",
                "1970-01-01 01:00:00",
                "",
                "Bratislava",
                "012 34",
                "UNSIGNED",
                "",
                "",
                "No",
                "0.0",
                "",
                "",
                "0",
                "0",
                "0",
            ],
        ]
        response = self.get_test("/web/dashboard/?action=Export")
        csv_reader = csv.reader(response.content.decode("utf-8").split("\r\n")[:-1])
        self.assertEqual(list(csv_reader), export1)

        self.login("operator2")

        export2 = [
            header,
            [
                "xyz",
                "xyz$1",
                "1970-01-01 01:00:00",
                "1970-01-01 01:00:00.004000",
                "",
                "",
                "SIGNED",
                "",
                "",
                "No",
                "0.0",
                "",
                "",
                "0",
                "0",
                "0",
            ],
        ]
        response = self.get_test("/web/dashboard/?status=signed&action=Export")
        csv_reader = csv.reader(response.content.decode("utf-8").split("\r\n")[:-1])
        self.assertEqual(list(csv_reader), export2)

        self.login("operator3")

        Document.objects.create(
            id="test_time",
            client_id="85745",
            customer=self.customer3,
            timestamp=100000,
        )
        export3 = [
            header,
            [
                "test_time",
                "85745",
                "1970-01-01 01:01:40",
                "",
                "",
                "",
                "UNSIGNED",
                "",
                "",
                "No",
                "0.0",
                "",
                "",
                "0",
                "0",
                "0",
            ],
        ]
        response = self.get_test(
            "/web/dashboard/?begin=1970%2F01%2F01+01%3A01%3A40&action=Export"
        )
        csv_reader = csv.reader(response.content.decode("utf-8").split("\r\n")[:-1])
        self.assertEqual(list(csv_reader), export3)

    def test_audit_export(self):
        data = [["Id", "Total", "Created at"]] + [
            [
                hex(i),
                str(i),
                datetime.utcfromtimestamp(i + 1).strftime("%Y-%m-%d %H:%M:%S"),
            ]
            for i in range(0, 100, 10)
        ]
        for i in range(1, 11):
            entry = AuditEntry.objects.create(
                data={"Id": data[i][0], "Total": data[i][1]}
            )
            entry.created_at = datetime.utcfromtimestamp(i * 10 - 9)
            entry.save()
        AuditExport.objects.create(
            start_datetime=datetime.utcfromtimestamp(0),
            end_datetime=datetime.now(),
            field_names=["Id", "Total"],
        )
        create_exports()
        csv_reader = csv.reader(
            AuditExport.objects.exclude(result="").first().result.open("r")
        )
        self.assertEqual(list(csv_reader), data)

    def test_automatic_document_deletion(self):
        now = datetime.now()
        Document.objects.create(
            id="document1",
            client_id="document1",
            customer=self.customer1,
            timestamp=int((now - timedelta(days=41)).timestamp() * 1000),
        )
        document2 = Document.objects.create(
            id="document2",
            client_id="document2",
            customer=self.customer1,
            timestamp=int((now - timedelta(days=39, hours=23)).timestamp() * 1000),
        )
        document3 = Document.objects.create(
            id="document3",
            client_id="document3",
            customer=self.customer1,
            timestamp=int((now - timedelta(days=39, hours=23)).timestamp() * 1000),
        )
        remove_old_unsigned_documents([self.customer1])
        self.assertEqual(Document.objects.get(id="document1").deleted, True)
        self.assertEqual(Document.objects.get(id="document2").deleted, False)
        self.assertEqual(Document.objects.get(id="document3").deleted, False)

        Document.objects.create(
            id="document2_signed",
            client_id="document2",
            customer=self.customer1,
            timestamp=int((now - timedelta(days=31)).timestamp() * 1000),
            parent=document2,
        )
        Document.objects.create(
            id="document3_signed",
            client_id="document3",
            customer=self.customer1,
            timestamp=int((now - timedelta(days=29, hours=23)).timestamp() * 1000),
            parent=document3,
        )
        remove_old_signed_documents([self.customer1])
        self.assertEqual(Document.objects.get(id="document2_signed").deleted, True)
        self.assertEqual(Document.objects.get(id="document3_signed").deleted, False)

    def test_courier_registration(self):
        self.login("operator1")
        self.post_test("/register/", status_code=401)

        self.login("manager")

        response = self.post_test(
            "/register/",
            data={"username": "courier1", "id": "courier1"},
            status_code=400,
        )
        data = response.json()
        self.assertEqual(
            data,
            {
                "error": [
                    "password not specified",
                    "User with username courier1 already exists.",
                    "Courier with id courier1 already exists.",
                ]
            },
        )

        self.post_test(
            "/register/",
            data={"username": "courier123", "password": "pass", "id": "courier123"},
            status_code=200,
        )

    def test_courier_change_password(self):
        self.login("operator1")
        self.post_test("/changepassword/", status_code=401)

        self.login("manager")
        response = self.post_test(
            "/changepassword/", data={"username": "courier10"}, status_code=400
        )
        data = response.json()
        self.assertEqual(
            data,
            {
                "error": [
                    "password not specified",
                    "Courier with username courier10 does not exists.",
                ]
            },
        )

        self.post_test(
            "/changepassword/",
            data={"username": "courier1", "password": "test1234"},
            status_code=200,
        )
        self.login("courier1", "test1234")

    def test_fail2ban(self):
        BAN_CONFIG = config.BAN_CONFIG

        for i in range(BAN_CONFIG["operator"]["fail_limit"]):
            self.login("operator1", "wrong password", status_code=401)

        self.login("operator1", status_code=429)

        ban = Ban.objects.get(user__username="operator1")
        self.login("manager")
        self.get_test(f"/web/unban/{ban.id}/")
        self.get_test("/logout/")

        self.login("operator1")
        self.get_test("/logout/")

        for i in range(BAN_CONFIG["ip"]["fail_limit"]):
            self.login(str(i), "wrong password", status_code=401)

        self.login("operator2", status_code=429)

        credentials = base64.b64encode(b"operator1:pass").decode("utf-8")
        self.status_check(
            self.client.get(
                "/document/signed/since/0/",
                HTTP_AUTHORIZATION=f"Basic {credentials}",
            ),
            429,
        )

    def test_basic_auth(self):
        credentials = base64.b64encode(b"operator1:pass").decode("utf-8")
        self.status_check(
            self.client.get(
                "/document/signed/since/0/",
                HTTP_AUTHORIZATION=f"Basic {credentials}",
            ),
            200,
        )
