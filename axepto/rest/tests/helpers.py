import json

from django.test import TestCase


class TestHelpersMixin(TestCase):
    def get(self, url, *args, **kwargs):
        follow = kwargs.pop("follow", True)
        client = kwargs.pop("client", self.client)
        return client.get(url, *args, follow=follow, **kwargs)

    def post(self, url, *args, **kwargs):
        follow = kwargs.pop("follow", True)
        client = kwargs.pop("client", self.client)
        return client.post(url, *args, follow=follow, **kwargs)

    def get_assert(
        self,
        url,
        data=None,
        expected_status_code=None,
        expected_response=None,
        **kwargs
    ):
        response = self.get(url, data, **kwargs)
        if expected_status_code is not None:
            self.assertEqual(response.status_code, expected_status_code)
        if expected_response is not None:
            if "application/json" in response["Content-Type"]:
                self.assertEqual(json.loads(response.content), expected_response)
            else:
                self.assertEqual(response.content, expected_response)
        return response

    def post_assert(
        self,
        url,
        data=None,
        expected_status_code=None,
        expected_response=None,
        **kwargs
    ):
        response = self.post(url, data, **kwargs)
        if expected_status_code is not None:
            self.assertEqual(response.status_code, expected_status_code)
        if expected_response is not None:
            if "application/json" in response["Content-Type"]:
                self.assertEqual(json.loads(response.content), expected_response)
            else:
                self.assertEqual(response.content, expected_response)
        return response

    def post_json_assert(
        self,
        url,
        data=None,
        expected_status_code=None,
        expected_response=None,
        **kwargs
    ):
        return self.post_assert(
            url,
            json.dumps(data),
            expected_status_code,
            expected_response,
            content_type="application/json",
            **kwargs
        )
