import json
import unittest
from datetime import datetime
from typing import Any

from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.management import call_command
from django.test import TestCase
from selenium import webdriver
from selenium.webdriver.firefox.service import Service
from selenium.webdriver.support.wait import WebDriverWait
from webdriver_manager.firefox import GeckoDriverManager

from axepto.rest.models import (
    Courier,
    Customer,
    Manager,
    Operator,
    Survey,
    SurveyRecord,
)

from .helpers import TestHelpersMixin

# from pyvirtualdisplay import Display


sample_package = SimpleUploadedFile("package.zip", bytes("asdf", "utf-8"))

op_o2 = operator_o2 = {"username": "o2_operator", "password": "pass"}
op_tk = operator_telekom = {"username": "telekom_operator", "password": "pass"}
manager_data = {"username": "axepto", "password": "pass"}
courier_data = {"username": "courier", "password": "pass"}
courier2_data = {**courier_data, "username": "courier2"}
survey_active = {"active": True, "subject": "People's party", "package": sample_package}
invalid_id = 987654321


class CookieMixin(TestHelpersMixin):
    def get_cookie_from_login(self, user):
        another_client = self.client_class()
        self.post_assert("/login/", user, 200, client=another_client)
        response = self.get_assert("/auth/", None, 200, client=another_client)
        return json.loads(response.content)["result"]


def is_selenium_enabled():
    return False
    # return getattr(settings, "MODE") == 'dev'


class MySeleniumTests(CookieMixin, StaticLiveServerTestCase):
    @classmethod
    def setUpClass(cls):
        super(MySeleniumTests, cls).setUpClass()
        # start display
        # cls.vdisplay = Display(visible=0, size=(1024, 768))
        # cls.vdisplay.start()

        if is_selenium_enabled():
            # to test wizards, refer to
            # https://stackoverflow.com/questions/29457206/selenium-standalone-with-cors
            # fp = webdriver.FirefoxProfile()
            # fp.set_preference("security.fileuri.strict_origin_policy", False)

            cls.selenium = webdriver.Firefox(
                service=Service(GeckoDriverManager().install()),
            )
            # cls.selenium.maximize_window()

            call_command("collectstatic", "--noinput")

    @classmethod
    def tearDownClass(cls):
        if is_selenium_enabled():
            cls.selenium.quit()
        super(MySeleniumTests, cls).tearDownClass()

        # stop display
        # self.vdisplay.stop()

    def setUp(self):
        self.courier = Courier.objects.create(
            user=User.objects.create_user(**courier_data), id="courier"
        )

        self.axepto = Manager.objects.create(
            user=User.objects.create_user(**manager_data)
        )

        self.survey = Survey.objects.create(**survey_active)
        self.make_survey_action = lambda action="pre", courier=self.courier, token="": {
            "courier_name": courier.user.username,
            "action": action,
            "accepted": True,
            "timestamp": 1575385513734,
            "survey_id": self.survey.id,
            "token": token,
        }

    def first_record(self):
        return SurveyRecord.objects.filter(
            courier=self.courier, survey=self.survey
        ).first()

    @unittest.skipUnless(
        is_selenium_enabled(), "selenium tests enabled only in 'dev' mode"
    )
    def test_finishing_survey(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)
        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)

        self.assertFalse(self.first_record().finished)

        self.selenium.get(
            "%s%s"
            % (
                self.live_server_url,
                "/web/postsurvey/?courier_name=courier&target_server="
                + self.live_server_url
                + "&survey_id="
                + self.survey.id.__str__(),
            )
        )
        WebDriverWait(self.selenium, 13).until(
            lambda driver: self.first_record().finished
        )

        self.assertTrue(self.first_record().finished)
        self.assertIsNone(self.first_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("post", token=cookie), 200
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.first_record().finished)
        self.assertIsInstance(self.first_record().closed_at, datetime)

    @unittest.skipUnless(
        is_selenium_enabled(), "selenium tests enabled only in 'dev' mode"
    )
    def test_creating_survey(self):
        self.selenium.get("%s%s" % (self.live_server_url, "/web/login/"))
        self.selenium.find_element_by_name("username").send_keys(
            manager_data["username"]
        )
        self.selenium.find_element_by_name("password").send_keys(
            manager_data["password"]
        )
        self.selenium.find_element_by_xpath('//input[@value="Submit"]').click()
        WebDriverWait(self.selenium, 13).until(
            lambda driver: self.selenium.find_element_by_name("begin")
        )

        # only one survey record for predefined survey
        self.selenium.get("%s%s" % (self.live_server_url, "/web/surveys/"))
        table = self.selenium.find_element_by_id("table_id")
        tbody_list = table.find_elements_by_tag_name("tbody")
        self.assertEquals(1, tbody_list.__len__())
        tr_list = tbody_list[0].find_elements_by_tag_name("tr")
        self.assertEquals(1, tr_list.__len__())
        self.assertEqual(Survey.objects.count(), 1)

        sample_survey_data: dict[str, Any] = {
            "subject": "asdfasdf",
            "description": "2354234\n\nsdgf\nsdgf",
            "form_url": "http://ripkomiks.borec.cz",
            "repeating": 147,
        }

        # fill and submit survey form
        self.selenium.get("%s%s" % (self.live_server_url, "/web/surveys/add"))
        self.selenium.find_element_by_id("id_subject").send_keys(
            sample_survey_data["subject"]
        )
        self.selenium.find_element_by_xpath('//*[@id="id_description"]').send_keys(
            sample_survey_data["description"]
        )
        couriers_div = self.selenium.find_element_by_id("div_id_couriers")
        couriers_div.find_element_by_xpath("div/div/button").click()
        couriers_div.find_element_by_link_text("courier").click()
        couriers_div.find_element_by_xpath("div/div/button").click()
        self.selenium.find_element_by_id("id_form_url").send_keys(
            sample_survey_data["form_url"]
        )
        self.selenium.find_element_by_id("id_repeating").send_keys(
            sample_survey_data["repeating"]
        )
        self.selenium.find_element_by_id("id_active").click()
        self.selenium.find_element_by_xpath('//input[@value="Save"]').click()
        WebDriverWait(self.selenium, 13).until(
            lambda driver: self.selenium.find_element_by_id("table_id")
        )

        # two survey records exist
        self.selenium.get("%s%s" % (self.live_server_url, "/web/surveys/"))
        table = self.selenium.find_element_by_id("table_id")
        tbody_list = table.find_elements_by_tag_name("tbody")
        self.assertEquals(1, tbody_list.__len__())
        tr_list = tbody_list[0].find_elements_by_tag_name("tr")
        self.assertEquals(2, tr_list.__len__())

        # survey fields were correctly set
        qs = Survey.objects.filter(couriers=self.courier)
        created_survey = qs.first()
        self.assertEquals(1, qs.count())
        self.assertEquals(created_survey.subject, sample_survey_data["subject"])
        self.assertEquals(
            created_survey.description.replace("\r", ""),
            sample_survey_data["description"].replace("\r", ""),
        )  # CRLF problem
        self.assertEquals(created_survey.form_url, sample_survey_data["form_url"])
        self.assertEquals(created_survey.repeating, sample_survey_data["repeating"])
        self.assertTrue(created_survey.active)

        # self.selenium.get('file:///home/<USER>/axepto/axepto/assets/surveys/index.html')


class TestSurveys(CookieMixin, TestCase):
    def setUp(self):
        self.o2 = Customer.objects.filter(id="o2")[0]
        self.tk = Customer.objects.create(id="telekom", name="telekom", active=True)

        self.op_o2 = Operator.objects.create(
            user=User.objects.create_user(**op_o2), customer=self.o2
        )

        self.op_tk = Operator.objects.create(
            user=User.objects.create_user(**op_tk), customer=self.tk
        )

        self.courier = Courier.objects.create(
            user=User.objects.create_user(**courier_data), id="courier"
        )

        self.courier2 = Courier.objects.create(
            user=User.objects.create_user(**courier2_data), id="courier2"
        )

        self.survey = Survey.objects.create(**survey_active)
        self.make_survey_action = lambda action="pre", courier=self.courier, token="": {
            "courier_name": courier.user.username,
            "action": action,
            "accepted": True,
            "timestamp": 1575385513734,
            "survey_id": self.survey.id,
            "token": token,
        }

    def first_record(self):
        return SurveyRecord.objects.filter(
            courier=self.courier, survey=self.survey
        ).first()

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def get_cookie_from_login(self, user):
        another_client = self.client_class()
        self.post_assert("/login/", user, 200, client=another_client)
        response = self.get_assert("/auth/", None, 200, client=another_client)
        return json.loads(response.content)["result"]

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_passing_session_cookie(self):
        self.get_assert("/auth/", None, 401)

        self.post_assert("/login/", op_o2, 200)
        self.get_assert("/auth/", None, 401, {"error": "Not logged in as courier"})

        self.post_assert("/login/", courier_data, 200)

        another_client = self.client_class()
        self.get_assert("/active/v2/", None, 401, client=another_client)

        response = self.get_assert("/auth/", None, 200)
        cookie = json.loads(response.content)["result"]

        # stolen cookie works when passed as a cookie
        another_client.cookies[settings.SESSION_COOKIE_NAME] = cookie
        self.get_assert("/active/v2/", None, 200, client=another_client)

        self.survey.couriers.add(self.courier)
        self.post_json_assert(
            "/survey/",
            self.make_survey_action(),
            400,
            {"error": "invalid token for courier"},
        )
        self.post_json_assert(
            "/survey/",
            self.make_survey_action(token=cookie + "evil_suffix"),
            400,
            {"error": "invalid token for courier"},
        )
        self.post_json_assert(
            "/survey/",
            self.make_survey_action(token=cookie, courier=self.courier2),
            400,
            {"error": "invalid token for courier"},
        )
        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_driver_list_returning_survey_for_the_correct_courier(self):
        self.post_assert("/login/", courier_data, 200)
        self.get_assert("/active/v2/", None, 200, [])

        self.survey.couriers.add(self.courier)
        self.get_assert(
            "/active/v2/",
            None,
            200,
            [
                {
                    "id": "package",
                    "client_id": "People's party",
                    "sender": "",
                    "filename": "package.zip",
                    "url": "http://testserver/document/unsigned/package/",
                    "text": "People's party",
                }
            ],
        )

        another_client = self.client_class()
        self.post_assert("/login/", courier2_data, 200, client=another_client)
        self.get_assert("/active/v2/", None, 200, [], client=another_client)

        # TODO test a document can be fetched along with a survey

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_unassigned_survey(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.post_json_assert(
            "/survey/",
            self.make_survey_action(token=cookie),
            400,
            {"error": "survey is not assigned to courier"},
        )

        self.survey.couriers.add(self.courier)
        self.post_json_assert(
            "/survey/", self.make_survey_action(token=cookie), 200, {"result": "OK"}
        )

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_malformed_input(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)
        data = self.make_survey_action(token=cookie)
        self.post_json_assert(
            "/survey/",
            {i: data[i] for i in ["token"]},
            400,
            {"error": "'courier_name'"},
        )
        self.post_json_assert(
            "/survey/",
            {i: data[i] for i in ["token", "courier_name"]},
            400,
            {"error": "'survey_id'"},
        )
        self.post_json_assert(
            "/survey/",
            {i: data[i] for i in ["token", "courier_name", "survey_id"]},
            400,
            {"error": "'action'"},
        )
        self.post_json_assert(
            "/survey/",
            {i: data[i] for i in ["token", "courier_name", "survey_id", "action"]},
            400,
            {"error": "'accepted'"},
        )
        self.post_json_assert(
            "/survey/",
            {
                i: data[i]
                for i in ["token", "courier_name", "survey_id", "action", "accepted"]
            },
            400,
            {"error": "'timestamp'"},
        )
        self.post_json_assert(
            "/survey/",
            {
                i: data[i]
                for i in [
                    "token",
                    "courier_name",
                    "survey_id",
                    "action",
                    "accepted",
                    "timestamp",
                ]
            },
            200,
            {"result": "OK"},
        )

        self.post_json_assert(
            "/survey/",
            {**data, "survey_id": invalid_id},
            400,
            {"error": "survey does not exist"},
        )
        self.post_json_assert(
            "/survey/",
            {**data, "courier_name": "I DO NOT EXISTS", "action": "check-in"},
            400,
            {"error": "survey is not assigned to courier"},
        )
        self.post_json_assert(
            "/survey/",
            {**data, "action": "unhandled action"},
            400,
            {"error": "Unknown action"},
        )
        self.post_assert(
            "/survey/", data, 400, {"error": "expected application/json content type"}
        )

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_survey_record(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            0,
        )

        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)
        first_record_id = self.first_record().id
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey, accepted=True
            ).count(),
            1,
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey, accepted=False
            ).count(),
            0,
        )

        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            2,
        )
        second_record = (
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey, accepted=True
            )
            .first()
            .id
        )
        closed_record = (
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey, accepted=False
            )
            .first()
            .id
        )
        self.assert_(first_record_id != second_record)
        self.assertEqual(first_record_id, closed_record)

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_finishing_survey(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)
        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)
        self.assertFalse(self.first_record().finished)

        self.post_json_assert("/survey/", self.make_survey_action("check-in"), 200)
        self.assertTrue(self.first_record().finished)
        self.assertIsNone(self.first_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("post", token=cookie), 200
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.first_record().finished)
        self.assertIsInstance(self.first_record().closed_at, datetime)

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_cannot_finish_unchecked(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)
        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)
        self.assertFalse(self.first_record().finished)
        self.assertTrue(self.first_record().accepted)
        self.assertIsNone(self.first_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("post", token=cookie), 200
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )
        self.assertFalse(self.first_record().accepted)
        self.assertIsInstance(self.first_record().closed_at, datetime)

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_cancel_survey(self):
        cookie = self.get_cookie_from_login(courier_data)

        self.survey.couriers.add(self.courier)

        self.post_json_assert(
            "/survey/", self.make_survey_action("cancel", token=cookie), 200
        )
        self.assertFalse(self.first_record().finished)
        self.assertFalse(self.first_record().accepted)
        self.assertIsInstance(self.first_record().closed_at, datetime)
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )

    def c1_record(self):
        return SurveyRecord.objects.filter(
            courier=self.courier, survey=self.survey
        ).first()

    def c2_record(self):
        return SurveyRecord.objects.filter(
            courier=self.courier2, survey=self.survey
        ).first()

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_parallel_process(self):
        cookie = self.get_cookie_from_login(courier_data)
        cookie2 = self.get_cookie_from_login(courier2_data)

        self.survey.couriers.add(self.courier)
        self.survey.couriers.add(self.courier2)

        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)

        self.assertFalse(self.c1_record().finished)

        self.post_json_assert(
            "/survey/",
            self.make_survey_action(courier=self.courier2, token=cookie2),
            200,
        )

        self.assertFalse(self.c2_record().finished)

        self.post_json_assert("/survey/", self.make_survey_action("check-in"), 200)
        self.assertTrue(self.c1_record().finished)
        self.assertIsNone(self.c1_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("check-in", self.courier2), 200
        )
        self.assertTrue(self.c2_record().finished)
        self.assertIsNone(self.c2_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("post", token=cookie), 200
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.c1_record().finished)
        self.assertIsInstance(self.c1_record().closed_at, datetime)

        self.post_json_assert(
            "/survey/",
            self.make_survey_action("post", self.courier2, token=cookie2),
            200,
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier2, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.c2_record().finished)
        self.assertIsInstance(self.c2_record().closed_at, datetime)

        self.assertEqual(SurveyRecord.objects.filter(survey=self.survey).count(), 2)

    @unittest.skip("Old tests, should be updated when surveys would be available again")
    def test_another_parallel_process(self):
        cookie = self.get_cookie_from_login(courier_data)
        cookie2 = self.get_cookie_from_login(courier2_data)

        self.survey.couriers.add(self.courier)
        self.survey.couriers.add(self.courier2)

        self.post_json_assert("/survey/", self.make_survey_action(token=cookie), 200)
        self.assertFalse(self.c1_record().finished)

        self.post_json_assert(
            "/survey/",
            self.make_survey_action(courier=self.courier2, token=cookie2),
            200,
        )
        self.assertFalse(self.c2_record().finished)

        self.post_json_assert("/survey/", self.make_survey_action("check-in"), 200)
        self.assertTrue(self.c1_record().finished)
        self.assertIsNone(self.c1_record().closed_at)

        self.post_json_assert(
            "/survey/", self.make_survey_action("post", token=cookie), 200
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.c1_record().finished)
        self.assertIsInstance(self.c1_record().closed_at, datetime)

        self.post_json_assert(
            "/survey/", self.make_survey_action("check-in", self.courier2), 200
        )
        self.assertTrue(self.c2_record().finished)
        self.assertIsNone(self.c2_record().closed_at)

        self.post_json_assert(
            "/survey/",
            self.make_survey_action("post", self.courier2, token=cookie2),
            200,
        )
        self.assertEqual(
            SurveyRecord.objects.filter(
                courier=self.courier2, survey=self.survey
            ).count(),
            1,
        )
        self.assertTrue(self.c2_record().finished)
        self.assertIsInstance(self.c2_record().closed_at, datetime)

        self.assertEqual(SurveyRecord.objects.filter(survey=self.survey).count(), 2)
