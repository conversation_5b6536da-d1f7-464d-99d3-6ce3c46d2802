import logging
from datetime import timedelta

from django.conf import settings
from django.template.loader import render_to_string
from django.urls import reverse
from humanfriendly import format_size

from axepto.constants import (
    ADDED_TO_QUARANTINE,
    ASSIGN_RATIO,
    DELETED_PARENT,
    DOCUMENT_DELAY,
    LOGS_RECIEVED,
    LONG_SIGNING,
    NOT_OBTAINED,
    REPORT_DAILY,
    REPORT_WEEKLY,
    SIGNED_NOT_UPLOADED,
    SMALL_DOCUMENT,
    TIME_SHIFT,
    TWICE_RECIEVED_DOCUMENT,
    UPLOADED_DECREASE,
)
from axepto.helpers import format_time, format_timespan, get_absolute_url
from axepto.library.notification_helpers import add_notification

from .models import Notification

logger = logging.getLogger(__name__)

SIGNED_PACKAGES_SIZE = getattr(settings, "SIGNED_PACKAGES_SIZE", 100000)

# values recieved in sec converted to ms
DELAY_NOTIFICATION_LIMIT = (
    getattr(settings, "DELAY_NOTIFICATION_LIMIT", timedelta(days=5).total_seconds())
    * 1000
)

SIGNING_PROCESS_TIME_LIMIT = (
    getattr(
        settings, "SIGNING_PROCESS_TIME_LIMIT", timedelta(minutes=10).total_seconds()
    )
    * 1000
)

TABLET_TIME_SHIFT = (
    getattr(settings, "TABLET_TIME_SHIFT", timedelta(minutes=2).total_seconds()) * 1000
)


def notify_if_small(doc):
    if doc.zip_size < SIGNED_PACKAGES_SIZE:
        doc_url = get_absolute_url(reverse("document_info", args=[doc.id]))
        cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))
        text = render_to_string(
            "notify_if_small.html",
            {
                "doc_id": doc.get_rep_id(),
                "doc_url": doc_url,
                "cour_url": cour_url,
                "cour_name": doc.author.name,
                "size": format_size(doc.zip_size),
            },
        )

        logger.info(text)

        add_notification(
            Notification(
                send_to_courier=False,
                text=text,
                kind=SMALL_DOCUMENT,
                value=doc.zip_size,
                courier=doc.author,
            ),
            True,
        )


def notify_quarantine(qo):
    url = get_absolute_url(reverse("quarantine_dashboard"))
    cour_url = get_absolute_url(reverse("courier_info", args=[qo.courier.user_id]))

    text = render_to_string(
        "notify_quarantine.html",
        {
            "id": qo.id,
            "time": format_time(qo.timestamp),
            "url": url,
            "cour_url": cour_url,
            "cour_name": qo.courier.name,
        },
    )

    logger.info(text)

    add_notification(
        Notification(
            send_to_courier=False,
            text=text,
            kind=ADDED_TO_QUARANTINE,
            value=qo.timestamp,
            courier=qo.courier,
        ),
        True,
    )


def notify_if_twice(q):
    url = get_absolute_url(reverse("quarantine_dashboard"))
    cour_url = get_absolute_url(reverse("courier_info", args=[q.courier.user_id]))

    text = render_to_string(
        "notify_if_twice.html",
        {
            "id": q.id,
            "date": format_time(q.timestamp),
            "url": url,
            "cour_url": cour_url,
            "cour_name": q.courier.name,
        },
    )

    logger.info(text)

    add_notification(
        Notification(
            send_to_courier=False,
            type=REPORT_DAILY,
            text=text,
            kind=TWICE_RECIEVED_DOCUMENT,
            value=q.timestamp,
            courier=q.courier,
        ),
        False,
    )


# send notification if parent of signed package is deleted
def notify_if_deleted(doc):
    parent = doc.parent
    if parent.deleted:
        signed_url = get_absolute_url(reverse("document_info", args=[doc.id]))
        parent_url = get_absolute_url(reverse("document_info", args=[doc.parent.id]))
        cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))

        text = render_to_string(
            "notify_if_deleted.html",
            {
                "parent_id": doc.parent.get_rep_id(),
                "parent_url": parent_url,
                "signed_url": signed_url,
                "signed_id": doc.get_rep_id(),
                "cour_url": cour_url,
                "cour_name": doc.author.name,
            },
        )

        logger.info(text)

        add_notification(
            Notification(
                send_to_courier=True,
                text=text,
                kind=DELETED_PARENT,
                value=parent.timestamp,
                courier=doc.author,
            ),
            True,
        )


def notify_logs(log):
    url = get_absolute_url(reverse("logs_dashboard"))
    cour_url = get_absolute_url(
        reverse("courier_info", args=[str(log.courier.user_id)])
    )

    text = render_to_string(
        "notify_logs.html",
        {
            "id": log.id,
            "file_name": log.file_name,
            "url": url,
            "cour_url": cour_url,
            "cour_name": log.courier.name,
        },
    )

    logger.info(text)

    add_notification(
        Notification(
            send_to_courier=False,
            text=text,
            kind=LOGS_RECIEVED,
            value=log.timestamp,
            courier=log.courier,
        ),
        True,
    )


# sends notification if signed package was received too late
# after it was successfully closed
def notify_if_delayed(wizard_close_ts, package_received_ts, doc):
    diff = package_received_ts - wizard_close_ts  # difference in ms

    if diff > DELAY_NOTIFICATION_LIMIT:
        doc_url = get_absolute_url(reverse("document_info", args=[doc.id]))
        cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))

        text = render_to_string(
            "notify_if_delayed.html",
            {
                "doc_id": doc.get_rep_id(),
                "doc_url": doc_url,
                "delay": format_timespan(diff),
                "cour_url": cour_url,
                "cour_name": doc.author.name,
            },
        )

        logger.info(text)

        add_notification(
            Notification(
                send_to_courier=True,
                text=text,
                kind=DOCUMENT_DELAY,
                value=diff,
                courier=doc.author,
            ),
            True,
        )


# sends notification if package was not obtained for
# longer than defined amount of time
def notify_not_obtained(doc, diff):
    doc_url = get_absolute_url(reverse("document_info", args=[doc.id]))
    cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))

    text = render_to_string(
        "notify_not_obtained.html",
        {
            "doc_id": doc.get_rep_id(),
            "doc_url": doc_url,
            "delay": format_timespan(diff),
            "cour_url": cour_url,
            "cour_name": doc.author.name,
        },
    )

    logger.info(text)

    add_notification(
        Notification(
            send_to_courier=False,
            text=text,
            document=doc,
            kind=NOT_OBTAINED,
            value=diff,
            courier=doc.author,
        ),
        True,
    )


def notify_long_signing(wizard_start_ts, wizard_close_ts, doc):
    diff = wizard_close_ts - wizard_start_ts

    if diff > SIGNING_PROCESS_TIME_LIMIT:
        doc_url = get_absolute_url(reverse("document_info", args=[doc.id]))
        cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))

        text = render_to_string(
            "notify_long_signing.html",
            {
                "doc_id": doc.get_rep_id(),
                "doc_url": doc_url,
                "delay": format_timespan(diff),
                "cour_url": cour_url,
                "cour_name": doc.author.name,
            },
        )

        logger.info(text)

        add_notification(
            Notification(
                send_to_courier=True,
                type=REPORT_WEEKLY,
                text=text,
                kind=LONG_SIGNING,
                value=diff,
                courier=doc.author,
            ),
            False,
        )


# time in tablet is way forward
def notify_time_tablet(wizard_close_ts, package_recieved_ts, doc):
    diff = wizard_close_ts - package_recieved_ts

    if diff > TABLET_TIME_SHIFT:
        doc_url = get_absolute_url(reverse("document_info", args=[doc.id]))
        cour_url = get_absolute_url(reverse("courier_info", args=[doc.author.user_id]))

        text = render_to_string(
            "notify_time_tablet.html",
            {
                "doc_id": doc.get_rep_id(),
                "doc_url": doc_url,
                "delay": format_timespan(diff),
                "cour_url": cour_url,
                "cour_name": doc.author.name,
            },
        )

        logger.info(text)

        add_notification(
            Notification(
                send_to_courier=True,
                type=REPORT_DAILY,
                text=text,
                kind=TIME_SHIFT,
                value=diff,
                courier=doc.author,
            ),
            False,
        )


def notify_uploaded_decrease(customer):
    url = get_absolute_url(reverse("dashboard"))

    cust_name = customer.name

    text = render_to_string(
        "notify_uploaded_decrease.html", {"url": url, "customer": cust_name}
    )

    logger.info(text)

    add_notification(
        Notification(send_to_courier=False, text=text, kind=UPLOADED_DECREASE, value=0),
        True,
    )


def notify_assign_ratio(perc):
    url = get_absolute_url(reverse("dashboard"))

    text = render_to_string("notify_assign_ratio.html", {"url": url, "perc": perc})

    logger.info(text)

    add_notification(
        Notification(send_to_courier=False, text=text, kind=ASSIGN_RATIO, value=0), True
    )


def notify_remotely_signed_not_uploaded(documents, actions):
    text = render_to_string(
        "notify_signed_not_uploaded.html",
        {
            "documents": {
                doc.id: {
                    "action": actions[doc.id],
                    "url": get_absolute_url(reverse("document_info", args=[doc.id])),
                }
                for doc in documents
            }
        },
    )

    add_notification(
        Notification(
            send_to_courier=False,
            text=text,
            kind=SIGNED_NOT_UPLOADED,
            value=len(documents),
        ),
        True,
    )
