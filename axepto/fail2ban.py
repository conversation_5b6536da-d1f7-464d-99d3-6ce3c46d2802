from datetime import datetime, timedelta
from typing import Optional

import pytz
from django.contrib.auth.models import User
from django.db.models import Q
from django.http import HttpRequest, HttpResponse
from setty import config

from axepto.constants import permissions_groups
from axepto.helpers import json_response
from axepto.rest.models import Ban, LoginAttempt


def ban_response(retry_after: float) -> HttpResponse:
    return json_response(
        {"error": "account/ip is banned"}, 429, headers={"Retry-After": retry_after}
    )


def is_banned(request: HttpRequest, username: str) -> Optional[HttpResponse]:
    """Check if user/ip is banned
    If they are not and unsuccessfully tried to login more than X
    times in last Y minutes we ban them
    """
    now = datetime.now(tz=pytz.timezone("UTC"))
    user = User.objects.filter(username=username).first()
    ip = request.META.get("REMOTE_ADDR", "X")

    ban = Ban.objects.filter(
        Q(valid_from__lte=now),
        Q(valid_to__gte=now),
        Q(ip=ip) | Q(user=user),
        Q(active=True),
    ).first()
    if ban:
        return ban_response((ban.valid_to - now).total_seconds())

    if user:
        for group in permissions_groups:
            if (
                hasattr(user, group)
                and LoginAttempt.objects.filter(
                    successful=False,
                    username=username,
                    timestamp__gte=now
                    - timedelta(seconds=config.BAN_CONFIG[group]["cooloff_time"]),
                ).count()
                >= config.BAN_CONFIG[group]["fail_limit"]
            ):
                ban_duration = timedelta(
                    seconds=config.BAN_CONFIG[group]["ban_duration"]
                )
                Ban(
                    user=user,
                    valid_from=now,
                    valid_to=now + ban_duration,
                ).save()

                return ban_response(ban_duration.total_seconds())

    if (
        LoginAttempt.objects.filter(
            successful=False,
            ip=ip,
            timestamp__gte=now
            - timedelta(seconds=config.BAN_CONFIG["ip"]["cooloff_time"]),
        ).count()
        >= config.BAN_CONFIG["ip"]["fail_limit"]
    ):
        ban_duration = timedelta(seconds=config.BAN_CONFIG["ip"]["ban_duration"])
        Ban(
            ip=ip,
            valid_from=now,
            valid_to=now + ban_duration,
        ).save()

        return ban_response(ban_duration.total_seconds())

    return None


def unban(ban_id: int) -> None:
    ban = Ban.objects.filter(id=ban_id).prefetch_related("user").first()

    if ban:
        ban.active = False
        ban.save(update_fields=["active"])

        now = datetime.now()
        if ban.user:
            for group in permissions_groups:
                if hasattr(ban.user, group):
                    LoginAttempt.objects.filter(
                        successful=False,
                        username=ban.user.username,
                        timestamp__gte=now
                        - timedelta(seconds=config.BAN_CONFIG[group]["cooloff_time"]),
                    ).delete()

        if ban.ip:
            LoginAttempt.objects.filter(
                successful=False,
                ip=ban.ip,
                timestamp__gte=now
                - timedelta(seconds=config.BAN_CONFIG["ip"]["cooloff_time"]),
            ).delete()


def can_user_login(request: HttpRequest, username: str, user: User) -> bool:
    can_login = True

    if user is None:
        can_login = False

    if hasattr(user, "courier"):
        can_login &= user.courier.active and not user.courier.deleted

    LoginAttempt(
        username=username, ip=request.META.get("REMOTE_ADDR"), successful=can_login
    ).save()

    return can_login
