import re
from enum import Enum

EXCEL_DATETIME_FORMAT = "%d.%m.%Y %H:%M:%S"
WEB_DATETIME_FORMAT = "%y/%m/%d - %H:%M:%S"
FORM_DATETIME_FORMAT = "%Y/%m/%d %H:%M:%S"
EXCEL_DATE_FORMAT = "%d.%m.%Y"
WEB_DATE_FORMAT = "%Y/%m/%d"

courier = "courier"
operator = "operator"
manager = "manager"
client = "client"

permissions_groups = [courier, client, operator, manager]

web_permissions = {
    "dashboard": [courier, operator, manager, client],
    "archive": [manager],
    "summary": [manager],
    "couriers": [manager, client],
    "quarantine": [manager],
    "history": [manager, client],
    "logs": [manager],
    "document_info": [operator, manager, client],
    "courier_info": [manager, client],
    "courier_edit": [manager, client],
    "courier_delete": [manager, client],
    "courier_add": [manager, client],
    "see_deleted_documents": [manager, operator, client],
    "surveys": [manager, client],
    "see_tracking_data": [manager],  # in documents info wizard start, close
    "survey_add": [manager],
    "survey_edit": [manager],
    "survey_delete": [manager],
    "see_all_customer_documents": [manager, operator, client],
    "see_documents_of_all_customers": [manager, client, courier],
    "settings": [manager],
    "audit": [manager],
    "audit_status": [manager],
    "bans": [manager],
}

rest_permissions = {
    "driver_list": [courier],
    "driver_list_add": [courier],
    "document_unsigned_get": [manager, courier],
    "document_unsigned_post": [operator],
    "document_unsigned_delete": [operator],
    "list_signed_since": [operator],
    "list_unsigned_since": [operator],
    "document_signed_post": [courier],
    "document_signed_get": [operator, manager],
    "document_signed_delete": [operator],
    "assign": [operator],
    "quarantine": [manager],
    "quarantine_delete": [manager],
    "logs_post": [courier],
    "logs_get": [manager],
    "survey_post": [courier],
    "auth": [courier],
    "acknowledge_document": [courier],
    "register": [client, manager],
    "change_password": [client, manager],
}

# actions in system for history evidence

ASSIGN = "ASSIGN"
DELETE = "DELETE"
DOWNLOAD = "DOWNLOAD"
LOGIN_REST = "LOGIN_REST"
SIGNED_REMOTELY = "SIGNED_REMOTELY"
ACKNOWLEDGED = "ACKNOWLEDGED"

ACTION_CHOICES = [
    (ASSIGN, "Assign"),
    (DELETE, "Delete"),
    (DOWNLOAD, "Download"),
    (LOGIN_REST, "Login"),
    (SIGNED_REMOTELY, "Signed remotely"),
    (ACKNOWLEDGED, "ACKNOWLEDGED"),
]

ACTION_TRANS = {
    ASSIGN: "ASSIGN",
    DELETE: "DELETE",
    DOWNLOAD: "DOWNLOAD",
    LOGIN_REST: "LOGIN",
    SIGNED_REMOTELY: "SIGNED REMOTELY",
    ACKNOWLEDGED: "ACKNOWLEDGED",
}

REPORT_DAILY = "REPORT_DAILY"
REPORT_WEEKLY = "REPORT_WEEKLY"

EMAIL_NOTIFICATION_TYPES = [
    (REPORT_DAILY, "Report daily"),
    (REPORT_WEEKLY, "Report weekly"),
]


NOT_OBTAINED = "NOT_OBTAINED"
SMALL_DOCUMENT = "SMALL_DOCUMENT"
ADDED_TO_QUARANTINE = "ADDED_TO_QUARANTINE"
TWICE_RECIEVED_DOCUMENT = "TWICE_RECIEVED_DOCUMENT"
DELETED_PARENT = "DELETED_PARENT"
LONG_SIGNING = "LONG_SIGNING"
TIME_SHIFT = "TIME_SHIFT"
LOGS_RECIEVED = "LOGS_RECIEVED"
DOCUMENT_DELAY = "DOCUMENT_DELAY"
UPLOADED_DECREASE = "UPLOADED_DECREASE"
ASSIGN_RATIO = "ASSIGN_RATIO"
SIGNED_NOT_UPLOADED = "SIGNED_NOT_UPLOADED"


EMAIL_NOTIFICATION_KIND_CHOICES = [
    (NOT_OBTAINED, "Not obtained"),
    (SMALL_DOCUMENT, "Small document"),
    (ADDED_TO_QUARANTINE, "Document added to quarantine"),
    (TWICE_RECIEVED_DOCUMENT, "Document recieved twice"),
    (DELETED_PARENT, "Deleted parent"),
    (LONG_SIGNING, "Long signing"),
    (TIME_SHIFT, "Time shift"),
    (LOGS_RECIEVED, "Logs recieved"),
    (DOCUMENT_DELAY, "Document delay"),
    (UPLOADED_DECREASE, "Number of uploaded packages decreased"),
    (ASSIGN_RATIO, "Assign ratio"),
]


class SUMMARY_TYPES(Enum):
    unsigned = "UNSIGNED"
    signed = "SIGNED"
    assign = ASSIGN


MIDNIGHT_SUMMARY = [SUMMARY_TYPES.signed, SUMMARY_TYPES.unsigned]

FILENAME_VALIDATION_PATTERN = re.compile(r"^[a-zA-Z0-9$_]+\.zip$")

ALLOWED_FILES_IN_UNSIGNED_DOCUMENTS = re.compile(r"^metadata\.json|document\.zip$")
REQUIRED_FILES_IN_UNSIGNED_DOCUMENTS = ("metadata.json", "document.zip")

ALLOWED_FILES_IN_SIGNED_DOCUMENTS = re.compile(
    r"^(?:metadata|tracking)\.json|document\.zip$"
)
REQUIRED_FILES_IN_SIGNED_DOCUMENTS = ["tracking.json", "metadata.json", "document.zip"]

ALLOWED_FILES_IN_DOCUMENT_ZIP = (
    r"^(?:(?:metadata|pmetadata)\.json|scans.pdf|scans\/|scans\/[0-9]+\.jpg|{})$"
)
