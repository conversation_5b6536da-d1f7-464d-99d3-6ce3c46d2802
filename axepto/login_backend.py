from django.contrib.auth.backends import ModelBackend, UserModel


class PrefetchModelBackend(ModelBackend):
    def get_user(self, user_id):
        try:
            user = UserModel._default_manager.select_related(
                "courier", "manager", "operator__customer", "client"
            ).get(pk=user_id)
        except UserModel.DoesNotExist:
            return None
        return user if self.user_can_authenticate(user) else None
