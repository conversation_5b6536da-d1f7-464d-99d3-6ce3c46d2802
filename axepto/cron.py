import logging
import os
import subprocess
import traceback
from datetime import datetime
from functools import wraps

from django.conf import settings
from django.http import HttpResponse
from django.views import View
from django_cron import CronJobBase, Schedule
from sentry_sdk import capture_exception  # type: ignore

from axepto.archive.helpers import archive_documents
from axepto.audit.helpers import create_exports
from axepto.constants import DELETE, MIDNIGHT_SUMMARY, SUMMARY_TYPES
from axepto.helpers import get_utc_tz
from axepto.library.courier_notifications import send_cc_notifications
from axepto.library.tasks import (
    check_assign_ratio,
    check_certificates,
    check_not_obtained,
    check_remotely_signed_not_uploaded,
    check_uploaded_amount,
    daily_summary,
    document_summary,
    handle_email_notifications,
    history_summary,
    remove_old_quarantine_documents,
    remove_old_signed_documents,
    send_union_notifications,
)
from axepto.library.telekom_notifications import send_telekom_notifications
from axepto.rest.helpers import clean_driver_list, remove_old_unsigned_documents
from axepto.rest.models import Customer, Document

SEND_INTIME_NOTIFICATIONS = getattr(settings, "SEND_INTIME_NOTIFICATIONS", False)
SEND_TELEKOM_NOTIFICATIONS = getattr(settings, "SEND_TELEKOM_NOTIFICATIONS", False)
SEND_EMAILS = getattr(settings, "SEND_EMAILS", False)

logger = logging.getLogger(__name__)


def cron_job_logger(func):
    @wraps(func)
    def inner(cron_job):
        logger.info(f"Starting cron job {cron_job.code}")
        func(cron_job)
        logger.info(f"Finished cron job {cron_job.code}")

    return inner


def send_exceptions_to_sentry(func):
    @wraps(func)
    def inner(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(traceback.format_exc())
            capture_exception(e)
            raise

    return inner


class CronRunView(View):
    def get(self, request):
        subprocess.Popen(
            [
                "python3",
                os.path.abspath(
                    os.path.join(os.path.dirname(__file__), "../manage.py")
                ),
                "runcrons",
            ]
        ).wait(timeout=settings.CRON_TIMEOUT)
        return HttpResponse()


class DeleteOldDocumentsJob(CronJobBase):
    dt = datetime.now()
    dt = dt.replace(hour=0, minute=59, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)
    code = "axepto.delete_old_documents_job"  # a unique code

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        customers = Customer.objects.filter(active=True).all()
        remove_old_unsigned_documents(customers)
        remove_old_signed_documents(customers)
        remove_old_quarantine_documents(customers)


class CleanDriverList(CronJobBase):
    dt = datetime.now()
    dt = dt.replace(hour=22, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]

    schedule = Schedule(run_at_times=RUN_AT_TIMES)
    code = "axepto.clean_driver_list_job"  # a unique code

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        daily_summary([SUMMARY_TYPES.assign], history_summary)
        clean_driver_list()


class SendCourierNotifications(CronJobBase):
    RUN_EVERY_MINS = 10
    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)

    code = "axepto.send_courier_notifications"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        if SEND_INTIME_NOTIFICATIONS:
            send_cc_notifications()


class SendTelekomNotifications(CronJobBase):
    RUN_EVERY_MINS = 10
    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)

    code = "axepto.send_telekom_notifications"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        if SEND_TELEKOM_NOTIFICATIONS:
            send_telekom_notifications()


class CheckNotObtained(CronJobBase):
    RUN_EVERY_MINS = 10
    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)

    code = "axepto.check_not_obtained"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        check_not_obtained()


class SendEmailNotifications(CronJobBase):
    dt = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.send_email_notifications"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        if SEND_EMAILS:
            handle_email_notifications()


class CheckUploadedAmount(CronJobBase):
    dt = datetime.now().replace(hour=12, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.check_uploaded_amount"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        check_uploaded_amount()


class CheckAssignRatio(CronJobBase):
    dt = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.check_assign_ratio"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        check_assign_ratio()


class CreateAuditExports(CronJobBase):
    dt = datetime.now().replace(hour=23, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.create_audit_exports"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        create_exports()


class CheckSignedNotUploadedDocuments(CronJobBase):
    dt = datetime.now().replace(hour=15, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.check_signed_not_uploaded_documents"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        check_remotely_signed_not_uploaded()


class DailySummary(CronJobBase):
    dt = datetime.now().replace(hour=23, minute=59, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.daily_summary"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        daily_summary(MIDNIGHT_SUMMARY, document_summary)


class CheckCertificates(CronJobBase):
    dt = datetime.now().replace(hour=8, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.check_certificates"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        if settings.CERT_CHECKER_SLACK_URL:
            check_certificates()


class SendUnionNotifications(CronJobBase):
    RUN_EVERY_MINS = 10
    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)

    code = "axepto.send_union_notifications"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        if settings.UNION_NOTIFICATIONS_HOST:
            send_union_notifications()


class HistoryDeleteFix(CronJobBase):
    dt = datetime.now().replace(hour=1, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.history_delete_fix"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        documents_to_delete = Document.objects.filter(
            deleted=False, history__action=DELETE
        ).all()

        for document in documents_to_delete:
            logger.info(f"Fixing delete status from history for document {document.id}")
            document.deleted = True
            document.save(update_fields=["deleted"])


class ArchiveDocuments(CronJobBase):
    dt = datetime.now().replace(hour=1, minute=0, second=0, microsecond=0)
    dt = get_utc_tz(dt)
    RUN_AT_TIMES = ["%02d:%02d" % (dt.hour, dt.minute)]
    schedule = Schedule(run_at_times=RUN_AT_TIMES)

    code = "axepto.archive_documents"

    @send_exceptions_to_sentry
    @cron_job_logger
    def do(self):
        archive_documents()
