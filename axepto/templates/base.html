{% load static %}
<!doctype html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Axepto</title>
        <meta name="description" content="Axepto">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <link rel="shortcut icon" type="image/png" href="{% static 'favicon.ico' %}"/>
        <link rel="stylesheet" type="text/css" class="js-stylesheet" href="{% if request.COOKIES.theme == "dark" %}{% static 'web/dark.css' %}{% else %}{% static 'web/light.css' %}{% endif %}">
        <script src="{% static "web/settings.js" %}"></script>

        {% load user_data %}
    </head>
    <body>
        <div class="wrapper">
            {% block navigation %}
                <nav id="sidebar" class="sidebar js-sidebar ">
                    <script>
                      (() => {
                          let sidebarCollapsed = localStorage.getItem("sidebarCollapsed") === "true"
                          if(sidebarCollapsed) {
                            const sidebar = document.querySelector(".js-sidebar")
                            sidebar.classList.add("collapsed")
                          }
                          document.addEventListener("DOMContentLoaded", () => {
                            const toggle = document.querySelector(".js-sidebar-toggle")
                            toggle.addEventListener("click", () => {
                              sidebarCollapsed = !sidebarCollapsed
                              localStorage.setItem("sidebarCollapsed", sidebarCollapsed)
                            })
                            for(const link of document.querySelectorAll(".sidebar-link")) {
                              link.addEventListener("click", () => {
                                localStorage.setItem("sidebarCollapsed", false)
                              })
                            }
                          })
                        })()
                    </script>
                    <div class="sidebar-content js-simplebar" data-simplebar>
                        <div class="simplebar-height-auto-observer-wrapper">
                            <div class="simplebar-height-auto-observer"></div>
                        </div>
                        <div class="simplebar-mask">
                            <div class="simplebar-offset">
                                <div class="simplebar-content-wrapper">
                                    <div class="simplebar-content">
                                        <div class="sidebar-brand">
                                            <img src="{% static 'logo_dashboard_h.svg' %}" class="w-100 sidebar-brand-text" style="max-width: 220px">
                                            <img src="{% static 'logo_icon.svg' %}" class="w-100 sidebar-brand-icon" style="max-width: 26px">
                                            <script>
                                                const sidebarLogo = document.querySelector(".sidebar-brand-text")
                                                const img = localStorage.getItem("adminkit_config_theme") === "light" ? "{% static 'logo_dashboard_h_light.svg' %}" : "{% static 'logo_dashboard_h.svg' %}"
                                                sidebarLogo.setAttribute("src", img)
                                            </script>
                                        </div>
                                        <div class="sidebar-user">
                                            <div class="d-flex justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <span class="avatar img-fluid rounded me-1 d-flex justify-content-center align-items-center">
                                                        <i class="fas fa-user" style="font-size: 36px"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1 ps-2 fw-bold">
                                                    <div class="sidebar-user-title">{% get_user_name request %}</div>
                                                    <div class="sidebar-user-subtitle fst-italic">{% get_user_perms request %}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <ul class="sidebar-nav">
                                            {% if menu_.dashboard %}
                                            <li class="sidebar-item {% block nav-dash %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'dashboard' %}">
                                                    <i class="fas fa-table-list"></i>
                                                    <span class="align-middle">Parcel list</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.archive %}
                                            <li class="sidebar-item {% block nav-arch %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'archive' %}">
                                                    <i class="fas fa-inbox"></i>
                                                    <span class="align-middle">Archive</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.summary %}
                                            <li class="sidebar-item {% block nav-summ %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'web_summary' %}">
                                                    <i class="fas fa-desktop"></i>
                                                    <span class="align-middle">Summary</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.couriers %}
                                            <li class="sidebar-item {% block nav-cours %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'couriers' %}">
                                                    <i class="fas fa-users"></i>
                                                    <span class="align-middle">Couriers</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.bans %}
                                            <li class="sidebar-item {% block nav-bans %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'bans' %}">
                                                    <i class="fas fa-ban"></i>
                                                    <span class="align-middle">Bans</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.quarantine %}
                                            <li class="sidebar-item {% block nav-quar %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'quarantine_dashboard' %}">
                                                    <i class="fas fa-shield"></i>
                                                    <span class="align-middle">Quarantine</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.surveys %}
                                            <li class="sidebar-item {% block nav-surveys %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'surveys_dashboard' %}">
                                                    <i class="fas fa-square-poll-horizontal"></i>
                                                    <span class="align-middle">Surveys</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.logs %}
                                            <li class="sidebar-item {% block nav-logs %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'logs_dashboard' %}">
                                                    <i class="fas fa-file"></i>
                                                    <span class="align-middle">Logs</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.audit and setty.AUDIT_ENABLED %}
                                            <li class="sidebar-item {% block nav-audit %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'audit' %}">
                                                    <i class="fas fa-file-circle-check"></i>
                                                    <span class="align-middle">Audit</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                            {% if menu_.settings %}
                                            <li class="sidebar-item {% block nav-settings %}{% endblock %}">
                                                <a class="sidebar-link" href="{% url 'settings' %}">
                                                    <i class="fas fa-gear"></i>
                                                    <span class="align-middle">Settings</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                        </ul>
                                        <div class="sidebar-cta">
                                            <div class="sidebar-item">
                                                <a class="sidebar-link" href="{% url 'web_logout' %}">
                                                    <i class="fas fa-right-from-bracket"></i>
                                                    <span class="align-middle">Logout</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            {% endblock%}

            <script src="{% static "web/app.js" %}"></script>
            <div class="main overflow-visible">
                {% block content %}
                {% endblock %}
            </div>
        </div>
    </body>
</html>
