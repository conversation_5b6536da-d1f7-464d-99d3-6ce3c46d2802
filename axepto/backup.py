import datetime

from boto.ec2 import connect_to_region
from boto.utils import get_instance_metadata
from django.conf import settings

REGION = settings.AWS_REGION
MIN_BACKUP_INTERVAL = settings.MIN_BACKUP_INTERVAL
AWS_DATE_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
PREFIX = "axepto_auto_backup"


def instance_name(conn, instance_id):
    instance = conn.get_all_instances(instance_id)[0].instances[0]
    return instance.tags["Name"]


def backup_volume(volume, description):
    volume.create_snapshot(description)
    print(
        "Created snapshot of volume %s with description: %s" % (volume.id, description)
    )


def outdated(snapshot, date_format, min_backup_interval):
    d = datetime.datetime.strptime(snapshot.start_time, date_format)
    return (d + min_backup_interval) < datetime.datetime.now()


def is_auto_backup(snapshot, prefix):
    return snapshot.description.startswith(prefix)


def purge_old_backups(volume, can_purge):
    to_delete = [s for s in volume.snapshots() if can_purge(s)]
    print("Deleting %s old backups for volume id %s" % (len(to_delete), volume.id))
    for s in to_delete:
        print("\tDeleting snapshot created %s (id=%s)" % (s.start_time, s.id))
        s.delete()


def can_purge(s):
    return outdated(s, AWS_DATE_FORMAT, MIN_BACKUP_INTERVAL) and is_auto_backup(
        s, PREFIX
    )


def backup_ec2():
    conn = connect_to_region(
        REGION,
        is_secure=True,
    )
    metadata = get_instance_metadata()
    if metadata:
        instance_id = metadata["instance-id"]
        ins_name = instance_name(conn, instance_id)
        print("Backing up instance %s (%s)" % (ins_name, instance_id))
        instance_volumes = conn.get_all_volumes(
            filters={"attachment.instance-id": instance_id}
        )

        for v in instance_volumes:
            description = "%s for %s (volume id: %s)" % (PREFIX, ins_name, v.id)
            backup_volume(v, description)
            purge_old_backups(v, can_purge)
