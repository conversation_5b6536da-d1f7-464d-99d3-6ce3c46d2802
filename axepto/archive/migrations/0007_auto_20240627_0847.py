# Generated by Django 3.2.8 on 2024-06-27 08:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("archive", "0006_auto_20210906_1317"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="documentarchive",
            options={},
        ),
        migrations.AddField(
            model_name="documentarchive",
            name="error",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name="documentarchive",
            name="original_file_name",
            field=models.CharField(blank=True, default="", max_length=120),
        ),
        migrations.AddField(
            model_name="documentarchive",
            name="signed_locally",
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AddField(
            model_name="documentarchive",
            name="signed_remotely",
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="documentactionsarchive",
            name="document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="history",
                to="archive.documentarchive",
            ),
        ),
    ]
