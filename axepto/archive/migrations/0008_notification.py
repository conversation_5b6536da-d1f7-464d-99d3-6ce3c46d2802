# Generated by Django 3.2.8 on 2024-07-01 09:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rest", "0082_customer_has_password_protected_packages"),
        ("archive", "0007_auto_20240627_0847"),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationArchive",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("REPORT_DAILY", "Report daily"),
                            ("REPORT_WEEKLY", "Report weekly"),
                        ],
                        default=None,
                        max_length=25,
                        null=True,
                    ),
                ),
                (
                    "kind",
                    models.CharField(
                        choices=[
                            ("NOT_OBTAINED", "Not obtained"),
                            ("SMALL_DOCUMENT", "Small document"),
                            ("ADDED_TO_QUARANTINE", "Document added to quarantine"),
                            ("TWICE_RECIEVED_DOCUMENT", "Document recieved twice"),
                            ("DELETED_PARENT", "Deleted parent"),
                            ("LONG_SIGNING", "Long signing"),
                            ("TIME_SHIFT", "Time shift"),
                            ("LOGS_RECIEVED", "Logs recieved"),
                            ("DOCUMENT_DELAY", "Document delay"),
                            (
                                "UPLOADED_DECREASE",
                                "Number of uploaded packages decreased",
                            ),
                            ("ASSIGN_RATIO", "Assign ratio"),
                        ],
                        default=None,
                        max_length=25,
                        null=True,
                    ),
                ),
                ("sent", models.BooleanField(default=False)),
                ("send_to_courier", models.BooleanField(default=False)),
                ("subject", models.CharField(max_length=100)),
                ("text", models.TextField()),
                ("value", models.BigIntegerField(default=0)),
                (
                    "courier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="archived_notifications",
                        to="rest.courier",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="notifications",
                        to="archive.documentarchive",
                    ),
                ),
            ],
        ),
    ]
