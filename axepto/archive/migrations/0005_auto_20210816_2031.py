# Generated by Django 2.2.10 on 2021-08-16 20:31

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("archive", "0004_auto_20191029_1530"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="documentactionsarchive",
            name="action",
            field=models.CharField(
                choices=[
                    ("ASSIGN", "Assign"),
                    ("DELETE", "Delete"),
                    ("DOWNLOAD", "Download"),
                    ("LOGIN_REST", "Login"),
                    ("SIGNED_REMOTELY", "Signed remotely"),
                    ("ACK<PERSON>OWLEDGED", "ACKNOWLEDGED"),
                ],
                default=None,
                max_length=25,
            ),
        ),
        migrations.AlterField(
            model_name="documentarchive",
            name="rest",
            field=django.contrib.postgres.fields.jsonb.JSONField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="documentarchive",
            name="tracking",
            field=django.contrib.postgres.fields.jsonb.JSONField(
                blank=True, default=dict, null=True
            ),
        ),
    ]
