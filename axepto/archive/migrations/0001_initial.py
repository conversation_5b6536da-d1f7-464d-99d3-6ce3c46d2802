# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-10-22 08:09
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("rest", "0059_auto_20191015_1448"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentActionsArchive",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("ASSIGN", "Assign"),
                            ("DELETE", "Delete"),
                            ("DOWNLOAD", "Download"),
                            ("LOGIN_REST", "Login"),
                        ],
                        default=None,
                        max_length=25,
                    ),
                ),
                ("auto", models.NullBooleanField()),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="archived_author_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DocumentArchive",
            fields=[
                (
                    "id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default="",
                        max_length=100,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "id_v2",
                    models.CharField(
                        blank=True, db_index=True, default="", max_length=100
                    ),
                ),
                ("timestamp", models.BigIntegerField(db_index=True, default=0)),
                ("client_id", models.CharField(blank=True, default="", max_length=100)),
                (
                    "rest",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default={}, null=True
                    ),
                ),
                (
                    "tracking",
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default={}, null=True
                    ),
                ),
                ("version", models.CharField(blank=True, default="", max_length=15)),
                ("zip_size", models.BigIntegerField(db_index=True, default=0)),
                ("deleted", models.BooleanField(default=False)),
                ("hard_delete", models.BooleanField(default=False)),
                ("notified", models.BooleanField(default=False)),
                ("operator_notification", models.BooleanField(default=False)),
                (
                    "internal_id",
                    models.CharField(blank=True, default="", max_length=100),
                ),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="archived_documents",
                        to="rest.Courier",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="archived_documents",
                        to="rest.Customer",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="archive.DocumentArchive",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="documentactionsarchive",
            name="document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="archived_document_history",
                to="archive.DocumentArchive",
            ),
        ),
        migrations.AddField(
            model_name="documentactionsarchive",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="archived_user_history",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
