from django.views.generic import DetailView, ListView
from django.views.generic.edit import FormMixin

from axepto.archive.models import DocumentActionsArchive, DocumentArchive
from axepto.constants import web_permissions
from axepto.web.helpers import specific_login_required
from axepto.web.views import render_dashboard, render_document_info


class ArchiveView(FormMixin, ListView):
    @specific_login_required(web_permissions["archive"])
    def get(self, request):
        return render_dashboard(
            request, DocumentArchive, True, "Archive", "archive_item"
        )


class ArchiveItemView(DetailView):
    @specific_login_required(web_permissions["archive"])
    def get(self, request, pk):
        return render_document_info(
            request, pk, DocumentArchive, DocumentActionsArchive, True, "archive_item"
        )
