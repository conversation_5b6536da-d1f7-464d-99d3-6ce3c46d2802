import datetime

from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import (
    SET_NULL,
    BigIntegerField,
    BooleanField,
    CharField,
    DateTimeField,
    ForeignKey,
    JSONField,
    Model,
    TextField,
)
from humanfriendly import format_size

from axepto.constants import (
    ACTION_CHOICES,
    ACTION_TRANS,
    EMAIL_NOTIFICATION_KIND_CHOICES,
    EMAIL_NOTIFICATION_TYPES,
    WEB_DATETIME_FORMAT,
)
from axepto.helpers import get_ba_tz
from axepto.rest.models import (
    Courier,
    Customer,
    DocumentQuerySet,
    deleted_rest,
    get_field,
)
from axepto.rest.models_helpers import get_city, prepare_tracking_data


class DocumentArchive(Model):
    objects = DocumentQuerySet.as_manager()

    id: CharField = CharField(
        max_length=100,
        blank=True,
        unique=True,
        default="",
        primary_key=True,
        db_index=True,
    )

    id_v2: CharField = <PERSON>r<PERSON>ield(
        max_length=100, null=True, blank=True, default="", db_index=True
    )

    timestamp: BigIntegerField = BigIntegerField(db_index=True, default=0)

    client_id: CharField = CharField(
        max_length=100, blank=True, default="", db_index=True
    )

    error: JSONField = JSONField(default=dict, null=True, blank=True)

    rest: JSONField = JSONField(default=dict, null=True, blank=True)

    tracking: JSONField = JSONField(default=dict, null=True, blank=True)

    parent: ForeignKey = ForeignKey(
        "self",
        null=True,
        blank=True,
        related_name="children",
        on_delete=SET_NULL,
    )

    signed_locally: BooleanField = BooleanField(
        null=False,
        blank=True,
        default=False,
    )

    signed_remotely: BooleanField = BooleanField(
        null=False,
        blank=True,
        default=False,
    )

    author: ForeignKey = ForeignKey(
        Courier,
        related_name="archived_documents",
        null=True,
        blank=True,
        on_delete=SET_NULL,
    )

    customer: ForeignKey = ForeignKey(
        Customer,
        null=True,
        blank=True,
        related_name="archived_documents",
        on_delete=SET_NULL,
    )

    version: CharField = CharField(max_length=15, blank=True, default="")

    original_file_name: CharField = CharField(max_length=120, blank=True, default="")

    zip_size: BigIntegerField = BigIntegerField(db_index=True, default=0)

    deleted: BooleanField = BooleanField(default=False)

    hard_delete: BooleanField = BooleanField(default=False)

    notified: BooleanField = BooleanField(default=False)

    operator_notification: BooleanField = BooleanField(default=False)

    internal_id: CharField = CharField(max_length=100, blank=True, default="")

    def get_rep_id(self):
        return self.client_id if self.client_id else self.id

    def is_unsigned(self):
        return self.parent is None

    def is_error(self):
        return bool(self.error)

    def is_signed(self):
        return not self.is_error() and not self.is_unsigned()

    def get_error_code(self):
        if self.is_error():
            return self.error["code"]
        else:
            return None

    def get_error_code_msg(self):
        if self.is_error():
            return self.error["text"]
        else:
            return None

    # Returns time of upload in Bratislava, in format Y/M/D - H:M:S if dt is false
    # if dt is true returns time in datetime format
    def get_time_formatted(self, dt=False):

        res = get_ba_tz(datetime.datetime.fromtimestamp(self.timestamp / 1000))

        if dt:
            # xlwt cant work with tz aware datetime
            return res.replace(tzinfo=None)

        return res.strftime(WEB_DATETIME_FORMAT)

    def get_expiration_formatted(self, dt=False):

        res = get_ba_tz(
            datetime.datetime.fromtimestamp(self.timestamp / 1000).replace(
                hour=21, minute=0, second=0
            )
            + datetime.timedelta(days=self.customer.old_unsigned_packages_removal_limit)
        )

        if dt:
            # xlwt cant work with tz aware datetime
            return res.replace(tzinfo=None)

        return res.strftime(WEB_DATETIME_FORMAT)

    def get_display_name(self):
        return get_field("fullname", self.rest, self.customer)

    def get_city_name(self):
        return get_city(self.rest, self.customer)

    def get_display_size(self):
        return format_size(self.zip_size)

    def get_data(self, field, property_name):
        if field == "rest":
            return get_field(property_name, self.rest, self.customer)
        if field == "tracking":
            return get_field(property_name, self.tracking, self.customer)

        return None

    @property
    def get_parsed_metadata(self):
        parsed = []
        for key in ["wizard-title", "address", "power-of-attorney"]:
            parsed.append(
                {
                    "key": key.replace("-", " ").title(),
                    "value": get_field(key, self.rest, self.customer),
                }
            )
        return parsed

    def get_tracking_data(self):
        return prepare_tracking_data(self.tracking)

    # If we interpret 'parent' relation as oriented edge, the representing
    # document is the most relevant document from the oriented star containing
    # this document. Resolved in this order:
    # 1. the newest signed version, if such version exists
    # 2. the newest error version, if such version exists
    # 3. the unsigned document itself
    def get_representing_doc(self):
        if self.is_unsigned():
            # Primarily, return last successfully signed document
            signed_doc = (
                self.children.all().filter(error={}).order_by("-timestamp").first()
            )
            if signed_doc is not None:
                return signed_doc
            else:
                # If there are no successfully signed documents, return
                # newest error document
                error_doc = self.children.all().order_by("-timestamp").first()
                if error_doc is not None:
                    return error_doc
                else:
                    # There is no signed or error document for this
                    return self
        else:
            return self.parent.get_representing_doc()

    def get_status(self):
        if self.is_error():
            return "error"
        elif self.is_unsigned():
            return "unsigned"
        else:
            return "signed"

    def get_download_link(self):
        if self.is_unsigned():
            return "%s/document/unsigned/%s/" % (settings.HOST_DOMAIN, self.id)
        else:
            return "%s/document/signed/%s/" % (settings.HOST_DOMAIN, self.id)

    # A document is finished, if it is either signed or has a signed version
    def is_finished(self):
        def has_signed_child(doc):
            if doc.children.filter(error={}).exists():
                return True
            else:
                return False

        if self.is_unsigned():
            return has_signed_child(self)
        elif self.is_error():
            return has_signed_child(self.parent)
        else:
            # Document is signed => finished
            return True

    def get_parent(self):
        # if unsigned return own timestamp
        # if signed/error(has parent) return parens timestamp

        if self.parent is None:
            return self

        else:
            return self.parent

    # returns parent time if doesnt have parent returns own time
    # datetime - if true date returned in datetime format instead of string
    def get_parent_time(self, dt=False):
        return self.get_parent().get_time_formatted(dt)

    def get_signed_time(self, dt=False):
        if self.parent is not None:
            return self.get_time_formatted(dt)
        else:
            return ""

    def safe_delete_rest(self):
        self.rest = deleted_rest(self.rest)

    def __str__(self):
        return "%s-%s" % (self.id, self.get_status())


class DocumentActionsArchive(Model):
    author: ForeignKey = ForeignKey(
        User,
        related_name="archived_author_history",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    user: ForeignKey = ForeignKey(
        User,
        related_name="archived_user_history",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    document: ForeignKey = ForeignKey(
        DocumentArchive,
        related_name="history",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    created_at: DateTimeField = DateTimeField(auto_now_add=True)

    action: CharField = CharField(max_length=25, choices=ACTION_CHOICES, default=None)

    auto: BooleanField = BooleanField(blank=True, null=True)

    def get_action(self):
        return ACTION_TRANS.get(self.action, "")

    def get_date_created(self):
        return get_ba_tz(self.created_at).strftime(WEB_DATETIME_FORMAT)

    def __str__(self):
        return self.action


class NotificationArchive(Model):

    created_at: DateTimeField = DateTimeField()

    type: CharField = CharField(
        max_length=25, choices=EMAIL_NOTIFICATION_TYPES, default=None, null=True
    )

    kind: CharField = CharField(
        max_length=25, choices=EMAIL_NOTIFICATION_KIND_CHOICES, default=None, null=True
    )

    sent: BooleanField = BooleanField(default=False)

    send_to_courier: BooleanField = BooleanField(default=False)

    subject: CharField = CharField(max_length=100)

    text: TextField = TextField()

    value: BigIntegerField = BigIntegerField(default=0)

    document: ForeignKey = ForeignKey(
        DocumentArchive,
        related_name="notifications",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    courier: ForeignKey = ForeignKey(
        Courier,
        related_name="archived_notifications",
        on_delete=SET_NULL,
        blank=True,
        null=True,
    )

    def __str__(self):
        return str(self.kind)
