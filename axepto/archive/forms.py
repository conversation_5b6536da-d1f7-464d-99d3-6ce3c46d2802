from django import forms

from axepto.rest.models import Customer


class ArchiveFilterForm(forms.Form):
    query = forms.CharField(
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "autocomplete": "off",
                "placeholder": "Search...",
            }
        ),
        required=False,
        label="Search",
    )

    customer = forms.ModelChoiceField(
        widget=forms.Select(attrs={"class": "form-control choices-single"}),
        queryset=Customer.objects.all(),
        empty_label="All",
        required=False,
        label="Customer",
    )
