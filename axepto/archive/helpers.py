from datetime import datetime, timedelta

from dateutil import tz
from django.conf import settings
from django.db import connection, transaction

from axepto.archive.models import (
    DocumentActionsArchive,
    DocumentArchive,
    NotificationArchive,
)
from axepto.helpers import date_to_timestamp
from axepto.rest.models import Document, History, Notification

ID_JOIN = "', '"
ARCHIVE_DOCUMENT_TABLE_NAME = DocumentArchive._meta.db_table
ARCHIVE_HISTORY_TABLE_NAME = DocumentActionsArchive._meta.db_table
ARCHIVE_NOTIFICATION_TABLE_NAME = NotificationArchive._meta.db_table
DOCUMENT_TABLE_NAME = Document._meta.db_table
HISTORY_TABLE_NAME = History._meta.db_table
NOTIFICATION_TABLE_NAME = Notification._meta.db_table


def archive_documents():
    with connection.cursor() as cursor:
        cursor.execute(
            f"SELECT column_name FROM information_schema.columns "
            f"WHERE table_name = '{ARCHIVE_DOCUMENT_TABLE_NAME}' "
            f"ORDER BY ordinal_position",
        )
        document_columns = [row[0] for row in cursor.fetchall()]
        cursor.execute(
            f"SELECT column_name FROM information_schema.columns "
            f"WHERE table_name = '{ARCHIVE_HISTORY_TABLE_NAME}' "
            f"ORDER BY ordinal_position",
        )
        history_columns = [row[0] for row in cursor.fetchall()]
        cursor.execute(
            f"SELECT column_name FROM information_schema.columns "
            f"WHERE table_name = '{ARCHIVE_NOTIFICATION_TABLE_NAME}' "
            f"ORDER BY ordinal_position",
        )
        notification_columns = [row[0] for row in cursor.fetchall()]

    while True:
        documents = (
            Document.objects.signed(False)
            .until(
                date_to_timestamp(
                    datetime.now(tz=tz.gettz("Europe/Bratislava"))
                    - timedelta(days=settings.ARCHIVE_OLDER_THAN_DAYS)
                )
            )
            .values_list("id", flat=True)
        )[: settings.ARCHIVE_BATCH_SIZE]
        if len(documents) == 0:
            break

        with transaction.atomic():
            with connection.cursor() as cursor:
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_DOCUMENT_TABLE_NAME} "
                    f"({f', '.join(document_columns)}) "
                    f"SELECT {f', '.join(document_columns)} "
                    f"FROM {DOCUMENT_TABLE_NAME} "
                    f"WHERE id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_DOCUMENT_TABLE_NAME} "
                    f"({', '.join(document_columns)}) "
                    f"SELECT {f', '.join(document_columns)} "
                    f"FROM {DOCUMENT_TABLE_NAME} "
                    f"WHERE parent_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_HISTORY_TABLE_NAME} "
                    f"({', '.join(history_columns)}) "
                    f"SELECT {f', '.join(history_columns)} FROM {HISTORY_TABLE_NAME} "
                    f"WHERE document_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_HISTORY_TABLE_NAME} "
                    f"({f', '.join(history_columns)}) "
                    f"SELECT {HISTORY_TABLE_NAME}."
                    f"{f', {HISTORY_TABLE_NAME}.'.join(history_columns)} "
                    f"FROM {HISTORY_TABLE_NAME} JOIN {DOCUMENT_TABLE_NAME} "
                    f"ON {DOCUMENT_TABLE_NAME}.id = {HISTORY_TABLE_NAME}.document_id "
                    f"AND {DOCUMENT_TABLE_NAME}.parent_id "
                    f"IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_NOTIFICATION_TABLE_NAME}"
                    f"({f', '.join(notification_columns)}) "
                    f"SELECT {f', '.join(notification_columns)} "
                    f"FROM {NOTIFICATION_TABLE_NAME} "
                    f"WHERE document_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"INSERT INTO {ARCHIVE_NOTIFICATION_TABLE_NAME} "
                    f"({f', '.join(notification_columns)}) "
                    f"SELECT {NOTIFICATION_TABLE_NAME}."
                    f"{f', {NOTIFICATION_TABLE_NAME}.'.join(notification_columns)} "
                    f"FROM {NOTIFICATION_TABLE_NAME} JOIN {DOCUMENT_TABLE_NAME} "
                    f"ON {DOCUMENT_TABLE_NAME}.id "
                    f"= {NOTIFICATION_TABLE_NAME}.document_id "
                    f"AND {DOCUMENT_TABLE_NAME}.parent_id "
                    f"IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {NOTIFICATION_TABLE_NAME} "
                    f"WHERE document_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {NOTIFICATION_TABLE_NAME} "
                    f"USING {DOCUMENT_TABLE_NAME} WHERE {DOCUMENT_TABLE_NAME}.id = "
                    f"{NOTIFICATION_TABLE_NAME}.document_id "
                    f"AND {DOCUMENT_TABLE_NAME}.parent_id IN "
                    f"('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {HISTORY_TABLE_NAME} "
                    f"WHERE document_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {HISTORY_TABLE_NAME} USING {DOCUMENT_TABLE_NAME} "
                    f"WHERE {DOCUMENT_TABLE_NAME}.id = "
                    f"{HISTORY_TABLE_NAME}.document_id "
                    f"AND {DOCUMENT_TABLE_NAME}.parent_id IN "
                    f"('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {DOCUMENT_TABLE_NAME} "
                    f"WHERE parent_id IN ('{ID_JOIN.join(documents)}')"
                )
                cursor.execute(
                    f"DELETE FROM {DOCUMENT_TABLE_NAME} "
                    f"WHERE id IN ('{ID_JOIN.join(documents)}')"
                )
