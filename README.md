# Doc

 Recommended cron job `sudo crontab -e`

    */5 * * * * python3 /home/<USER>/default/manage.py runcrons > /home/<USER>/cronjob.log 2>&1

 uwsgi required during development 

    uwsgi dev.ini


## Options

`DELETE_OLD_DOCUMENTS` ineteger, in days, packages older than this constant will be deleted from database, S3 file is kept

`DELETE_HARD_OLD_DOCUMENTS` ineteger, in days, packages older than this constant will be deleted from database and S3 storage

`GET_TITLE` function, parameters metadata json given by operator, returns name of process (Contract, Loan,...)

`GET_CLIENT_ID` function, parameters customer, metadata json given by operator, returns client_id used for database

`GET_ADDRESS` function, parameters metadata json given by operator, returns customers address

`GET_CITY` function, parameters metadata json given by operator, returns customers city

`GET_FULLNAME` function, parameters metadata json given by operator, returns customers full name

`GET_DELETED_REST` function, parameters metadata json given by operator, returns json that can be kept after deleting package

`INPUT_SCHEMAS` json object, list of objects representing schema for jsonschema plugin (for more check plugin doc), metadata from operator must be compatible with this schema
 
`ALLOW_TEST_VIEWS` bool, if true testing views on address HOST_DOMAIN/test/* are available

`NOTIFICATION_MAIL` string, notification mails will be send from this address (or not)

`NOTIFICATIONS_RECIPIENTS` list of strings, notification emails will be send to those email addresses

`VALIDATE_PASSWORDS` bool, if true server will force safer passwords (8 chars, 1 uppercase, 1 lowercase and 1 number)

`DELETE_FROM_STORAGE` bool, if true, deleted packages will be removed from S3 storage

`ALLOW_BACKUPS` bool, if true, AWS instance will be backed up every 12 hours

`AWS_STORAGE_BUCKET_NAME` string, name AWS bucket where packages are stored

`DELAY_NOTIFICATION_LIMIT` timedelta, if time between signing(wizard close) and sending package(server recieve) is greater than this notification will be generated, default 5 days

`UNOBTAINED_NOTIFICATION_LIMIT` timedelta, if signed package is not downloaded for longer than this period notification will be generated, default 10 minutes

`SIGNING_PROCESS_TIME_LIMIT` timedelta, if signing process took longer than this notification will be generated, default 10 minutes

`TABLET_TIME_SHIFT` timedelta, if time of closing wizard is about this value (and more) greater than time of recieving package notification will be generated, default 2 minutes

## History

System tracks important actions in system. Currently 4 actions are available:

`ASSIGN` assigning document to user. If document was assigned by operator or manager (`/assign/` call), his account is linked by `author` attribute and `auto` is set to false. If document was assigned automatically (courier tried to download document by scanning qr/bar code) value of `auto` is set to true

`DELETE` deleting document. If `auto` attribute is true document was deleted by system (old, expired packages). If `auto` is false document was deleted by operator/manager request (account linked in `author` attribute)

`DOWNLOAD` marks downloading document so we know how many documents were used.

`LOGIN` marks login of courier

## Development

How to set up development environment locally folow this [guide](./doc/quickstart.md).

## Deployment

Instructions on how to deploy to production are in this [guide](./doc/prod_deployment.md).

Instructions on how to deploy new test environment are in this [guide](./doc/creating_test_enviroment.md).

Instructions on how to generate new signing keys for customers are in this [guide](./doc/key_generation.md).

Here is the current list of deployments:

* `produkcia` server:
  * axepto_default (the production one)

* `produkcia2` server:
  * axepto_default (the production one)

* `cron` server:
  * cronjobs for the production

* `dev` server:
  * axepto_mzone_test
  * axepto_orange_test
  * axepto_test3
  * axepto_test4
  * axepto_union_test