services:
  postgres:
    image: postgres:16-alpine
    ports:
      - 5432:5432
    environment:
      - POSTGRES_PASSWORD=axeptopassword
      - POSTGRES_USER=axepto
      - POSTGRES_MULTIPLE_DATABASES=packeta,sps
    volumes:
      - ./db-entrypoint.sh:/docker-entrypoint-initdb.d/multiple-databases.sh

  axepto_sps:
    build: .
    ports:
      - 8080:8080/tcp
    environment:
      - DJANGO_SETTINGS_MODULE=axepto.settings.development      
      - DB_TABLE=sps
      - DOCUMENT_SIGN_CERTIFICATE_TELEKOM=aGVsbG8K
    depends_on:
      - postgres

  axepto_packeta:
    build: .
    ports:
      - 8081:8080/tcp
    environment:      
      - DJANGO_SETTINGS_MODULE=axepto.settings.development
      - DB_TABLE=packeta
    depends_on:
      - postgres
