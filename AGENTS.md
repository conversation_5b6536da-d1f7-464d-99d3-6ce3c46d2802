# Repository Guidelines

## Project Structure & Modules
- `axepto/`: Django project code (apps, models, views, cron, assets).
  - `settings/`: `development.py`, `prod.py`, `ci.py`, `test.py` based on `base.py`.
  - `rest/`: REST/domain models, admin, views, urls, tests.
  - `web/`: web app templates and views.
  - `tests/` and `rest/tests/`: Django test modules.
- Root: `manage.py`, `dev-manage.py`, `docker-compose.yml`, `Dockerfile`, `doc/`, `.pre-commit-config.yaml`.

## Build, Run, and Test
- Install dev deps: `python3 -m venv venv && source venv/bin/activate && pip install -r dev-requirements.txt`
- Local DB (option A): `docker compose up postgres`
- App (Docker): `docker compose up axepto_sps` (env from compose; serves on `:8080`).
- App (local): set `.env` with `DJANGO_SETTINGS_MODULE=axepto.settings.development`, then:
  - Migrate: `python dev-manage.py migrate`
  - Run: `python dev-manage.py runserver`
  - uWSGI (dev): `uwsgi dev.ini`
- Tests: `python manage.py test` (or target: `python manage.py test axepto.tests`)
  - CI-like: `DJANGO_SETTINGS_MODULE=axepto.settings.ci python manage.py test`

## Coding Style & Naming
- Formatting: Black (line length 88), isort (profile=black). Run: `pre-commit run -a`.
- Linting/Type checking: `pycodestyle` (ignores E203,W503), `mypy` (`--follow-imports=normal`).
- Conventions: 4-space indent; `snake_case` for functions/vars, `PascalCase` for classes; module names are lowercase; tests named `test_*.py`.

## Testing Guidelines
- Framework: Django `TestCase` (see `axepto/tests/test_cron.py`).
- Scope tests near code (e.g., `axepto/rest/tests/`).
- Seed required fixtures via migrations or test `setUp`.
- Run a single test: `python manage.py test axepto.tests.test_cron.ApiTest`.

## Commits & Pull Requests
- Commits: clear, imperative subject (e.g., "Fix cron summary counts"). Group related changes.
- Branches: use `feature/...`, `fix/...`, or `chore/...` naming.
- PRs: include purpose, summary of changes, screenshots for UI, and links to issues; note migrations and env vars.
- Ensure: tests pass, `pre-commit` clean, and migrations included.

## Security & Config Tips
- Never commit secrets. Use `.env` (see `example.env`).
- Set `DJANGO_SETTINGS_MODULE` per environment; verify DB vars before running migrations.
- For AWS and certificates, follow docs under `doc/` (e.g., `telekom_client_certs.md`).
