import json
import string
import subprocess
import sys
import time
import uuid
from datetime import timed<PERSON><PERSON>
from random import choice, randint

import requests

"""
{
  "wizard_start":"1499773700810",
  "wizard_close":"1499773713701"
}
"""

FILES_PATH = "files/"

# Note: this file was created by <PERSON><PERSON>. For now it is used to quickly create
# new Documents. It should be used to quickly test api and run common tasks
# agains remote server.
# TODO: review and possible refactor


def create_tracking_json():
    now = int(time.time() * 1000)
    return {
        "wizard_start": int(
            now - (timedelta(days=6, minutes=5).total_seconds() * 1000)
        ),
        "wizard_close": int(now + (timedelta(days=6).total_seconds() * 1000)),
    }


class ApiUtils(object):
    def __init__(self, **kwargs):
        self.host = kwargs["host"]
        self.username = kwargs["username"]
        self.password = kwargs["password"]
        self.customer = kwargs["customer"]
        self.session = requests.Session()

    def upload_wizard(self):
        t = self.session.post(
            "%s/document/unsigned/" % self.host,
            files=[
                (
                    "file",
                    (
                        "document.zip",
                        open(FILES_PATH + "package.zip", "rb"),
                        "application/zip",
                    ),
                )
            ],
        )

        print(repr(t))
        print(t.text)
        if t.status_code == 200:
            print("upload wizard: done")
        return json.loads(t.text)["id"]

    def login(self):
        t = self.session.get(
            "%s/login/" % self.host, auth=(self.username, self.password)
        )
        print(t.cookies)

    def get_list_unsigned(self):
        t = self.session.get("%s/document/unsigned/since/0/" % self.host)
        if t.status_code == 200:
            print("get list unsigned: done")

        return json.loads(t.text)

    def assign(self, cour, pack):
        req = {"id": str(cour), "documents": [str(pack)]}
        t = self.session.post("%s/assign/" % self.host, data=json.dumps(req))
        if t.status_code == 200:
            print("assign: done")
        else:
            print("assign: failed")

    def unassign(self, cour):
        req = {"id": str(cour), "documents": []}
        t = self.session.post("%s/assign/" % self.host, data=json.dumps(req))
        if t.status_code == 200:
            print("un-assign: done")
        else:
            print("un-assign: failed")

    def assign2(self, cour, pack):
        req = {
            "id": str(cour),
            "documents": [{"doc": str(pack), "cislo_zasielky": uuid.uuid4().hex[:5]}],
        }

        t = self.session.post("%s/assign2/" % self.host, data=json.dumps(req))
        if t.status_code == 200:
            print("assign2: done")
        else:
            print("assign2: failed")

    def check_driver_list(self, pack):
        t = self.session.get("%s/active/" % self.host)
        docs = json.loads(t.text)["documents"]
        for item in docs:
            if item["id"] == pack:
                print("check driver list: done")
                return

        print("check driver list: failed")

    def download_unsigned(self, id):
        t = self.session.get("%s/document/unsigned/%s/" % (self.host, id), stream=True)
        print(t.status_code)
        if t.status_code == 200:
            print("download unsigned: done")
        handle = open("dwn.zip", "wb")
        for chunk in t.iter_content(chunk_size=512):
            if chunk:  # filter out keep-alive new chunks
                handle.write(chunk)

    def delete_unsigned(self, id):
        t = self.session.delete("%s/document/unsigned/%s/" % (self.host, id))
        if t.status_code == 200:
            print("delete unsigned: done")

    def upload_signed(self, id):
        with open("signed_upload/tracking.json", "w") as outfile:
            json.dump(create_tracking_json(), outfile)
        """subprocess.call(
            [
                "cd signed_upload; zip package.zip metadata.json tracking.json "
                + "document.zip"
            ],
            shell=True,
        )"""
        subprocess.call(
            [
                "cd signed_upload; rm package.zip; zip package.zip "
                + "metadata.json tracking.json"
            ],
            shell=True,
        )
        t = self.session.post(
            "%s/document/signed/%s/" % (self.host, id),
            files=[
                (
                    "file",
                    (
                        "document.zip",
                        open("signed_upload/package.zip", "rb"),
                        "application/zip",
                    ),
                )
            ],
        )

        if t.status_code == 200:
            print("upload signed: done")
        return json.loads(t.text)["id"]

    def get_list_signed(self):
        t = self.session.get("%s/document/signed/since/0/" % self.host)
        if t.status_code == 200:
            print("get list unsigned: done")

        return json.loads(t.text)

    def download_signed(self, id):
        t = self.session.get("%s/document/signed/%s/" % (self.host, id), stream=True)
        print(t.status_code)
        if t.status_code == 200:
            print("download signed: done")
        handle = open("dwn.zip", "wb")
        for chunk in t.iter_content(chunk_size=512):
            if chunk:  # filter out keep-alive new chunks
                handle.write(chunk)

    def delete_signed(self, id):
        t = self.session.delete("%s/document/signed/%s/" % (self.host, id))
        if t.status_code == 200:
            print("delete signed: done")

    def prepare_o2_package(self):
        data = None
        with open(FILES_PATH + "o2/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = "".join(
                choice(string.ascii_uppercase + string.digits) for _ in range(12)
            )

        with open(FILES_PATH + "o2/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))

        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip o2/metadata.json "
                + "o2/document.zip"
            ],
            shell=True,
        )

    def prepare_mzone_package(self):
        data = None
        with open(FILES_PATH + "mzone/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = "Ro_" + str(uuid.uuid1())

        with open(FILES_PATH + "mzone/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))

        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip mzone/metadata.json "
                + "mzone/document.zip"
            ],
            shell=True,
        )

    def prepare_telekom_package(self):
        data = None
        with open(FILES_PATH + "telekom/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = f"{randint(10**8, 10**10)}$1"

        with open(FILES_PATH + "telekom/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))
        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip telekom/metadata.json "
                + "telekom/document.zip"
            ],
            shell=True,
        )

    def prepare_digi_package(self):
        data = None
        with open(FILES_PATH + "digi/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = f"{randint(10**8, 10**10)}$1"

        with open(FILES_PATH + "digi/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))
        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip digi/metadata.json "
                + "digi/document.zip"
            ],
            shell=True,
        )

    def prepare_orange_package(self):
        data = None
        with open(FILES_PATH + "orange/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = str(randint(10**6, 10**10))

        with open(FILES_PATH + "orange/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))

        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip orange/metadata.json "
                + "orange/document.zip"
            ],
            shell=True,
        )

    def prepare_union_package(self):
        data = None
        with open(FILES_PATH + "orange/metadata.json", "r") as readfile:
            file_content = readfile.read()
            data = json.loads(file_content)
            data["client_id"] = str(uuid.uuid1()).replace("-", "")

        with open(FILES_PATH + "union/metadata.json", "w") as outfile:
            outfile.write(json.dumps(data))

        subprocess.call(
            [
                "cd "
                + FILES_PATH
                + "; rm package.zip || true; zip -j package.zip union/metadata.json "
                + "union/document.zip"
            ],
            shell=True,
        )

    def prepare_packages(self):
        if self.customer == "o2":
            self.prepare_o2_package()
        elif self.customer == "telekom":
            self.prepare_telekom_package()
        elif self.customer == "orange":
            self.prepare_orange_package()
        elif self.customer == "mzone":
            self.prepare_mzone_package()
        elif self.customer == "union":
            self.prepare_union_package()
        elif self.customer == "digi":
            self.prepare_digi_package()
        else:
            raise Exception('Unknow "customer"')


# TODO move to separate file

if len(sys.argv) < 5:
    print(
        (
            "Incorrect number of arguments. Usage: python3 %s [username] [password] "
            + "[host] [customer]"
        )
        % sys.argv[0]
    )
    sys.exit()

print(sys.argv)

t1 = ApiUtils(
    username=sys.argv[1],
    password=sys.argv[2],
    host=sys.argv[3],
    customer=sys.argv[4],
)

t1.prepare_packages()
t1.login()
id = t1.upload_wizard()
