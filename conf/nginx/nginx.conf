worker_processes auto;
pcre_jit on;

error_log  /dev/stderr;
pid        /tmp/nginx/nginx.pid;

include /etc/nginx/modules/*.conf;
include /etc/nginx/conf.d/*.conf;

events {
        worker_connections 1024;
}

http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        server_tokens off;
        client_max_body_size 16m;
        sendfile on;
        tcp_nopush on;
        ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:2m;
        ssl_session_timeout 1h;
        ssl_session_tickets off;
        gzip on;
        gzip_vary on;

        # Specifies the main log format.
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        # Sets the path, format, and configuration for a buffered log write.
        access_log /dev/stdout main;

        # Includes virtual hosts configs.
        include /etc/nginx/http.d/*.conf;

        # Allow to have read-only filesystem
        client_body_temp_path /tmp/nginx/client_temp;
        proxy_temp_path       /tmp/nginx/proxy_temp_path;
        fastcgi_temp_path     /tmp/nginx/fastcgi_temp;
        uwsgi_temp_path       /tmp/nginx/uwsgi_temp;
        scgi_temp_path        /tmp/nginx/scgi_temp;
}
