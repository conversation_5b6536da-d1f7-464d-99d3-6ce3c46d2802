#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile dev-requirements.in
#
asgiref==3.4.1
    # via django
attrs==21.2.0
    # via jsonschema
backcall==0.2.0
    # via ipython
black==22.3.0
    # via -r dev-requirements.in
boto3==1.18.55
    # via django-ses
botocore==1.21.55
    # via
    #   boto3
    #   s3transfer
build==1.2.1
    # via pip-tools
certifi==2021.5.30
    # via
    #   -r requirements.in
    #   requests
    #   sentry-sdk
cffi==1.15.0
    # via cryptography
cfgv==3.4.0
    # via pre-commit
charset-normalizer==2.0.6
    # via requests
click==8.0.1
    # via
    #   black
    #   pip-tools
colorama==0.4.4
    # via crayons
configparser==5.0.2
    # via webdriver-manager
convertdate==2.3.2
    # via workalendar
crayons==0.4.0
    # via webdriver-manager
cryptography==36.0.1
    # via pyopenssl
decorator==5.1.0
    # via
    #   -r requirements.in
    #   ipython
distlib==0.3.8
    # via virtualenv
django==3.2.8
    # via
    #   -r requirements.in
    #   django-cors-headers
    #   django-cron
    #   django-debug-toolbar
    #   django-extensions
    #   django-guid
    #   django-picklefield
    #   django-qr-code
    #   django-ses
    #   django-setty
    #   django-storages
    #   jsonfield
django-cors-headers==3.10.0
    # via -r requirements.in
django-crispy-forms==1.13.0
    # via -r requirements.in
django-cron==0.6.0
    # via -r requirements.in
django-debug-toolbar==3.2.2
    # via -r requirements.in
django-dotenv==1.4.2
    # via -r requirements.in
django-extensions==3.1.3
    # via -r requirements.in
django-guid==3.2.0
    # via -r requirements.in
django-picklefield==3.0.1
    # via
    #   -r requirements.in
    #   django-setty
django-qr-code==2.2.0
    # via -r requirements.in
django-ses==4.1.0
    # via -r requirements.in
django-setty==3.2.1
    # via -r requirements.in
django-storages==1.11.1
    # via -r requirements.in
filelock==3.15.4
    # via virtualenv
humanfriendly==10.0
    # via -r requirements.in
identify==2.5.36
    # via pre-commit
idna==3.2
    # via requests
ipython==7.28.0
    # via -r requirements.in
isort==5.10.1
    # via -r dev-requirements.in
jedi==0.18.0
    # via ipython
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
jsonfield==3.1.0
    # via -r requirements.in
jsonschema==4.0.1
    # via -r requirements.in
lunardate==0.2.0
    # via workalendar
lxml==4.6.3
    # via -r dev-requirements.in
matplotlib-inline==0.1.3
    # via ipython
mypy==1.0.1
    # via -r dev-requirements.in
mypy-extensions==0.4.3
    # via
    #   black
    #   mypy
nodeenv==1.9.1
    # via pre-commit
packaging==24.0
    # via build
parso==0.8.2
    # via jedi
pathspec==0.9.0
    # via black
pexpect==4.8.0
    # via ipython
pickleshare==0.7.5
    # via ipython
pip-tools==7.4.1
    # via -r requirements.in
platformdirs==2.4.0
    # via
    #   black
    #   virtualenv
pre-commit==2.15.0
    # via -r dev-requirements.in
prompt-toolkit==3.0.20
    # via ipython
psycopg2-binary==2.9.1
    # via -r requirements.in
ptyprocess==0.7.0
    # via pexpect
pycodestyle==2.7.0
    # via -r dev-requirements.in
pycparser==2.21
    # via cffi
pygments==2.10.0
    # via ipython
pyluach==1.3.0
    # via workalendar
pymeeus==0.5.11
    # via convertdate
pyopenssl==22.0.0
    # via -r requirements.in
pyproject-hooks==1.1.0
    # via
    #   build
    #   pip-tools
pyrsistent==0.18.0
    # via jsonschema
python-dateutil==2.8.2
    # via
    #   botocore
    #   workalendar
python-memcached==1.59
    # via django-setty
pytz==2021.3
    # via
    #   -r requirements.in
    #   convertdate
    #   django
    #   django-ses
pyyaml==6.0.1
    # via pre-commit
requests==2.26.0
    # via
    #   -r requirements.in
    #   webdriver-manager
s3transfer==0.5.0
    # via boto3
segno==1.3.3
    # via django-qr-code
selenium==3.141.0
    # via -r dev-requirements.in
sentry-sdk==1.9.0
    # via -r requirements.in
six==1.16.0
    # via
    #   python-dateutil
    #   python-memcached
sqlparse==0.4.2
    # via
    #   django
    #   django-debug-toolbar
statsd-exporter==3.2.1
    # via -r requirements.in
toml==0.10.2
    # via pre-commit
tomli==1.2.1
    # via
    #   black
    #   build
    #   mypy
    #   pip-tools
traitlets==5.1.0
    # via
    #   ipython
    #   matplotlib-inline
types-boto==2.49.14
    # via -r dev-requirements.in
types-cryptography==3.3.14
    # via types-pyopenssl
types-enum34==1.1.8
    # via types-cryptography
types-ipaddress==1.0.8
    # via types-cryptography
types-pyopenssl==22.0.3
    # via -r dev-requirements.in
types-python-dateutil==2.9.0.20240316
    # via -r dev-requirements.in
types-pytz==2021.3.6
    # via -r dev-requirements.in
types-requests==2.27.20
    # via -r dev-requirements.in
types-six==1.16.1
    # via types-boto
types-urllib3==1.26.13
    # via types-requests
typing-extensions==********
    # via mypy
urllib3==1.26.7
    # via
    #   botocore
    #   requests
    #   selenium
    #   sentry-sdk
uwsgi==2.0.20
    # via -r requirements.in
virtualenv==20.21.1
    # via pre-commit
wcwidth==0.2.5
    # via prompt-toolkit
webdriver-manager==3.4.2
    # via -r dev-requirements.in
wheel==0.37.0
    # via pip-tools
workalendar==16.1.0
    # via -r requirements.in

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
