[uwsgi]

# Django-related settings
# the base directory (full path)
#chdir           = /home/<USER>/Documents/test2
# Djan<PERSON>'s wsgi file
module          = axepto.wsgi

# process-related settings
# master
master          = true
# maximum number of worker processes
processes       = 5
# the socket (use the full path to be safe
http            = 127.0.0.1:8000
# ... with appropriate permissions - may be needed
chmod-socket    = 666
# clear environment on exit
vacuum          = true

die-on-term     = true

py-autoreload 	= 2

#static files for web
static-map	= /static/=axepto/web/static

#becouse of admin static files
static-map      = /static/admin=axepto/static/admin

logto		= log/uwsgi.log

#log-backupname  = log/uwsgi.log.old

#50 megs, then rotate
#log-maxsize = 50000000

