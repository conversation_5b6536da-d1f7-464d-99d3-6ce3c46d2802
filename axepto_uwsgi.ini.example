[uwsgi]

# Django-related settings
# the base directory (full path)
chdir           = /home/<USER>/test2
# Django's wsgi file
module          = axepto.wsgi

# process-related settings
# master
master          = true
# maximum number of worker processes
processes       = 5
# the socket (use the full path to be safe
socket          = /home/<USER>/test2/test2.sock
# ... with appropriate permissions - may be needed
chmod-socket    = 666
# clear environment on exit
vacuum          = true

die-on-term     = true

logto           = log/uwsgi.log

log-backupname  = log/uwsgi.log.old

#50 megs, then rotate
log-maxsize     = 50000000

#reload if rss memory is higher than specified megabytes
reload-on-rss   = 256

