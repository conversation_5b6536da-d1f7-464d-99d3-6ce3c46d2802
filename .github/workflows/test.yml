name: Build and Test

on: [push]

jobs:
  build_and_test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16.2-alpine3.19
        env:
          POSTGRES_USER: axepto
          POSTGRES_PASSWORD: axeptopassword
          POSTGRES_DB: axepto
        ports:
          - 5432:5432
        options: --name postgres

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10' 

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install "cython<3.0.0" wheel
          pip install "pyyaml==5.4.1" --no-build-isolation
          pip install -r ci-requirements.txt

      - name: Run formatters
        run: |
          isort --check --filter-files .
          black --check .

      - name: Run checkers
        run: |
          pycodestyle --ignore=E203,W503 .
          mypy .

      - name: Configure locale
        env:
          LC_ALL: sk_SK.UTF-8
          LC_CTYPE: sk_SK.UTF-8
        run: |
          sudo apt-get install locales
          sudo locale-gen sk_SK.UTF-8
          sudo dpkg-reconfigure locales

      - name: Run tests
        env:
          DJANGO_SETTINGS_MODULE: axepto.settings.ci
          DB_TABLE: axepto
          DB_USER: axepto
          DB_PASSWORD: axeptopassword
          DB_HOST: localhost
          DOCUMENT_SIGN_CERTIFICATE_MZONE: IA==
          DOCUMENT_SIGN_CERTIFICATE_O2: IA==
          DOCUMENT_SIGN_CERTIFICATE_ORANGE: IA==
          DOCUMENT_SIGN_CERTIFICATE_TELEKOM: IA==
          DOCUMENT_SIGN_CERTIFICATE_UNION: IA==
        run: |
          ./manage.py migrate
          ./manage.py test
