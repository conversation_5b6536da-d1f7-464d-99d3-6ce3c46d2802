name: build-docker
on:
  workflow_call:
    inputs:
      microservice_name:
        required: true
        type: string

jobs:
  BuildPush:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.aws_region }}
          role-to-assume: ${{ vars.aws_role_arn }}
          role-session-name: GithubActionRunID-${{ github.run_id }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker
        id: build-image
        uses: docker/build-push-action@v5
        with:
          context: .
          load: true
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ vars.docker_repo_address_base }}/axepto-${{ inputs.microservice_name }}:${{ github.sha }}

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.17.0
        with:
          image-ref: "${{ vars.docker_repo_address_base }}/axepto-${{ inputs.microservice_name }}:${{ github.sha }}"
          format: "table"
          exit-code: "0"
          ignore-unfixed: true
          vuln-type: "os,library"
          severity: "CRITICAL,HIGH"

      - name: Push Docker Image
        id: push-image
        uses: docker/build-push-action@v5
        with:
          context: ${{ inputs.context }}
          push: true
          cache-from: type=gha
          tags: ${{ vars.docker_repo_address_base }}/axepto-${{ inputs.microservice_name }}:${{ github.sha }}
          build-args: ${{ inputs.build_args }}
