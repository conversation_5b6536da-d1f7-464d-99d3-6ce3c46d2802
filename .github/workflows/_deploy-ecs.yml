name: deploy-ecs

on:
  workflow_call:
    inputs:
      microservice_name:
        required: true
        type: string
      environment:
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-${{ inputs.microservice_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials for Shared Account to check if image exists
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.aws_region }}
          role-to-assume: ${{ vars.aws_role_arn }}
          role-session-name: GithubActionRunID-${{ github.run_id }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Check if image exists
        run: |
          docker pull  ${{ vars.docker_repo_address_base }}/axepto-${{ inputs.microservice_name }}:${{ github.sha }}

      - name: Configure AWS Credentials for Environment to Deploy the App
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.aws_region }}
          role-to-assume: ${{ vars[format('aws_role_arn_{0}', inputs.environment)] }}
          role-session-name: GithubActionRunID-${{ github.run_id }}

      - name: Download task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ vars[format('ecs_task_definition_{0}_{1}', inputs.environment, inputs.microservice_name)] }} --query taskDefinition |
          jq 'del(
                  .taskDefinitionArn,
                  .requiresAttributes,
                  .compatibilities,
                  .revision,
                  .status,
                  .registeredAt,
                  .registeredBy
              )' > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: ${{ inputs.microservice_name }}
          image: ${{ vars.docker_repo_address_base }}/axepto-${{ inputs.microservice_name }}:${{ github.sha }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ vars[format('ecs_service_name_{0}_{1}', inputs.environment, inputs.microservice_name)] }}
          cluster: ${{ vars[format('ecs_cluster_name_{0}_applications', inputs.environment)] }}
          wait-for-service-stability: true
          wait-for-minutes: 10
