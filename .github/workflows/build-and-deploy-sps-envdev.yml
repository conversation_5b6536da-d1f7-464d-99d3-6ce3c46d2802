name: 'SPS: Build and deploy to DEV'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - .github/workflows/_build-docker.yml
      - .github/workflows/_deploy-ecs.yml
      - .github/workflows/build-and-deploy-sps-envdev.yml
      - .github/workflows/build-and-deploy-sps-envprod.yml
      - axepto/**
      - conf/**
      - Dockerfile
      - entrypoint.sh

jobs:
  BuildPush:
    name: Docker Build & Push
    uses: ./.github/workflows/_build-docker.yml
    with:
      microservice_name: sps
    secrets: inherit

  Deploy:
    name: ECS Deployment
    uses: ./.github/workflows/_deploy-ecs.yml
    needs: BuildPush
    with:
      microservice_name: sps
      environment: envdev
    secrets: inherit
