name: 'Packeta: Build and deploy to DEV'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - .github/workflows/_build-docker.yml
      - .github/workflows/_deploy-ecs.yml
      - .github/workflows/build-and-deploy-packeta-envdev.yml
      - .github/workflows/build-and-deploy-packeta-envprod.yml
      - axepto/**
      - conf/**
      - Dockerfile
      - entrypoint.sh

jobs:
  BuildPush:
    name: Docker Build & Push
    uses: ./.github/workflows/_build-docker.yml
    with:
      microservice_name: packeta
    secrets: inherit

  Deploy:
    name: ECS Deployment
    uses: ./.github/workflows/_deploy-ecs.yml
    needs: BuildPush
    with:
      microservice_name: packeta
      environment: envdev
    secrets: inherit
