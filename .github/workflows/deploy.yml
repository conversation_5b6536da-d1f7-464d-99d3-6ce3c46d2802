name: CD Pipeline
on:
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Deploy to environment'
        type: choice
        required: true
        options:
        - SPS
        - SPS-cron
        - Packeta
        - Packeta-cron

jobs:
  continuous-deployment:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      - name: Create CodeDeploy Deployment
        id: deploy
        run: |
          aws deploy create-deployment \
            --application-name axepto \
            --deployment-group-name ${{ inputs.deploy_environment }} \
            --deployment-config-name CodeDeployDefault.OneAtATime \
            --github-location repository=${{ github.repository }},commitId=${{ github.sha }}