FROM python:3.10-alpine3.19

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

ARG NGINX_VERSION=1.24.0-r16

# # Install required packages to update CA certificates
# RUN apk add --no-cache ca-certificates && \
#     update-ca-certificates

# # Copy your custom CA certificates into the container
# COPY ./Entrust_OV_TLS_Issuing_RSA_CA_2.pem /usr/local/share/ca-certificates/Entrust_OV_TLS_Issuing_RSA_CA_2.crt
# COPY ./Sectigo_Public_Server_Authentication_Root_R46.pem /usr/local/share/ca-certificates/Sectigo_Public_Server_Authentication_Root_R46.crt
# # Update CA certificates again to include the new ones in the containers ca-certificates
# RUN update-ca-certificates
COPY ./chain.pem /tmp/certificates/chain.pem

RUN ls /etc/ssl/certs | grep Sectigo
RUN ls /etc/ssl/certs | grep Entrust

WORKDIR /app

# Install Python dependencies
COPY requirements.txt ./requirements.txt

# Install dependencies
RUN apk update \
    && apk add --virtual build-deps \
    gcc \
    libc-dev \
    linux-headers \
    libffi-dev \
    && apk add nginx=${NGINX_VERSION} \
    # Install Python dependencies
    && pip install -r requirements.txt \
    && rm -rf requirements.txt \
    && apk del build-deps

COPY ./axepto ./axepto
COPY ./entrypoint.sh ./entrypoint.sh
COPY ./manage.py ./manage.py

# Remove default nginx config
RUN rm /etc/nginx/nginx.conf \
    && rm /etc/nginx/http.d/default.conf

# Copy the configuration files
COPY ./conf/uwsgi.ini /tmp/uwsgi.ini
COPY ./conf/nginx/nginx.conf /etc/nginx/nginx.conf
COPY ./conf/nginx/app.conf /etc/nginx/http.d

# Create the directory for the temporary files
RUN mkdir -p /tmp/nginx /tmp/certificates

EXPOSE 8080

ENTRYPOINT ["./entrypoint.sh"]
