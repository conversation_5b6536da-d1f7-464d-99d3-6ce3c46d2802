<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Axepto - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .main-content {
            padding: 20px;
        }
        .search-bar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .document-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-signed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-unsigned {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn-filter {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .pagination-wrapper {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-signature me-2"></i>AXEPTO
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#"><i class="fas fa-user me-1"></i>Admin User</a>
                <a class="nav-link" href="#"><i class="fas fa-sign-out-alt"></i></a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content">
        <div class="search-bar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" placeholder="Search documents...">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-primary"><i class="fas fa-download me-1"></i>Export</button>
                    <button class="btn btn-primary"><i class="fas fa-calculator me-1"></i>Calculate</button>
                </div>
            </div>
        </div>

        <div class="filter-section">
            <h5 class="mb-3">Parcel List</h5>
            <div class="row mb-3">
                <div class="col-12">
                    <h6>Predefined filters:</h6>
                    <button class="btn btn-outline-secondary btn-filter">Last Work Day</button>
                    <button class="btn btn-outline-secondary btn-filter">This Week</button>
                    <button class="btn btn-outline-secondary btn-filter">Last Week</button>
                    <button class="btn btn-outline-secondary btn-filter">This Month</button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <label class="form-label">Begin Date</label>
                    <input type="date" class="form-control">
                </div>
                <div class="col-md-2">
                    <label class="form-label">End Date</label>
                    <input type="date" class="form-control">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select class="form-select">
                        <option>All</option>
                        <option>Signed</option>
                        <option>Unsigned</option>
                        <option>Error</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Courier</label>
                    <select class="form-select">
                        <option>All Couriers</option>
                        <option>John Doe</option>
                        <option>Jane Smith</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Customer</label>
                    <select class="form-select">
                        <option>All Customers</option>
                        <option>Telekom</option>
                        <option>Orange</option>
                        <option>O2</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-primary w-100">Show</button>
                </div>
            </div>
        </div>

        <div class="document-table">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Client ID</th>
                        <th>Status</th>
                        <th>Courier</th>
                        <th>Date</th>
                        <th>Customer</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>DOC001</strong></td>
                        <td>CLI123</td>
                        <td><span class="status-badge status-signed">Signed</span></td>
                        <td>John Doe</td>
                        <td>2024-01-04</td>
                        <td>Telekom</td>
                        <td><a href="#" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>
                    <tr>
                        <td><strong>DOC002</strong></td>
                        <td>CLI124</td>
                        <td><span class="status-badge status-unsigned">Unsigned</span></td>
                        <td>Jane Smith</td>
                        <td>2024-01-04</td>
                        <td>Orange</td>
                        <td><a href="#" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>
                    <tr>
                        <td><strong>DOC003</strong></td>
                        <td>CLI125</td>
                        <td><span class="status-badge status-error">Error</span></td>
                        <td>Bob Miller</td>
                        <td>2024-01-04</td>
                        <td>O2</td>
                        <td><a href="#" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>
                    <tr>
                        <td><strong>DOC004</strong></td>
                        <td>CLI126</td>
                        <td><span class="status-badge status-signed">Signed</span></td>
                        <td>Alice Johnson</td>
                        <td>2024-01-03</td>
                        <td>Union</td>
                        <td><a href="#" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>
                    <tr>
                        <td><strong>DOC005</strong></td>
                        <td>CLI127</td>
                        <td><span class="status-badge status-unsigned">Unsigned</span></td>
                        <td>Mike Wilson</td>
                        <td>2024-01-03</td>
                        <td>Telekom</td>
                        <td><a href="#" class="btn btn-sm btn-outline-primary">View</a></td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination-wrapper">
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
                <div class="text-center mt-2">
                    <small class="text-muted">Page 1 of 10 (showing 5 of 50 documents)</small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
