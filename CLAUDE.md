# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Django Management
- `python manage.py runserver` - Start development server
- `python manage.py migrate` - Apply database migrations
- `python manage.py makemigrations` - Create new migrations
- `python dev-manage.py` - Development version of manage.py
- `python manage.py runcrons` - Execute cron jobs manually
- `python manage.py shell_plus` - Enhanced Django shell with IPython

### UWSGI Development
- `uwsgi dev.ini` - Required during development for proper operation

### Frontend/Wizard Development
- Navigate to `wizard/` directory for React-based signing wizard
- Check `wizard/package.json` for build commands (uses Webpack + Gulp)
- Wizard assets are built and placed in `axepto/assets/` directories

### Testing
- Tests are located in individual app directories (`tests.py` files)
- Use Django's standard `python manage.py test` command
- Testing utilities available in `testing_utils/` directory

## Architecture Overview

### Core Django Apps
- **`axepto.rest`** - Main REST API handling document operations, couriers, customers
- **`axepto.web`** - Web dashboard interface for operators/managers  
- **`axepto.audit`** - Audit logging and export functionality
- **`axepto.archive`** - Document archiving system

### Key Models (axepto.rest.models)
- **Document** - Core document model with signing workflow states
- **Customer** - Customer/client management
- **Courier** - Courier management and assignments
- **History** - Action tracking (assign, delete, download, login)
- **Notification** - System notifications
- **Quarantine** - Quarantined/problematic documents
- **Survey** - Customer satisfaction surveys

### Settings Structure
- Base settings in `axepto/settings/base.py`
- Environment-specific: `development.py`, `prod.py`, `test.py`, `ci.py`
- Uses PostgreSQL database and S3 storage
- Supports multiple delivery companies via MODE enum

### URL Structure
- `/` - REST API endpoints (axepto.rest)
- `/web/` - Dashboard interface (axepto.web)  
- `/admin/` - Django admin
- `/archive/` - Archive operations
- `/test/` - Test views (if ALLOW_TEST_VIEWS enabled)

### Document Workflow
Documents flow through states: upload → assign to courier → signing → completion
- Supports multiple operators (Telekom, Orange, O2, Union, etc.)
- Each operator has custom schemas in `axepto/schemas/`
- Signing wizard generates operator-specific bundles

### Key Features
- Multi-tenant courier company support
- Document signing via tablet wizard interface
- AWS S3 storage integration
- SES email notifications  
- Audit logging and exports
- Quarantine system for problematic documents
- Survey system for customer feedback
- Cron jobs for cleanup and notifications

### External Integrations
- AWS S3 for document storage
- AWS SES for email notifications
- Sentry for error tracking
- Multiple courier company notification systems
- Certificate management and validation