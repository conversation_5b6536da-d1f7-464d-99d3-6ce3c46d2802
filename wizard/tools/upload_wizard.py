import json
import sys
from random import randint

import requests

possibleNames = [
    {
        "name": "<PERSON>",
        "surname": "<PERSON>",
        "email": "liv<PERSON><PERSON>@axepto.com",
        "type_": "DSL Internet",
        "scans": {
            "mandatory": [],
            "optional": [
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [],
        "documents": [{"type": "internet", "required": True}],
    },
    {
        "name": "<PERSON>",
        "surname": "<PERSON>",
        "email": "<EMAIL>",
        "type_": "migrace",
        "scans": {
            "mandatory": [
                {"text": "Parabola", "description": "", "folder": "parabola"}
            ],
            "optional": [
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [
            {
                "id": "zsh_smart",
                "text": "Smart karta",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
                "hidden": True,
            },
            {
                "id": "zsh_stb",
                "text": "STB",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_ovladac",
                "text": "Dálkový ovladač",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_lnbsin",
                "text": "LNB konvertor - single",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_koax",
                "text": "Koaxiální kabel",
                "unit": "Ks",
                "unit_price_mon": 7,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_sckab",
                "text": "SCART kabel",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_fkon",
                "text": "F-konektor - šroubovací/kompresní",
                "unit": "Ks",
                "unit_price_mon": 2,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
        ],
        "documents": [
            {"type": "amendment", "required": True},
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Albert",
        "surname": "Taylor",
        "email": "<EMAIL>",
        "type_": "Nová instalace",
        "scans": {
            "mandatory": [
                {"text": "Parabola", "description": "", "folder": "parabola"},
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
            ],
            "optional": [{"text": "Plná moc", "description": "", "folder": "plna_moc"}],
        },
        "materials": [
            {
                "id": "zsh_parab",
                "text": "Parabola",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_drzak",
                "text": "Držák na parabolu",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_smart",
                "text": "Smart karta",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
                "hidden": True,
            },
            {
                "id": "zsh_stb",
                "text": "STB",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_ovladac",
                "text": "Dálkový ovladač",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_lnbsin",
                "text": "LNB konvertor - single",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_koax",
                "text": "Koaxiální kabel",
                "unit": "Ks",
                "unit_price_mon": 7,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_sckab",
                "text": "SCART kabel",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_fkon",
                "text": "F-konektor - šroubovací/kompresní",
                "unit": "Ks",
                "unit_price_mon": 2,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
        ],
        "documents": [
            {"type": "contract", "required": True},
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Monica",
        "surname": "White",
        "email": "<EMAIL>",
        "type_": "Servis SD",
        "scans": {
            "mandatory": [],
            "optional": [
                {"text": "Parabola", "description": "", "folder": "parabola"},
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [],
        "documents": [
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Július",
        "surname": "Satinský",
        "email": "<EMAIL>",
        "type_": "Servis ND",
        "scans": {
            "mandatory": [],
            "optional": [
                {"text": "Parabola", "description": "", "folder": "parabola"},
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [],
        "documents": [
            {"type": "amendment", "required": True},
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Milan",
        "surname": "Lasica",
        "email": "<EMAIL>",
        "type_": "Partner box",
        "scans": {
            "mandatory": [],
            "optional": [
                {"text": "Parabola", "description": "", "folder": "parabola"},
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [
            {
                "id": "zsh_smart",
                "text": "Smart karta",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
                "hidden": True,
            },
            {
                "id": "zsh_stb",
                "text": "STB",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_ovladac",
                "text": "Dálkový ovladač",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_lnbdua",
                "text": "LNB konvertor - dual",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_koax",
                "text": "Koaxiální kabel",
                "unit": "Ks",
                "unit_price_mon": 7,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_sckab",
                "text": "SCART kabel",
                "unit": "Ks",
                "unit_price_mon": 1,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
            {
                "id": "zsh_fkon",
                "text": "F-konektor - šroubovací/kompresní",
                "unit": "Ks",
                "unit_price_mon": 2,
                "unit_price_dem": 0,
                "prepaid": 0,
            },
        ],
        "documents": [
            {"type": "amendment", "required": True},
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Rasťo",
        "surname": "Piško",
        "email": "<EMAIL>",
        "type_": "Přeinstalace",
        "scans": {
            "mandatory": [
                {"text": "Parabola", "description": "", "folder": "parabola"}
            ],
            "optional": [
                {
                    "text": "OP ID 1.strana",
                    "description": "Přední strana průkazu",
                    "folder": "op_id_1",
                },
                {
                    "text": "OP ID 2.strana",
                    "description": "Zadní strana průkazu",
                    "folder": "op_id_2",
                },
                {"text": "Plná moc", "description": "", "folder": "plna_moc"},
            ],
        },
        "materials": [],
        "documents": [
            {"type": "amendment", "required": True},
            {"type": "final", "required": True},
        ],
    },
    {
        "name": "Jožo",
        "surname": "Mrkvička",
        "email": "<EMAIL>",
        "type_": "Demontáž",
        "scans": {"mandatory": [], "optional": []},
        "materials": [],
        "documents": [{"type": "final", "required": True}],
    },
]


def create_metadata(name, surname, email, type_, scans, materials, documents):
    return json.dumps(
        {
            "campaigns_info": [
                {"id": "vanoce", "text": "Vánoce"},
                {"id": "velikonoce", "text": "Velikonoce"},
            ],
            "scans_info": scans,
            "materials_info": materials,
            "documents_info": documents,
            "type": type_,
            "error_info": [
                {"type": 0, "text": "Klient není přítomen"},
                {
                    "type": 1,
                    "text": "Technik nedorazil na sjednanou návštěvu (nehoda, zdržení, "
                    + "… )",
                },
            ],
            "byd_ulice": {
                "value": "Malinova",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "byd_mesto": {
                "value": "Malinovo",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "byd_orc": {
                "value": "12",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "byd_psc": {
                "value": "848 51",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "uzi_titul": {
                "value": "Mr.",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_mobc": {
                "value": " 421 999 111 222",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "uzi_email": {
                "value": email,
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_idc": {
                "value": "EA45615",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "dorvyu_radio": {"value": "post", "editable": True},
            "zahl_techkod": {
                "value": "1568",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "tech_jmeno": {
                "value": "Meno Technika",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "zahl_smlouc": {
                "value": "48943-28",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "zahl_datum": {
                "value": "22-9-2015",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "bal_bas_vaz": {"value": True, "editable": True},
            "balicek_radio": {"value": "zakl", "editable": True},
            "uzi_telc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ooj_jmeno": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "kauce_vyse": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": False,
            },
            "ooj_rc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "byd_poc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "byd_stat": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_ulice": {
                "value": "Malinova",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_mesto": {
                "value": "Malinovo",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_poc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_orc": {
                "value": "12",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_psc": {
                "value": "848 51",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_ulice": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_mesto": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_poc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_orc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_psc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "akce_nazev": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "kampan_nazev": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "prodzar_stb": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "prodzar_cam": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "predpla_vyse": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": False,
            },
            "zpuhraz_sipoc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zpuhraz_ucetc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zpuhraz_bankod": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "castk_mat": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "zsh_ovladac_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbsin_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbdua_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbqua_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_parab_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_drzak_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_objim_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_koax_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_hdkab_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_sckab_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_fkon_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_fspoj_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_chkot_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_tmel_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lista_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_ovladac_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbsin_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbdua_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lnbqua_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_parab_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_drzak_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_objim_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_koax_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_hdkab_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_sckab_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_fkon_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_fspoj_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_chkot_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_tmel_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_lista_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_stb_mon": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_stb_dem": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "zsh_castka": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": False,
            },
            "prist_puv_1": {
                "value": "12345678901234567",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "prist_puv_2": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "prist_nov_1": {
                "value": "12345678901234567",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "prist_nov_2": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "karta_puv_1": {
                "value": "123456789012",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "karta_puv_2": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "karta_nov_1": {
                "value": "123456789012",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "karta_nov_2": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "osoba_typ_radio": {"value": "fyz", "editable": True},
            "uzi_krestni_jmeno": {
                "value": name,
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_prijmeni": {
                "value": surname,
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_obch_firm": {
                "value": "Best Firma Ever",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_rc": {
                "value": "111111/1111",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_ico": {
                "value": "86408176",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_dic": {
                "value": "8888888888",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "prnjzar_stb": {"value": 1, "editable": True},
            "prnjzar_cam": {"value": 2, "editable": True},
            "podp_jmeno_uzi": {
                "value": "Test Štandardný",
                "required": True,
                "validated": True,
                "editable": True,
            },
            "predpla_radio": {"value": "ano", "editable": True},
            "bal_hbo": {"value": True, "editable": True},
            "bal_hbo_go": {"value": True, "editable": True},
            "bal_extra_vaz": {"value": True, "editable": True},
            "bal_hbo_vaz": {"value": True, "editable": True},
            "bal_hbo_go_vaz": {"value": True, "editable": True},
            "kauce_radio": {"value": "ano", "editable": True},
            "zpuhraz_radio": {"value": "sipo", "editable": True},
            "bal_extra": {"value": True, "editable": True},
            "zsh_nadstd": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "zsh_pozn": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "zsh_odm_chb": {"value": True, "editable": True},
            "zsh_odm": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "zsh_uzi_chb": {"value": True, "editable": True},
            "typ_televize_radio": {"value": "crt", "editable": True},
            "zsh_doba": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "vyrobce_tv": {"value": "Samsung", "editable": True},
            "zsh_servuzi": {
                "value": "",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "cam_radio": {"value": "ne", "editable": True},
            "ooj_idc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "uzi_jmeno": {
                "value": "X1 Test Standardny",
                "required": False,
                "validated": False,
                "editable": True,
            },
            "bal_televize": {"value": True, "editable": False},
            "zarizeni_typ1": {
                "value": "cam_prodej",
                "editable": True,
                "options": [
                    {"key": "cam_prodej", "value": "CAM prodej"},
                    {"key": "cam_pronajem", "value": "CAM pronájem"},
                    {"key": "stb_prodej", "value": "STB prodej"},
                    {"key": "stb_pronajem", "value": "STB pronájem"},
                ],
            },
            "zarizeni_typ2": {
                "value": "cam_pronajem",
                "editable": True,
                "options": [
                    {"key": "cam_prodej", "value": "CAM prodej"},
                    {"key": "cam_pronajem", "value": "CAM pronájem"},
                    {"key": "stb_prodej", "value": "STB prodej"},
                    {"key": "stb_pronajem", "value": "STB pronájem"},
                ],
            },
            "zahl_radio": {"value": "smlouc", "editable": False},
            "uzi_dslemail": {
                "value": "<EMAIL>",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_dslulice": {
                "value": "Malinova",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_dslmesto": {
                "value": "Malinovo",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_dslpoc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_dslorc": {
                "value": "12",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "ins_dslpsc": {
                "value": "848 51",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_dslulice": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_dslmesto": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_dslpoc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_dslorc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "dor_dslpsc": {
                "value": "",
                "required": False,
                "validated": True,
                "editable": True,
            },
            "podp_dslvop_chb": {"value": False, "required": True},
            "podp_vop_chb": {"value": False, "required": True},
        }
    )


class WizardCreator(object):
    def __init__(self, **kwargs):
        self.host = kwargs["host"]
        self.username = kwargs["username"]
        self.password = kwargs["password"]
        self.session = requests.Session()
        self.login()

    def get_random_metadata(self):
        user_details = possibleNames[randint(0, len(possibleNames) - 1)]
        return create_metadata(
            user_details["name"],
            user_details["surname"],
            user_details["email"],
            user_details["type_"],
            user_details["scans"],
            user_details["materials"],
            user_details["documents"],
        )

    def get_metadata(self, i):
        user_details = possibleNames[i]
        return create_metadata(
            user_details["name"],
            user_details["surname"],
            user_details["email"],
            user_details["type_"],
            user_details["scans"],
            user_details["materials"],
            user_details["documents"],
        )

    def upload_wizard(self, data):
        self.session.post("%s/document/unsigned/" % self.host, data=data)

    def login(self):
        return self.session.post(
            "%s/login/" % self.host,
            data={"username": self.username, "password": self.password},
        )

    def push_wizards(self, amount):
        for i in range(0, amount):
            self.upload_wizard(self.get_metadata(i))


if len(sys.argv) < 4:
    print(
        (
            "Incorrect number of arguments. Usage: python3 %s [host] [username] "
            + "[password] [amount: optional]"
        )
        % sys.argv[0]
    )
    sys.exit()

print(sys.argv)

wcreator = WizardCreator(host=sys.argv[1], username=sys.argv[2], password=sys.argv[3])

amount = int(sys.argv[4]) if len(sys.argv) >= 5 else 10

wcreator.push_wizards(amount)


"""[
    {
        "id": "zsh_smart",
        "text": "Smart karta",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "hidden": True,
    },
    {
        "id": "zsh_hdbox",
        "text": "Box HD Kaon   příslušenství",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "hidden": True,
    },
    {
        "id": "zsh_camod",
        "text": "CA Modul",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "hidden": True,
    },
    {
        "id": "zsh_ovladac",
        "text": "Dálkový ovladač",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "min": 50,
        "max": 70,
    },
    {
        "id": "zsh_lnbsin",
        "text": "LNB konvertor - single",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "min": 0,
        "max": 1,
    },
    {
        "id": "zsh_lnbdua",
        "text": "LNB konvertor - dual",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
        "min": 4,
        "max": 5,
    },
    {
        "id": "zsh_lnbqua",
        "text": "LNB konvertor - quad",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_parab",
        "text": "Parabola",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_drzak",
        "text": "Držák na parabolu",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_objim",
        "text": "Objímka LNB - náhradní",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_koax",
        "text": "Koaxiální kabel",
        "unit": "m",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_hdkab",
        "text": "HDMI kabel",
        "unit": "ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_sckab",
        "text": "SCART kabel",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_fkon",
        "text": "F-konektor - šroubovací/kompresní",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_fspoj",
        "text": "F-spojka F/F",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_chkot",
        "text": "Chemická kotva",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_tmel",
        "text": "Tepelně izolační tmel pro fasády",
        "unit": "Ks",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
    {
        "id": "zsh_lista",
        "text": "Lišta (bíla standard)",
        "unit": "m",
        "unit_price_mon": 10,
        "unit_price_dem": 0,
        "prepaid": 0,
    },
]"""
