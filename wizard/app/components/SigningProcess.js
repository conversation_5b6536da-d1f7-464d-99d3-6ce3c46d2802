import React from 'react';
import {scanFolder, finalFolder, photoDataSite, signedFolder, zipFolder} from '../Constants';
import {AppState} from '../AppState';
import {listDir,
        jpgToPdf,
        saveText,
        zip,
        send,
        cancel,
        logD,
        scan,
        formPdf,
        signPdf,
        preview,
        createFolder,
        copy,
        loadFile} from '../Operations';
import ShowForm from './webforms/ShowForm.js';
import {fromJS} from 'immutable';

import PhotoDialog from './PhotoDialog';

const statusIcons = [
    'process-not-done wider',
    'process-done',
    ''
];

// Screen for signing process - scans and signatures
export default class SigningProcess extends React.Component {

    signingInProgress = false

    send = () => {
        if (this.canSend()) {
            const {password} = AppState.value;
            const zipName = zipFolder+'/document.zip';
            zip(zipName, finalFolder, password);
            saveText(zipFolder + '/metadata.json', JSON.stringify(AppState.value.get('metadata').toJS()));
            zip('SignedDocuments.zip', zipFolder);
            send('SignedDocuments.zip').then((result) => {
                alert('Balíček byl odeslán !');
                cancel();
            });
        }
    }

    formDocument = (id) => {
        AppState.transact((state) => {
            return state.merge(fromJS({
                currentFormShown: id,
                showForm: true
            }));
        });
    }

    copyDoc(path){
        copy(path, signedFolder)
        const parts = path.split('/')
        const newPath = signedFolder + '/' + parts[parts.length - 1]
        return newPath
    }

    lockCurrentFields = (id) => {
        let {formFields, lockedFields} = AppState.value
        

        formFields.forEach((value) => 
        {
            if(!lockedFields.has(value)){
                lockedFields = lockedFields.set(value, fromJS([id]))
            } else {
                if(!lockedFields.get(value).includes(id)){
                    lockedFields = lockedFields.set(value, lockedFields.get(value).push(id))
                }
            }
        })

        AppState.transact((state) => {
            return state.setIn(['lockedFields'], lockedFields)
        })
    }

    signDocument = (id) => {
        const {name, path} = AppState.value.getIn(['toSign', id]);
        let {pdfMetadata, fieldData} = AppState.value;
        if (!this.signingInProgress) {
            this.signingInProgress = true;
            logD('signing ' + name);

            // Inserting only date
            let formMeta = {};
            let d = new Date()
            let date = d.getDate()+'.'+(d.getMonth()+1)+'.'+d.getFullYear()
            formMeta['podpis_datum'] = date
            
            console.log('Building with ', formMeta);
            const newPath = this.copyDoc(path)
            formPdf(newPath, formMeta, 'data.csv');
            // Sign document with given id
            signPdf(newPath, finalFolder + '/signed_' + name, false).then((result) => {
                // 'true' if successful
                // 'cancel' if unsuccessful
                this.signingInProgress = false;

                if (result === 'cancel') {
                    // unsuccessful sign (whatever reason)
                    // can be even after previous successful sign
                    // TODO delete old pdf ?
                    // TODO maybe android app can do this
                    AppState.transact((state) => {
                        return state.setIn(['toSign', id, 'done'], 0);
                    });
                } else {
                    // successful sign, pdf is saved in finalFolder
                    AppState.transact((state) => {
                        return state.setIn(['toSign', id, 'done'], 1)
                            .set('showForm', false);
                    });
                    // mark that fields in AppState,formFields are now signed in form AppState,toSign,id
                    this.lockCurrentFields(id);
                }
            });
        }
    }

    previewDocument = (id) => {
        const {name, done} = AppState.value.getIn(['toSign', id]);
        alert('preview name '+name);
        if (done === 0) {
            // no document to preview
            return;
        }
        preview(finalFolder + '/signed_' + name);
    }

    canSend() {
        const {toSign} = AppState.value;
        const {scans_info} = AppState.value.getIn(['metadata'])
        return scans_info.getIn(['mandatory']).every(({done}) => done) && toSign.every(({done, required}) => !required||done);
    }

    renderButton(isEnabled, onClick, text, style, className) {
        return <input className={(isEnabled ? '' : ' disabled ')+className} onClick={onClick} style={style} type='button' value={text} />;
    }

    renderStatusIcon(statusIndex) {
        return <div className={statusIcons[statusIndex]}> </div>;
    }

    renderEmbedButtonsInAction(action, buttons, statusIndex) {
        return (
            <tr>
                <td style={{width: '100%'}}>{this.renderButton(true, action.onClick, action.text, {float: 'left'})}</td>
                {buttons.map((button) =>
                    <td>{this.renderButton(button.isEnabled, button.onClick, button.text)}</td>
                )}
                <td style={{verticalAlign: 'middle'}}>{this.renderStatusIcon(statusIndex)}</td>
            </tr>
        );
    }

    renderScansAction(scan, statusIndex) {
        return this.renderEmbedButtonsInAction(scan, [], statusIndex);
    }

    renderSigningAction(sign, preview, statusIndex) {
        return this.renderEmbedButtonsInAction(sign, [preview], statusIndex);
    }

    renderTableWrap(children) {
        return (
            <table className='clientTable' style={{paddingTop: '20px'}}>
                {children}
            </table>
        );
    }

    renderSignings() {
        const {toSign} = AppState.value;
        let mandatory = []
        let optional = []
        toSign.forEach(({done, title, required}, index) => {
            let item = this.renderSigningAction(
                {
                    onClick: () => this.signDocument(index),
                    text: (done ? this.props.msg.signing.resign : this.props.msg.signing.sign) + title,
                },
                {
                    isEnabled: done !== 0,
                    onClick: () => this.previewDocument(index),
                    text: this.props.msg.signing.preview,
                },
                done
            )
            if(required === true){
                mandatory.push(item)
            } else {
                optional.push(item)
            }
        })

        return <div>
            {mandatory.length > 0 && <div><h2>{this.props.msg.signing.mandatory}</h2>{this.renderTableWrap(mandatory)}</div>}
            {optional.length > 0 && <div><h2>{this.props.msg.signing.optional}</h2>{this.renderTableWrap(optional)}</div>}
        </div>
    }

    showPhotos = (event) => {
        const {current_site} = AppState.value;
        if (current_site !== photoDataSite) {
            AppState.transact((state) => {
                return state.merge(fromJS({
                    previous_site: current_site,
                    current_site: photoDataSite,
                }));
            });
        }
    }

    photosDone(){
        const {scans_info} = AppState.value.getIn(['metadata']);
        let a = scans_info.getIn(['mandatory']).every(({done}) => done);
        return a === true ? 1 : 0;
    }

    renderPhoto() {
        return AppState.value.hasIn(['metadata', 'scans_info']) ? 
            this.renderTableWrap(
            this.renderScansAction(
                {
                    onClick: () => this.showPhotos(),
                    text: this.props.msg.signing.photos,
                },
                this.photosDone()
            )
        ) : null
    }

    render() {
        const {showForm, currentFormShown} = AppState.value;
        
        return (
            <div>
                {showForm ?
                    <ShowForm
                        onSubmit={() => this.signDocument(currentFormShown)}
                        onBack={() => AppState.transact((state) => state.set('showForm', false))}
                        msg={this.props.msg}
                    />
                :
                    <div id="siteContent">
                        <div id="mainContent" style={{padding: '20px'}}>
                            {this.renderSignings()}
                            {this.renderPhoto()}
                            <p className="recordItemActions">
                                {this.renderButton(this.canSend(), this.send, this.props.msg.signing.send, {width: '100px'}, 'greenColor')}
                                <br style={{clear: 'right'}} />
                            </p>
                        </div>
                    </div>
                }
            </div>
        );
    }
}
