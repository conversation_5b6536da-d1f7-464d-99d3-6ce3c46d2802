import React from 'react';
import {Input, Radio, Checkbox, Text, Select} from './Inputs';
import {AppState} from '../../AppState';
import {logD} from '../../Operations';
import {blurOnEnter, 
        canConfirm,
        getMaterialValue,
        counter,
        resolveSignatorName} from './formHelpers'
import {Input as RInput, Row, Col, Grid, FormControls, Button} from 'react-bootstrap';
import {Navbar as MNavbar} from './Navbar';
import {fromJS} from 'immutable';
import {matchRegex, 
        notEmpty, 
        matchDia, 
        validateInputsFactory,
        cond,
        isPerson,
        isFirm,
        validMin,
        validMax,
        isID,
        isActingPerson} from './validationHelpers';
import Rx from 'rx';

const mon_dem = fromJS(['_mon', '_dem'])

export class FinalForm extends React.Component {

	constructor(props) {
    super(props);

    let validators = {
      uzi_email: [matchRegex('uzi_email', /^$|^.+@.+\.[a-z]{2,}$/, 'Vložte prosím platný e-mail.')],
      uzi_telc: [matchRegex('uzi_telc', /^$|^\+{0,1}[0-9\ ]*$/, 'Vložte prosím platné telefonní číslo.')],
      uzi_mobc: [matchRegex('uzi_mobc', /^\+{0,1}[0-9\ ]*$/, 'Vložte prosím platné mobilní číslo.')],
      uzi_obch_firm: [cond(isFirm, matchDia('uzi_obch_firm', /^[a-zA-Z .0-9-]*$/, 'Zadejte prosím platný název.'))],
      uzi_krestni_jmeno: [cond(isPerson, matchDia('uzi_krestni_jmeno', /^[a-zA-Z .]*$/, 'Zadejte prosím platný jméno.'))],
      uzi_prijmeni: [cond(isPerson, matchDia('uzi_prijmeni', /^[a-zA-Z .-]*$/, 'Zadejte prosím platný příjmení.'))],
      ins_ulice: [matchDia('ins_ulice', /^[a-zA-Z .]*$/, 'Zadejte prosím platný název ulice.')],
      ins_mesto: [matchDia('ins_mesto', /^[a-zA-Z .]*$/, 'Zadejte prosím platný název města.')],
      ins_poc: [matchRegex('ins_poc', /^[0-9 ]*$/, 'Prosím, zadejte platné číslo domu.')],
      ins_orc: [matchRegex('ins_orc', /^[0-9 ]*$/, 'Prosím, zadejte platné orientační číslo.')],
      ins_psc: [matchRegex('ins_psc', /^[0-9 ]*$/, 'Zadejte prosím platné PSČ.')],
      ooj_jmeno: [matchDia('ooj_jmeno', /^[a-zA-Z .-]*$/, 'Zadejte prosím platný jméno.')],
      ooj_rc: [matchRegex('ooj_rc', /^[0-9/ ]*$/, 'Zadejte prosím platné rodné číslo.'), 
                isID('ooj_rc')],
      ooj_idc: [matchRegex('ooj_idc', /^[a-zA-Z0-9]*$/, 'Zadejte prosím platné číslo.'),
                cond(isActingPerson, notEmpty('ooj_idc'))],
      zsh_castka: [],
      zsh_doba: [matchRegex('zsh_doba', /^[0-9]*$/, 'Zadejte prosím čas v minutách.')],
      zsh_servuzi: [],
      zsh_odm: [],
      zsh_nadstd: [],
      zsh_pozn: [],
      prist_puv_1: [matchRegex('prist_puv_1', /^$|^[0-9a-zA-Z]{12,17}$/, 'Zadejte prosím platné sériové číslo (12-17 alfanumerických znaků).')],
      prist_puv_2: [matchRegex('prist_puv_2', /^$|^[0-9a-zA-Z]{12,17}$/, 'Zadejte prosím platné sériové číslo (12-17 alfanumerických znaků).')],
      prist_nov_1: [matchRegex('prist_nov_1', /^$|^[0-9a-zA-Z]{12,17}$/, 'Zadejte prosím platné sériové číslo (12-17 alfanumerických znaků).')],
      prist_nov_2: [matchRegex('prist_nov_2', /^$|^[0-9a-zA-Z]{12,17}$/, 'Zadejte prosím platné sériové číslo (12-17 alfanumerických znaků).')],
      karta_puv_1: [matchRegex('karta_puv_1', /^$|^[0-9]{12,12}$/, 'Zadejte prosím platné sériové číslo (12 numerických znaků).')],
      karta_puv_2: [matchRegex('karta_puv_2', /^$|^[0-9]{12,12}$/, 'Zadejte prosím platné sériové číslo (12 numerických znaků).')],
      karta_nov_1: [matchRegex('karta_nov_1', /^$|^[0-9]{12,12}$/, 'Zadejte prosím platné sériové číslo (12 numerických znaků).')],
      karta_nov_2: [matchRegex('karta_nov_2', /^$|^[0-9]{12,12}$/, 'Zadejte prosím platné sériové číslo (12 numerických znaků).')]
	  }

    if(AppState.value.hasIn(['metadata', 'materials_info'])){
  	  AppState.value.getIn(['metadata', 'materials_info'])
      .forEach(({text, id, min, max, hidden, type}, index) => {
        //do not use filter here you need indexes
        if(type == null || type === 'checkbox', type === 'dropdown'){
          return
        }

        mon_dem.forEach((val) => {
        	validators[id+val] = [matchRegex(id+val, /^\d*(\.{0,1}\,{0,1}\d*)?$/, 'Zadejte prosím platný množství.'),
                             validMin(id+val, index), validMax(id+val, index)]
          })
      })
    }

		this.fieldValidators = fromJS(validators)

  	this.validateInputs = validateInputsFactory(this.fieldValidators)

  	this.validationStream = new Rx.Subject();
  	this.subscription = this.validationStream
    	.debounce(700)
    	.subscribe(this.validateInputs)
  }

  componentWillMount(){
    AppState.transact((state) => {
      return state.updateIn(['formFields'], list => list.clear());
    })
    this.computePrice();
    this.checkInstAddr();
    this.validateInputs();
  }

  computePrice(){
    const computeRowPrice = (a, {id, unit_price_mon, unit_price_dem, prepaid}) => {
      let val_mon = getMaterialValue(a, id+'_mon')
      let val_dem = getMaterialValue(a, id+'_dem')

      let count = val_mon - prepaid < 0 ? 0 : val_mon - prepaid
      let res = count * unit_price_mon
      res += val_dem * unit_price_dem

      return res
    }

    //cam/cards fields to count filled
    const cam_new_field = ['prist_nov_1', 'prist_nov_2']
    const cam_old_field = ['prist_puv_1', 'prist_puv_2']
    const card_new_field = ['karta_nov_1', 'karta_nov_2']
    const card_old_field = ['karta_puv_1', 'karta_puv_2']

    let a = AppState.value.get('metadata')
    a = a.setIn(['zsh_smart_mon', 'value'], counter(card_new_field))
    a = a.setIn(['zsh_smart_dem', 'value'], counter(card_old_field))
    a = a.setIn(['zsh_camod_mon', 'value'], counter(cam_new_field))
    a = a.setIn(['zsh_camod_dem', 'value'], counter(cam_old_field))
    let res = 0
    if(a.hasIn(['materials_info'])){
      a.getIn(['materials_info']).forEach((val) => {
        res += computeRowPrice(a, val)
      })
    }
    //price over time
    let pot = getMaterialValue(a, 'zasah_doba_cena')
    let predpl = getMaterialValue(a, 'zasah_doba_predpl')
    let time = getMaterialValue(a, 'zsh_doba')-predpl//celkova doba bez preplateneho casu
    time = time < 0 ? 0 : time
    time = Math.ceil(time/15)
    res += pot*time
    //cant be lower than zero
    res = res < 0 ? 0 : res
    a = a.setIn(['zsh_castka', 'value'], res)
    
    AppState.transact((state) => {
      return state.set('metadata', a);
    });
  }


  renderMaterialRow(validation, value){
  	const {text, unit, id, min, max, type} = value
    let mon, dem;
    let field_mon = id+'_mon'
    let field_dem = id+'_dem'
    if(type === 'checkbox'){
      mon = <Checkbox field={field_mon} onChange={this.computePrice} />
      dem = <Checkbox field={field_dem} onChange={this.computePrice} />
    } else if (type === 'dropdown'){
      mon = <Select field={field_mon} onChange={this.computePrice} />
      dem = <Select field={field_dem} onChange={this.computePrice} />
    } else {
      mon = <Input type="number" field={field_mon} className="form-control" onChange={this.computePrice} {...validation}/>
      dem = <Input type="number" field={field_dem} className="form-control" onChange={this.computePrice} {...validation}/>
    }

  	return(
	  	<RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9 materialElement" label={text}>
	      <Row>
	        <Col xs={5}>
            {mon}
          </Col>
	        <Col xs={5}>
            {dem}
          </Col>
	        <Col xs={2} style={{marginTop: '7px'}}>
	        	{unit}
	        </Col>
	      </Row>
	    </RInput>
	  )
  }

  renderMaterials(validation){

    if(AppState.value.getIn(['metadata', 'materials_info']) != null ){
      let header = <Row>
        <Col xsOffset={3} xs={9}>
          <Col xs={5}><span className="centerElement">Montáž</span></Col> 
          <Col xs={5}><span className="centerElement">Demontáž</span></Col>
        </Col>
      </Row>

      let arr = AppState.value.getIn(['metadata', 'materials_info']).filter(({hidden}) => !hidden).map((value) => {
        return this.renderMaterialRow(validation, value) 
      })

      return(<div>{header}{arr}</div>)
    }
  }

  checkInstAddr = () => {
  	const addr = fromJS(['ulice', 'mesto', 'poc', 'orc', 'psc'])

  	const empty = addr.every((value) => !AppState.value.getIn(['metadata', 'ins_'+value, 'value']))

  	if(empty){
  		AppState.transact( (state) => {
				addr.forEach((value) => {
					state = state.setIn(['metadata', 'ins_'+value, 'value'], AppState.value.getIn(['metadata', 'byd_'+value, 'value']))
				})
				return state
			})
  	}

  }

  confirm = () => {
    this.postProcessing()
    this.props.onSubmit()
  }

  renderButtons(){
    return [{
  				title: "Zpět",
          bsStyle: "primary",
  				onClick: this.props.onBack
				},
				{
          title: "OK",
          onClick: this.confirm,
          bsStyle: "success",
          disabled: !canConfirm(this.fieldValidators)
	      },
    ]
  }

  validateConstraints(){
    let {metadata} = AppState.value;

    if(metadata.getIn(['typ_televize_radio', 'value']) !== 'lcd'){
      metadata.delete('cam_radio')
    }

    AppState.transact((state) => {
      return state.setIn(['metadata'], metadata)
    });
  }

  postProcessing(){
    let {metadata} = AppState.value;
    //uzi_jmeno
    let name = []
    if(metadata.getIn(['osoba_typ_radio', 'value']) === 'fyz'){
      if(!!metadata.getIn(['uzi_krestni_jmeno', 'value'])){
        name.push(metadata.getIn(['uzi_krestni_jmeno', 'value']))
      }
      if(!!metadata.getIn(['uzi_prijmeni', 'value'])){
        name.push(metadata.getIn(['uzi_prijmeni', 'value']))
      }
    } else {
      name.push(metadata.getIn(['uzi_obch_firm', 'value']))
    }

    metadata = metadata.setIn(['uzi_jmeno', 'value'], name.join(' '))

    //podp_mesto
    metadata = metadata.setIn(['podp_mesto', 'value'], metadata.getIn(['ins_mesto', 'value']))

    //podp_datum
    let d = new Date()
    let date = d.getDate()+'.'+(d.getMonth()+1)+'.'+d.getFullYear()
    metadata = metadata.setIn(['podp_datum', 'value'], date)

    //podp_jmeno_uzi
    metadata = resolveSignatorName(metadata)

    //podp_jmeno_tech
    metadata = metadata.setIn(['podp_jmeno_tech', 'value'], metadata.getIn(['tech_jmeno', 'value']))

    let materials = []
    if(metadata.hasIn(['materials_info'])){
      metadata.getIn(['materials_info']).forEach(({id, text, unit}) => {
        let mon = getMaterialValue(metadata, id+'_mon')
        let dem = getMaterialValue(metadata, id+'_dem')
        if(mon !== 0 || dem !== 0){
          materials.push({
            text: text,
            mon: mon,
            dem: dem,
            unit: unit
          })
        }
      })
    }

    //adds zero before short string
    function pad (str, max) {
      str = str.toString();
      return str.length < max ? pad("0" + str, max) : str;
    }

    for(let i = 1; i <= 18; i++){
      let iter = pad(i, 2)
      let matItem = i <= materials.length ? materials[i-1] : {text: '',mon: '', dem: '', unit: ''}
      metadata = metadata.setIn(['zsh_'+iter, 'value'], matItem.text)
      metadata = metadata.setIn(['zsh_mon_'+iter, 'value'], matItem.mon)
      metadata = metadata.setIn(['zsh_dem_'+iter, 'value'], matItem.dem)
      metadata = metadata.setIn(['zsh_jed_'+iter, 'value'], matItem.unit)
    }

    //uzi_telmobc
    let telmobc = []
    if(!!metadata.getIn(['uzi_telc', 'value'])){
      telmobc.push(metadata.getIn(['uzi_telc', 'value']))
    }
    if(!!metadata.getIn(['uzi_mobc', 'value'])){
      telmobc.push(metadata.getIn(['uzi_mobc', 'value']))
    }

    metadata = metadata.setIn(['uzi_telmobc', 'value'], telmobc.join(';'))

    //mark right checkbox for document type
    const types = {"Migrace": "mig",
                   "Nová instalace": "novins",
                    "Servis SD": "sdserv",
                    "Servis ND": "ndserv",
                    "Partner box": "part",
                    "Přeinstalace": "preins",
                    "Demontáž": "dem"}
    metadata = metadata.setIn(['zahl_radio', 'value'], types[AppState.value.get('type')])

    //address
    const addr = metadata.getIn(["ins_ulice", "value"])+" "
      +metadata.getIn(["ins_poc", "value"])+" "
      +metadata.getIn(["ins_orc", "value"])+" \n"
      +metadata.getIn(["ins_mesto", "value"])+" "
      +metadata.getIn(["ins_psc", "value"])

    metadata = metadata.setIn(['uzi_adresa', 'value'], addr)

    AppState.transact( (state) => {
      return state.setIn(['pdfMetadata'] , metadata)
    })
  }

  render() {
  	const validation = {
      validationStream: this.validationStream,
    	validateInputs: this.validateInputs
    }

    return (
  	 	<div className="document document-digi-provedeni" onKeyPress={blurOnEnter}>
        <Grid fluid={true}>
        <header className="header">
          <MNavbar buttons={this.renderButtons()} />
        <h1>
          <div className="header">
            <span>FORMULÁŘ O PROVEDENÍ / PŘEVZETÍ</span>
      	</div>
        </h1>
        </header>
        <form className="form-horizontal">
          <section className="section">

            <h2>UŽIVATEL</h2>
            <Row>
              <Col xs={6}>
                <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
                  <Col xs={6}>
                    <Radio option="fyz" name="osoba_typ_radio" className="radio" label="Fyzická osoba" {...validation}/>
                  </Col>
                  <Col xs={6}>
                    <Radio option="prav" name="osoba_typ_radio" className="radio" label="Právnická osoba" {...validation}/>
                  </Col>
                </RInput>

                {AppState.value.getIn(['metadata', 'osoba_typ_radio', 'value']) === 'fyz' ? <div>
                  <Input key={1} field="uzi_krestni_jmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Jméno" {...validation}/>

                  <Input key={2} field="uzi_prijmeni" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Příjmení" {...validation}/>
                </div>
                : 
                <div>
                  <Input key={3} field="uzi_obch_firm" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Obchodní firma" {...validation}/>
                </div>}
              </Col>

              <Col xs={6}>
                <Input type="tel" field="uzi_telc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Telefonní číslo" {...validation}/>

                <Input type="tel" field="uzi_mobc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Mobilní telefon" {...validation} />

                <Input type="email" field="uzi_email" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="E-mail" {...validation} />
              </Col>
            </Row>

            <Row>
              <p><strong className="strong">Osoba oprávněná jednat za Uživatele</strong></p>

              <Col xs={6}>
                <Input field="ooj_jmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Jméno, příjmení" {...validation}/>
              </Col>

              <Col xs={6}>
                <Input field="ooj_rc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Rodné číslo" {...validation}/>
                <Input field="ooj_idc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Číslo OP/pasu" {...validation}/>
              </Col>
            </Row>

            <h3>Adresa instalace</h3>

            <Row>
              <Col xs={6}>
                <Input
                	field="ins_ulice"
                	labelClassName="col-xs-3"
                	wrapperClassName="col-xs-9"
                	label="Ulice"
                	{...validation}
                />

                <Input
                	field="ins_mesto"
                	labelClassName="col-xs-3"
                	wrapperClassName="col-xs-9"
                	label="Město"
                	{...validation}
                />
              </Col>
              <Col xs={6}>
                <RInput labelClassName="col-xs-5" wrapperClassName="col-xs-7" label="Číslo popisné / orientační">
                  <Row>
                    <Col xs={6}>
                      <Input
                        type="number"
                        field="ins_poc"
                        className="form-control"
                        {...validation}
                      />
                    </Col>
                    <Col xs={6}>
                      <Input
                        type="number"
                      	field="ins_orc"
                      	className="form-control"
                      	{...validation}
                      />
                    </Col>
                  </Row>
                </RInput>
                <Input
                  type="tel"
                	field="ins_psc"
                	labelClassName="col-xs-3"
                	wrapperClassName="col-xs-6"
                	label="PSČ"
                	{...validation}
                />
              </Col>
            </Row>
          </section>

          <section className="section">
          	<h2>SLUŽBY</h2>
            <Row>
              <Col xs={6} >
                <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Doba trvání zásahu">
                  <Row>
                    <Col xs={10}>
                      <Input field="zsh_doba" type="number" className="form-control" {...validation} onChange={this.computePrice} />
                    </Col>
                    <Col xs={2} style={{marginTop: '7px'}}>min</Col>
                  </Row>
                </RInput>
                <h3>POUŽITÝ MATERIÁL<br/><small>(v případě demontáže odevzdaný materiál)</small></h3>
                {this.renderMaterials(validation)}

              </Col>
              <Col xs={6}>
								<label className="control-label">Úkony</label>
                <RInput wrapperClassName="col-xs-12">
                	<Checkbox field="zsh_uzi_chb" label="Servis způsobený zásahem Uživatele" wrapperClassName="chb-w-note" />
                	<Input type="textarea" field="zsh_servuzi" className="form-control min-height"/>
                	<Checkbox field="zsh_odm_chb" label="Uživatel odmítl výměnu zařízení" wrapperClassName="chb-w-note"/>
                	<Input type="textarea" field="zsh_odm" className="form-control min-height"/>
                	<Checkbox field="zsh_nadstd_chb" label="Platba technikovi za nadstandardní práce na přání Uživatele(mimo služby DIGI TV)" wrapperClassName="chb-w-note"/>
                	<Input type="textarea" field="zsh_nadstd" className="form-control min-height"/>
                	<p className="note">Poznámka k zásahu</p>
                	<Input type="textarea" field="zsh_pozn" className="form-control min-height"/>
                </RInput>
              </Col>
              <Col xs={12}>
              	<Input field="zsh_castka"
              	 labelClassName="col-xs-9"
              	 wrapperClassName="col-xs-3"
              	 label="Orientační částka k úhradě za materiál a provedené práce v následující faktuře"
								 {...validation}
								/>
              </Col>
            </Row>
          </section>
          <section className="section has-scans">
          	<Row>
          		<Col xs={6}>
            		<strong className="strong">Sériové číslo původního přístroje</strong>
								<Input field="prist_puv_1" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="1" scanButton={true} {...validation} onChange={this.computePrice}/>
								<Input field="prist_puv_2" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="2" scanButton={true} {...validation} onChange={this.computePrice}/>
							</Col>
							<Col xs={6}>
            		<strong className="strong">Sériové číslo nového přístroje</strong>
								<Input field="prist_nov_1" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="1" scanButton={true} {...validation} onChange={this.computePrice}/>
								<Input field="prist_nov_2" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="2" scanButton={true} {...validation} onChange={this.computePrice}/>
							</Col>
          	</Row>
          	<Row>
          		<Col xs={6}>
            		<strong className="strong">Sériové číslo původní karty</strong>
								<Input type="number" field="karta_puv_1" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="1" scanButton={true} {...validation} onChange={this.computePrice}/>
								<Input type="number" field="karta_puv_2" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="2" scanButton={true} {...validation} onChange={this.computePrice}/>
							</Col>
							<Col xs={6}>
            		<strong className="strong">Sériové číslo nové karty</strong>
								<Input type="number" field="karta_nov_1" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="1" scanButton={true} {...validation} onChange={this.computePrice}/>
								<Input type="number" field="karta_nov_2" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="2" scanButton={true} {...validation} onChange={this.computePrice}/>
							</Col>
          	</Row>
          </section>
          <section className="section">
            <h2>TYP TELEVIZE</h2>
            <Row>
              <Col xs={6}>
                <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
                  <Row>
                    <Col xs={6}>
                      <Radio option="crt" name="typ_televize_radio" className="radio" label="CRT (hluboká)" onChange={this.validateConstraints}/>
                      <Radio option="lcd" name="typ_televize_radio" className="radio" label="LCD (plochá)" onChange={this.validateConstraints}/>
                    </Col>
                    {AppState.value.getIn(['metadata', 'typ_televize_radio', 'value']) === 'lcd' ? 
                    <Col xs={6}>
                      <Radio option="ano" name="cam_radio" className="radio" label="CAM ne" />
                      <Radio option="ne" name="cam_radio" className="radio" label="CAM ano" />
                    </Col>
                    :
                    null
                    }
                  </Row>
                </RInput>
              </Col>
              <Col xs={6}>
                <Select  
                  field="vyrobce_tv" 
                  labelClassName="col-xs-3" 
                  wrapperClassName="col-xs-9" 
                  label="Výrobce TV"
                />
              </Col>
            </Row>
          </section>
        </form>
        </Grid>
      </div>
    );
  }
}
