import React from 'react';
import {Input as RInput, Navbar, Button} from 'react-bootstrap';
import {AppState} from '../../AppState';
import {fromJS, is} from 'immutable';
import {feedback} from './validationHelpers';
import {getQRcode} from '../../Operations';

function showDialog(field) {
    return () => {
        AppState.transact((state) => {
            return state.set('showUnlockDialog', true)
                        .set('fieldToUnlock', field)})
    }
}

export class Input extends React.Component {

    shouldComponentUpdate(nextProps, nextState){
        const {field} = this.props;
        let render = false;
        let meta = AppState.value.getIn(['metadata', field])
        let locked = AppState.value.getIn(['lockedFields']).has(field)
        let disabled = AppState.value.getIn(['disabledFields']).has(field)
        let validation = AppState.value.getIn(['validation', field])

        if(!is(meta, this.meta)){
            render = true
        }
        if(!render && !is(validation, this.validation)){
            render = true
        }
        if(!render && !is(disabled, this.disabled)){
            render = true
        }
        if(!render && !is(locked, this.locked)){
            render = true
        }
        this.meta = meta
        this.locked = locked
        this.disabled = disabled
        this.validation = validation

        return render
    }

    componentWillMount(){
        AppState.transact((state) => {
            return state.updateIn(['formFields'], list => list.set(this.props.field, this.props.field));
        })
    }

    inputChanged = (e) => {
        AppState.transact((state) => {
            this.props.validationStream && this.props.validationStream.onNext(null);
            return state.setIn(['metadata', this.props.field, 'value'], e.target.value);
        });

        if(this.props.onChange != null){
            this.props.onChange(e);
        }
    }

    isDisabled(){
        const {field} = this.props;
        const editable = AppState.value.getIn(['metadata', field, 'editable']);
        const locked = AppState.value.getIn(['lockedFields']).has(field); //signed field
        const tempDis = AppState.value.getIn(['disabledFields']).has(field); //temporary disabled field
        let disabled = editable === false ? true : false

        return disabled || locked || tempDis
    }

    render() {
        const {field, className, col, labelClassName, wrapperClassName, label, type, pattern, scanButton} = this.props;
        const fieldData = AppState.value.getIn(['metadata', field]);
        const {value, editable} = fieldData || {value: null, editable: null};
        const locked = AppState.value.getIn(['lockedFields']).has(field);
        const disabled = this.isDisabled()
        const onClick = locked ? showDialog(field) : () => {}
        const validity = AppState.value.getIn(['validation', field])
        const validateInputs = this.props.validateInputs || () => {}

        return (
            <RInput
                type={type || "text"}
                id={field}
                pattern={pattern}
                buttonAfter={scanButton ? <ScanButton 
                                            field={field} 
                                            validateInputs={validateInputs} 
                                            disabled={disabled} 
                                            onClick={onClick} 
                                            onChange={this.props.onChange} /> : null}
                field={field}
                labelClassName={labelClassName}
                wrapperClassName={wrapperClassName}
                label={label}
                className={className}
                onChange={this.inputChanged}
                value={this.props.value||value}
                disabled={disabled}
                onBlur={validateInputs}
                onClick={onClick}
                {...feedback(validity)}
            />
        );
    }
}

class ScanButton extends React.Component {
    getScan = () =>{
        getQRcode().then((result) => {
            if(result === 'cancel'){
                return
            }
            AppState.transact((state) => {
                return state.setIn(['metadata', this.props.field, 'value'], result)
            })
            this.props.validateInputs()
            if(this.props.onChange != null){
                this.props.onChange();
            }
        }
        )
      }

    render() {
        const onClick = this.props.disabled ? this.props.onClick : this.getScan
        return (<Button onClick={onClick} className="scan-camera" />);
    }
}

export class Checkbox extends React.Component {

    shouldComponentUpdate(nextProps, nextState){
        const {field} = this.props;
        let render = false;
        let meta = AppState.value.getIn(['metadata', field])
        let locked = AppState.value.getIn(['lockedFields']).has(field)
        let disabled = AppState.value.getIn(['disabledFields']).has(field)
        let validation = AppState.value.getIn(['validation', field])

        if(!is(meta, this.meta)){
            render = true
        }
        if(!render && !is(locked, this.locked)){
            render = true
        }
        if(!render && !is(validation, this.validation)){
            render = true
        }
        if(!disabled && !is(disabled, this.disabled)){
            render = true
        }
        this.meta = meta
        this.locked = locked
        this.validation = validation
        this.disabled = disabled

        return render
    }

    componentWillMount(){
        AppState.transact((state) => {
            return state.updateIn(['formFields'], list => list.set(this.props.field, this.props.field));
        })
    }

    inputChanged = (e) => {
        AppState.transact((state) => {
            return state.setIn(['metadata', this.props.field, 'value'], e.target.checked);
        });

        if(this.props.onChange != null){
            this.props.onChange(e);
        }

        if(this.props.validateInputs != null){
            this.props.validateInputs();
        }
    }

    isDisabled(){
        const {field} = this.props;
        const editable = AppState.value.getIn(['metadata', field, 'editable']);
        const locked = AppState.value.getIn(['lockedFields']).has(field); //signed field
        const tempDis = AppState.value.getIn(['disabledFields']).has(field); //temporary disabled field
        let disabled = editable === false ? true : false

        return disabled || locked || tempDis
    }

    render() {
        const {field, label, wrapperClassName, labelClassName} = this.props;
        const checked = AppState.value.getIn(['metadata', field, 'value']);
        const locked = AppState.value.getIn(['lockedFields']).has(field);
        const onClick = locked ? showDialog(field) : () => {}
        const validity = AppState.value.getIn(['validation', field])

        return (
            <div onClick={onClick} >
                <RInput 
                    type="checkbox" 
                    id={field} 
                    wrapperClassName={wrapperClassName}
                    labelClassName={labelClassName}
                    onChange={this.inputChanged} 
                    checked={checked} 
                    label={label}
                    disabled={this.isDisabled()}
                    onClick={onClick}
                    {...feedback(validity)}
                 />
            </div>
        );
    }
}



export class Radio extends React.Component {

    componentWillMount(){
        AppState.transact((state) => {
            return state.updateIn(['formFields'], (list) => 
                {
                    return list.set(this.props.name, this.props.name)
                });
        })
    }

    getName() {
        const {name, option} = this.props;
        return name + '_' + option + '_chb';
    }

    inputChanged = (e) => {
        const {name, option} = this.props;
        AppState.transact((state) => {
            return state.setIn(['metadata', name, 'value'], option);
        });

        if(this.props.onChange != null){
            this.props.onChange();
        }

        if(this.props.validateInputs != null){
            this.props.validateInputs();
        }
    }

    componentDidMount() {
        const {name, option} = this.props;

        AppState.transact((state) => {
            return state.mergeIn(['fieldData', name], fromJS({[option]: option}));
        });
    }

    shouldComponentUpdate(nextProps, nextState){
        const {name} = this.props;
        let render = false;
        let meta = AppState.value.getIn(['metadata', name])
        let locked = AppState.value.getIn(['lockedFields']).has(name)

        if(!is(meta, this.meta)){
            render = true
        }
        if(!render && !is(locked, this.locked)){
            render = true
        }
        this.meta = meta
        this.locked = locked

        return render
    }

    render() {
        const {name, option} = this.props;
        const checked = AppState.value.getIn(['metadata', name, 'value']) === option;
        const editable = AppState.value.getIn(['metadata', name, 'editable']);
        const locked = AppState.value.getIn(['lockedFields']).has(name);
        let disabled = editable === false ? true : false;
        disabled = disabled ? disabled : locked  
        const onClick = locked ? showDialog(name) : () => {}

        return (
            <div onClick={onClick} >
                <RInput 
                    type="radio" 
                    id={this.getName()} 
                    checked={checked} 
                    onChange={this.inputChanged} 
                    label={this.props.label} 
                    disabled={disabled}
                />
            </div>
        );
    }
}

export class Select extends React.Component {

    componentWillMount(){
        AppState.transact((state) => {
            return state.updateIn(['formFields'], list => list.set(this.props.field, this.props.field));
        })
    }

    inputChanged = (e) => {
        AppState.transact((state) => {
            this.props.validationStream && this.props.validationStream.onNext(null);
            return state.setIn(['metadata', this.props.field, 'value'], e.target.value);
        });

        if(this.props.onChange != null){
            this.props.onChange(e);
        }
    }

    renderOptions = (field) => {
        const options = AppState.value.getIn(['metadata', field, 'options'])

        let res = []
        if(options != null){
            options.forEach(({key, value}) => {
                res.push(<option key={key} value={key}>{value}</option>)
            })
        }
        res.push(<option key={null} hidden/>)
        return res
    }

    shouldComponentUpdate(nextProps, nextState){
        const {field} = this.props;
        let render = false;
        let meta = AppState.value.getIn(['metadata', field])
        let locked = AppState.value.getIn(['lockedFields']).has(field)

        if(!is(meta, this.meta)){
            render = true
        }
        if(!render && !is(locked, this.locked)){
            render = true
        }
        this.meta = meta
        this.locked = locked
        
        return render
    }

    render() {
        const {field, className, col, labelClassName, wrapperClassName, label} = this.props;
        const fieldData = AppState.value.getIn(['metadata', field]);
        const {value, editable} = fieldData || {value: null, editable: null};
        const locked = AppState.value.getIn(['lockedFields']).has(field);
        let disabled = editable === false ? true : false;
        disabled = disabled ? disabled : locked
        const onClick = locked ? showDialog(field) : () => {}
        
        return (
            <RInput
                type="select"
                id={field}
                field={field}
                labelClassName={labelClassName}
                wrapperClassName={wrapperClassName}
                label={label}
                className={className}
                onChange={this.inputChanged}
                disabled={disabled}
                onClick={onClick}
                value={value}
            >
            {this.renderOptions(field)}
            </RInput>
        );
    }
}

