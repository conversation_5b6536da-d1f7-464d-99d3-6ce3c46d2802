import React from 'react';
import {Input as RInput, Navbar as <PERSON><PERSON><PERSON><PERSON>, <PERSON>v, <PERSON>ton, Row, Col} from 'react-bootstrap';
import {AppState} from '../../AppState';


export class Text extends React.Component {
    render(){
        return( <span>{this.props.value}</span>);
    }
}

export class Navbar extends React.Component {

	renderButton(btn){
		return(
      <Button
      	onClick={btn.onClick}
      	type="submit"
      	bsStyle={btn.bsStyle}
      	disabled={btn.disabled}
      	className={btn.className}>{btn.title}</Button>
		)
	}

	renderButtons(){
		if(!this.props.buttons){
			return null
		}
		let res = []

		for(let i = 0; i < this.props.buttons.length; i++ ){
			res.push(this.renderButton(this.props.buttons[i]))
		}

    	return (
    		<Col md={5}>
          <div className="btn-group pull-right">
            {res}
          </div>
        </Col>
    	)
    }

    renderName(){
      return(
        <div>
          <Text value={AppState.value.getIn(["metadata", "zakaznik_meno"])} /> <br/>
          <Text value={AppState.value.getIn(["metadata", "zakaznik_priezvisko"])} />
        </div>
        
      )
    }

    render() {

        return (
        	<RNavbar fixedTop={true}>
            <Row className="headerRow vertical-align">
              <Col md={2}>
                <div className="logo" />
              </Col>
              <Col md={2}>
			          {this.renderName()}
              </Col>
              <Col md={2}>
              		<Text value={AppState.value.getIn(["metadata", "zakaznik_adresa"])} />
              </Col>
              <Col md={1}>
                <Text value={AppState.value.get("type")} />
              </Col>
              {this.renderButtons()}
            </Row>
            </RNavbar>
        );
    }
}
