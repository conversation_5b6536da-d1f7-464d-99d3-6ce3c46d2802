import React from 'react'
import {Input, Radio} from '../Inputs';
import {Input as RInput, Row, Col, FormControls} from 'react-bootstrap';
import {AppState} from '../../../AppState';
import {fromJS} from 'immutable';
import {matchRegex, 
        notEmpty,  
        cond, 
        isSipo} from '../validationHelpers';

export const serviceValidators = fromJS({
	kauce_vyse: [matchRegex('kauce_vyse', /^\d*(\.{0,1}\,{0,1}\d*)?$/, 'Prosím, zadejte kauci.')],
  predpla_vyse: [matchRegex('predpla_vyse', /^\d*(\.{0,1}\,{0,1}\d*)?$/, 'Prosím, zadejte výši předplatby.')],
  zpuhraz_sipoc: [matchRegex('zpuhraz_sipoc', /^[0-9/ ]*$/, 'Zadejte prosím platné číslo SIPO.'),
                  cond(isSipo, notEmpty('zpuhraz_sipoc'))],
  zpuhraz_ucetc: [matchRegex('zpuhraz_ucetc', /^[0-9 ]*$/, 'Prosím, zadejte platné číslo bankovního účtu.')],
  zpuhraz_bankod: [matchRegex('zpuhraz_bankod', /^[0-9 ]*$/, 'Prosím, zadejte platný kód banky.')]
})

export class ServicePart extends React.Component {
	render(){
		const validation = this.props.validation
		return(
			<div>
				<Row>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Kauce">
                <Row>
                  <Col xs={12}>
                    <Radio option="ano" name="kauce_radio" className="radio" label="ano"/>
                    <Radio option="ne" name="kauce_radio" className="radio" label="ne"/>
                  </Col>
                </Row>
            </RInput>
          </Col>
          <Col xs={6}>
            <Input type="number" field="kauce_vyse" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Výše kauce" {...validation}/>
          </Col>
        </Row>

        <Row>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Předplacení služby">
                <Row>
                  <Col xs={12}>
                    <Radio option="ano" name="predpla_radio" className="radio" label="ano"/>
                    <Radio option="ne" name="predpla_radio" className="radio" label="ne"/>
                  </Col>
                </Row>
            </RInput>
          </Col>
          <Col xs={6}>
            <Input type="number" field="predpla_vyse" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Výše předplatby" {...validation}/>
          </Col>
        </Row>

        <Row>
          <Col xs={6}>
           <FormControls.Static label="Frekvence plateb" labelClassName="col-xs-3" wrapperClassName="col-xs-9" value="Měsíčně" />
          </Col>
        </Row>

        <Row>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Způsob hrazení">
                <Row>
                  <Col xs={12}>
                    <Radio option="sipo" name="zpuhraz_radio" className="radio" label="SIPO" {...validation} />
                    <Radio option="jiny" name="zpuhraz_radio" className="radio" label="jiným způsobem" {...validation} />
                  </Col>
                </Row>
            </RInput>
          </Col>
          <Col xs={6}>
            <Input field="zpuhraz_sipoc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Číslo SIPO" {...validation}/>

            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Číslo účtu / kód banky">
              <Row>
                <Col xs={7}>
                  <Input type="number" field="zpuhraz_ucetc" className="form-control" {...validation} />
                </Col>
                <Col xs={5}>
                  <Input type="number" field="zpuhraz_bankod" className="form-control" {...validation} />
                </Col>
              </Row>
            </RInput>
          </Col>
        </Row>
        <Row>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Doručování vyúčtování">
                <Row>
                  <Col xs={12}>
                    <Radio option="email" name="dorvyu_radio" className="radio" label="e-mailem" {...validation} />
                    <Radio option="post" name="dorvyu_radio" className="radio" label="poštou (cena dle tarifu)" {...validation} />
                  </Col>
                </Row>
            </RInput>
          </Col>
        </Row>
			</div>
		)
	}
}