import React from 'react'
import {Input, Radio} from '../../Inputs';
import {Input as RInput, Row, Col} from 'react-bootstrap';
import {AppState} from '../../../../AppState';
import {fromJS} from 'immutable';
import {matchRegex,
        notEmpty,
        matchDia,
        cond,
        isPerson,
        isFirm,
        emailBilling,
        isID,
        isActingPerson} from '../../validationHelpers';

export const userValidators = fromJS({
	  uzi_dslemail: [matchRegex('uzi_dslemail', /^$|^.+@.+\.[a-z]{2,}$/, 'Vložte prosím platný e-mail.'),
                cond(emailBilling, notEmpty('uzi_dslemail'))],
    uzi_mobc: [matchRegex('uzi_mobc', /^\+{0,1}[0-9\ ]*$/, 'Vložte prosím platné mobilní číslo.')],
    uzi_obch_firm: [cond(isFirm, matchDia('uzi_obch_firm', /^[a-zA-Z .0-9-]*$/, 'Zadejte prosím platný název.'))],
    uzi_krestni_jmeno: [cond(isPerson, matchDia('uzi_krestni_jmeno', /^[a-zA-Z .]*$/, 'Zadejte prosím platný jméno.'))],
    uzi_prijmeni: [cond(isPerson, matchDia('uzi_prijmeni', /^[a-zA-Z .-]*$/, 'Zadejte prosím platný příjmení.'))],
    uzi_titul: [cond(isPerson, matchRegex('uzi_titul', /^[a-zA-Z .]*$/, 'Zadejte prosím platný titul.'))],
    uzi_ico: [cond(isFirm, matchRegex('uzi_ico', /^[0-9/ ]*$/, 'Zadejte prosím platné číslo.'))],
    uzi_dic: [cond(isFirm, matchRegex('uzi_dic', /^[0-9/ ]*$/, 'Zadejte prosím platné číslo.'))],
    uzi_rc: [cond(isPerson, matchRegex('uzi_rc', /^[0-9/ ]*$/, 'Zadejte prosím platné číslo.')), 
            cond(isPerson, isID('uzi_rc'))],
    uzi_idc: [cond(isPerson, matchRegex('uzi_idc', /^[a-zA-Z0-9]*$/, 'Zadejte prosím platné číslo.'))],
    uzi_telc: [matchRegex('uzi_telc', /^\+{0,1}[0-9\ ]*$/, 'Vložte prosím platné telefonní číslo.')],
    ooj_jmeno: [matchDia('ooj_jmeno', /^[a-zA-Z .-]*$/, 'Zadejte prosím platný jméno.')],
    ooj_rc: [matchRegex('ooj_rc', /^[0-9/ ]*$/, 'Zadejte prosím platné rodné číslo.'), 
              isID('ooj_rc')],
    ooj_idc: [matchRegex('ooj_idc', /^[a-zA-Z0-9]*$/, 'Zadejte prosím platné číslo.'),
              cond(isActingPerson, notEmpty('ooj_idc'))],
    byd_ulice: [matchDia('byd_ulice', /^[a-zA-Z .]*$/, 'Zadejte prosím platný název ulice.')],
    byd_mesto: [matchDia('byd_mesto', /^[a-zA-Z .]*$/, 'Zadejte prosím platný název města.')],
    byd_stat: [matchDia('byd_stat', /^[a-zA-Z ]*$/, 'Zadejte prosím platný název státu.')],
    byd_poc: [matchRegex('byd_poc', /^[0-9]*$/, 'Prosím, zadejte platné číslo domu.')],
    byd_orc: [matchRegex('byd_orc', /^[0-9]*$/, 'Prosím, zadejte platné orientační číslo.')],
    byd_psc: [matchRegex('byd_psc', /^[0-9 ]*$/, 'Zadejte prosím platné PSČ.')],
    ins_dslulice: [matchDia('ins_dslulice', /^[a-zA-Z ]*$/, 'Zadejte prosím platný název ulice.')],
    ins_dslmesto: [matchDia('ins_dslmesto', /^[a-zA-Z ]*$/, 'Zadejte prosím platný název města.')],
    ins_dslpoc: [matchRegex('ins_dslpoc', /^[0-9]*$/, 'Prosím, zadejte platné číslo domu.')],
    ins_dslorc: [matchRegex('ins_dslorc', /^[0-9]*$/, 'Prosím, zadejte platné orientační číslo.')],
    ins_dslpsc: [matchRegex('ins_dslpsc', /^[0-9 ]*$/, 'Zadejte prosím platné PSČ.')],
    dor_dslulice: [matchDia('dor_dslulice', /^[a-zA-Z ]*$/, 'Zadejte prosím platný název ulice.')],
    dor_dslmesto: [matchDia('dor_dslmesto', /^[a-zA-Z ]*$/, 'Zadejte prosím platný název města.')],
    dor_dslpoc: [matchRegex('dor_dslpoc', /^[0-9]*$/, 'Prosím, zadejte platné číslo domu.')],
    dor_dslorc: [matchRegex('dor_dslorc', /^[0-9]*$/, 'Prosím, zadejte platné orientační číslo.')],
    dor_dslpsc: [matchRegex('dor_dslpsc', /^[0-9 ]*$/, 'Zadejte prosím platné PSČ.')]
})

export class UserPart extends React.Component {
	render(){
		const validation = this.props.validation
		return(
			<section className="section">
        <h2>UŽIVATEL</h2>
        <Row>
          <Col xs={6}>

            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
              <Col xs={6}>
                <Radio option="fyz" name="osoba_typ_radio" className="radio" label="Fyzická osoba" {...validation} />
              </Col>
              <Col xs={6}>
                <Radio option="prav" name="osoba_typ_radio" className="radio" label="Právnická osoba" {...validation} />
              </Col>
            </RInput>

            {AppState.value.getIn(['metadata', 'osoba_typ_radio', 'value']) === 'fyz' ?  <div>

                <Input key={1} field="uzi_krestni_jmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Jméno" {...validation}/>

                <Input key={2} field="uzi_prijmeni" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Příjmení" {...validation}/>

                <Input key={3} field="uzi_titul" labelClassName="col-xs-3" wrapperClassName="col-xs-3" label="Titul" {...validation}/>

                <Input key={4} type="tel" field="uzi_rc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Rodné číslo" {...validation}/>

                <Input key={5} field="uzi_idc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Číslo OP/pasu" {...validation}/>

              </div>
            :
              <div> 
                <Input key={6} field="uzi_obch_firm" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Obchodní firma" {...validation}/>

                <Input key={7} field="uzi_ico" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="IČO" {...validation}/>

                <Input key={8} field="uzi_dic" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="DIČ" {...validation}/>
              </div>
            }
          </Col>


          <Col xs={6}>
            <Input type="tel" field="uzi_telc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Telefonní číslo" {...validation}/>

            <Input type="tel" field="uzi_mobc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Mobilní telefon" {...validation} />

            <Input type="email" field="uzi_dslemail" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="E-mail" {...validation} />
          </Col>

        </Row>

        <Row>
          <p><strong className="strong">Osoba oprávněná jednat za Uživatele</strong></p>

          <Col xs={6}>
            <Input field="ooj_jmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Jméno, příjmení" {...validation}/>
          </Col>

          <Col xs={6}>
            <Input type="tel" field="ooj_rc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Rodné číslo" {...validation}/>
            <Input field="ooj_idc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="Číslo OP/pasu" {...validation}/>
          </Col>
        </Row>

        <h3>TRVALÉ BYDLIŠTĚ / MÍSTO PODNIKÁNÍ / SÍDLO PRÁVNICKÉ OSOBY</h3>

        <Row>
          <Col xs={6}>
            <Input field="byd_ulice" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Ulice" {...validation}/>

            <Input field="byd_mesto" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Město" {...validation}/>

            <Input field="byd_stat" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Stát" {...validation}/>
          </Col>

          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Číslo popisné / orientační">
              <Row>
                <Col xs={6}>
                  <Input type="number" field="byd_poc" className="form-control" {...validation}/>
                </Col>
                <Col xs={6}>
                  <Input type="number" field="byd_orc" className="form-control" {...validation}/>
                </Col>
              </Row>
            </RInput>

            <Input type="tel" field="byd_psc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="PSČ" {...validation}/>
          </Col>
        </Row>

        <h3>Adresa instalace</h3>

        <Row>
          <Col xs={6}>
            <Input field="ins_dslulice" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Ulice" {...validation}/>

            <Input field="ins_dslmesto" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Město" {...validation}/>
          </Col>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Číslo popisné / orientační">
              <Row>
                <Col xs={6}>
                  <Input type="number" field="ins_dslpoc" className="form-control" {...validation}/>
                </Col>
                <Col xs={6}>
                  <Input type="number" field="ins_dslorc" className="form-control" {...validation}/>
                </Col>
              </Row>
            </RInput>
            <Input type="tel" field="ins_dslpsc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="PSČ" {...validation}/>
          </Col>
        </Row>

        <h3>ADRESA PRO DORUČOVÁNÍ PÍSEMNOSTÍ (POUZE V ČR)</h3>

        <Row>
          <Col xs={6}>
            <Input field="dor_dslulice" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Ulice" {...validation}/>

            <Input field="dor_dslmesto" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Město" {...validation}/>
          </Col>
          <Col xs={6}>
            <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Číslo popisné / orientační">
              <Row>
                <Col xs={6}>
                  <Input type="number" field="dor_dslpoc" className="form-control" {...validation}/>
                </Col>
                <Col xs={6}>
                  <Input type="number" field="dor_dslorc" className="form-control" {...validation}/>
                </Col>
              </Row>
            </RInput>
            <Input type="tel" field="dor_dslpsc" labelClassName="col-xs-3" wrapperClassName="col-xs-6" label="PSČ" {...validation}/>
          </Col>
        </Row>
      </section>
		)
	}
}