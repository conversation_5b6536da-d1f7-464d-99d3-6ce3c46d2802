import React from 'react';
import {Input, Radio, Checkbox, Text, Select} from './Inputs';
import {AppState} from '../../AppState';
import {logD} from '../../Operations';
import {Input as RInput, Row, Col, Grid, FormControls} from 'react-bootstrap';
import {Navbar as MNavbar} from './Navbar';
import {blurOnEnter, 
        enableField, 
        disableField,
        canConfirm,
        resolvePackages,
        resolvePersonalInfo,
        resolveUserName,
        resolveSignatorName} from './formHelpers'
import {fromJS} from 'immutable';
import {matchRegex, 
        notEmpty, 
        matchDia, 
        validateInputsFactory, 
        cond} from './validationHelpers';
import {UserPart, userValidators} from './parts/contract/UserPart';
import {ServicePart, serviceValidators} from './parts/ServicePart';

import Rx from 'rx';

export class Amendment extends React.Component {

    renderHeader() {
        return (
            <div className="header">
                <span>Dodatek ke Smlouvě č.</span>
                <Input field="zahl_smlouc" className="form-control input--medium input--inline" />
                <span>o poskytování služeb elektronických komunikací ze dne</span>
                <Input field="zahl_datum" className="form-control input--medium input--inline" />
            </div>
        );
    }

    render() {
        return (
            <BaseContract {...this.props} renderHeader={this.renderHeader} />
        );
    }

}

export class Contract extends React.Component {

    renderHeader() {
        return (
            <div className="header">
                <span>Smlouva o poskytování služeb elektronických komunikací č.</span>
                <Input field="zahl_smlouc" className="input--medium form-control input--inline" wrapperClassName="displayInline"/>
            </div>
        );
    }

    render() {
        return (
            <BaseContract {...this.props} renderHeader={this.renderHeader} />
        );
    }
}

class BaseContract extends React.Component {

    constructor(props) {
      super(props);

      this.fieldValidators = fromJS({
        podp_biometria_chb: []
      }).merge(userValidators).merge(serviceValidators)

      this.validateInputs = validateInputsFactory(this.fieldValidators)

      this.validationStream = new Rx.Subject();
      this.subscription = this.validationStream
        .debounce(700)
        .subscribe(this.validateInputs)
    }

    validateConstraints(){
      let state = AppState.value;

      if(state.getIn(['metadata', 'balicek_radio', 'value']) !== 'zakl'){
        state = disableField(state, 'bal_extra')//state.mergeIn(['bal_extra'], a);
        state = disableField(state, 'bal_sport')//state.mergeIn(['bal_sport'], a);
        state = disableField(state, 'bal_hbo')//state.mergeIn(['bal_hbo'], a);
      } else {
        state = enableField(state, 'bal_extra', true);
        state = enableField(state, 'bal_sport', true);
        state = enableField(state, 'bal_hbo', true);
      }

      if(state.getIn(['metadata', 'bal_bas_vaz', 'value']) !== true ||
         state.getIn(['metadata', 'bal_extra', 'value']) !== true){
        state = disableField(state, 'bal_extra_vaz')//state.mergeIn(['bal_extra_vaz'], a);
      } else {
        state = enableField(state, 'bal_extra_vaz', true);
      }

      if(state.getIn(['metadata', 'bal_bas_vaz', 'value']) !== true ||
         state.getIn(['metadata', 'bal_sport', 'value']) !== true){
        state = disableField(state, 'bal_sport_vaz')
      } else {
        state = enableField(state, 'bal_sport_vaz', true);
      }

      if(state.getIn(['metadata', 'bal_bas_vaz', 'value']) !== true ||
         state.getIn(['metadata', 'bal_hbo', 'value']) !== true){
        state = disableField(state, 'bal_hbo_vaz')
      } else {
        state = enableField(state, 'bal_hbo_vaz', true);
      }

      if(state.getIn(['metadata', 'bal_hbo', 'value']) !== true){
        state = disableField(state, 'bal_hbo_go')
      } else {
        state = enableField(state, 'bal_hbo_go', true);
      }

      if(state.getIn(['metadata', 'bal_hbo_vaz', 'value']) !== true ||
         state.getIn(['metadata', 'bal_hbo_go', 'value']) !== true){
        state = disableField(state, 'bal_hbo_go_vaz')
      } else{
        state = enableField(state, 'bal_hbo_go_vaz', true);
      }

      AppState.transact((state_) => {
        return state
      });
    }

    componentWillMount(){
      AppState.transact((state) => {
        return state.updateIn(['formFields'], list => list.clear());
      })
      this.validateConstraints();
      this.validateInputs();
    }

    confirm = () => {
      this.postProcessing()
      this.props.onSubmit()    
    }

    renderButtons(){
      return [{
          title: "Zpět",
          bsStyle: "primary",
          onClick: this.props.onBack,
        },
        {
          title: "OK",
          onClick: this.confirm,
          bsStyle: "success",
          disabled: !canConfirm(this.fieldValidators)
        }]
    }

    postProcessing(){
      let {metadata} = AppState.value;

      //kampan_nazev
      let camps = ''
      if(AppState.value.getIn(['metadata', 'campaigns_info']) != null){
        camps = AppState.value.getIn(['metadata', 'campaigns_info']).filter(({id}) => 
          !!AppState.value.getIn(['metadata', id, 'value'])
        ).map(({text}) => text).join(', ')
        metadata = metadata.setIn(['kampan_nazev', 'value'], camps)
      }

      //uzi_jmeno
      metadata = resolveUserName(metadata)

      //podp_mesto
      metadata = metadata.setIn(['podp_mesto', 'value'], metadata.getIn(['ins_mesto', 'value']))

      //podp_jmeno_uzi
      metadata = resolveSignatorName(metadata)

      //podp_datum
      let d = new Date()
      let date = d.getDate()+'.'+(d.getMonth()+1)+'.'+d.getFullYear()
      metadata = metadata.setIn(['podp_datum', 'value'], date)

      //podp_jmeno_tech
      metadata = metadata.setIn(['podp_jmeno_tech', 'value'], metadata.getIn(['tech_jmeno', 'value']))

      let pdfMetadata = metadata

      //packages
      pdfMetadata = resolvePackages(pdfMetadata)
      //personal info
      pdfMetadata = resolvePersonalInfo(pdfMetadata)

      AppState.transact( (state) => {
        return state.set('pdfMetadata', pdfMetadata)
          .set('metadata', metadata)
      })
    }

    renderCampaignRow({id, text}){
      return (<Checkbox field={id} label={text}/>)
    }

    renderCampaigns(){
      if(AppState.value.getIn(['metadata', 'campaigns_info']) != null ){
        return AppState.value.getIn(['metadata', 'campaigns_info']).map((value) => {
          return this.renderCampaignRow(value)
        })
      }
    }

    renderPackageRow([field, title]){
      return (<RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label={title}>
                <Checkbox field={field} label="Vázaný"/>           
              </RInput>)
    }

    renderPackages(){
      if(AppState.value.hasIn(['metadata', 'products_info'])){
        return AppState.value.getIn(['metadata', 'products_info']).map((value) => {
          return this.renderPackageRow(value)
        })
      }
    }

    render() {
        const validation = {
          validationStream: this.validationStream,
          validateInputs: this.validateInputs
        }

        return (
            <div className="document document-digi-smlouva" onKeyPress={blurOnEnter}>
              <Grid fluid={true}>
              <header className="header">
                <MNavbar buttons={this.renderButtons()} />
              <h1>
                {this.props.renderHeader()}
              </h1>
              </header>
              <form className="form-horizontal">
                
                <UserPart validation={validation} />

                <section className="section">
                  <Row>
                    <h2 className="pull-left" >POSKYTOVANÉ SLUŽBY</h2>
                  </Row>

                  {AppState.value.hasIn(['metadata', 'products_info']) ? 
                  <Row>
                    <Col xs={6}>
                      <Row>
                        {this.renderPackages()}
                      </Row>
                    </Col>
                  </Row>
                  :
                  <div>
                    <Row>
                      <Col xs={6}>
                        <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Balíčky">
                          <Row>
                            <Col xs={6}>
                              <Radio option="mini" name="balicek_radio" className="radio" label="Mini" onChange={this.validateConstraints}/>
                              <Radio option="zakl" name="balicek_radio" className="radio" label="Základní" onChange={this.validateConstraints}/>
                              <Radio option="maxi" name="balicek_radio" className="radio" label="Maxi" onChange={this.validateConstraints}/>
                            </Col>
                            <Col xs={6}>
                              <Checkbox field="bal_bas_vaz" label="Vázaný" onChange={this.validateConstraints}/>
                            </Col>
                          </Row>
                        </RInput>
                      </Col>
                      <Col xs={6}>
                        {/*akce_jmeno*/}
                        <Input field="akce_jmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="NÁZEV AKCE" {...validation}/>
                        {AppState.value.getIn(['metadata', 'campaigns_info']) == null ?
                          <Input field="kampan_nazev" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="KAMPANĚ" {...validation}/>
                        :
                        <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
                          {this.renderCampaigns()}
                        </RInput>}
                      </Col>
                    </Row>
                    {AppState.value.getIn(['metadata', 'balicek_radio', 'value']) === 'zakl' && <Row>
                      <Col xs={6}>
                        <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
                            <Row>
                              <Col xs={6}>
                                <Checkbox field="bal_extra" label="Extra" onChange={this.validateConstraints}/>
                                <Checkbox field="bal_sport" label="Sport" onChange={this.validateConstraints}/>
                                <Checkbox field="bal_hbo" label="HBO" onChange={this.validateConstraints}/>
                              </Col>
                              <Col xs={6}>
                                <Checkbox field="bal_extra_vaz" label="Vázaný" onChange={this.validateConstraints}/>
                                <Checkbox field="bal_sport_vaz" label="Vázaný" onChange={this.validateConstraints}/>
                                <Checkbox field="bal_hbo_vaz" label="Vázaný" onChange={this.validateConstraints}/>
                              </Col>
                            </Row>
                        </RInput>
                      </Col>
                    </Row>
                    }

                    {AppState.value.getIn(['metadata', 'bal_hbo', 'value']) === true && <Row>
                      <Col xs={6}>
                        <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label=" ">
                            <Row>
                              <Col xs={6}>
                                <Checkbox field="bal_hbo_go" label="HBO GO" onChange={this.validateConstraints}/>
                              </Col>
                              <Col xs={6}>
                                <Checkbox field="bal_hbo_go_vaz" label="Vázaný" onChange={this.validateConstraints}/>
                              </Col>
                            </Row>
                        </RInput>
                      </Col>
                    </Row>
                    }
                  </div>}

                  <Row>
                    <Col xs={6}>
                      <Select  
                        field="zarizeni_typ1" 
                        labelClassName="col-xs-3" 
                        wrapperClassName="col-xs-9" 
                        label="První zažízení"/>
                    </Col>
                    <Col xs={6}>
                      <Select 
                        field="zarizeni_typ2" 
                        labelClassName="col-xs-3" 
                        wrapperClassName="col-xs-9" 
                        label="Druhé zařízení"/>
                    </Col>
                  </Row>

                  <ServicePart validation={validation} />
                  <Row>
                    <Col xs={12}>
                      <Checkbox field="podp_biometria_chb" {...validation} label="Souhlasím s podpisem tohoto dokumentu za použití technologie zaznamenávající biometrické údaje podpisu (digitální vlastnoruční podpis)" {...validation}/>
                    </Col>
                  </Row>
                </section>
              </form>
              </Grid>
            </div>

      );
    }

}
