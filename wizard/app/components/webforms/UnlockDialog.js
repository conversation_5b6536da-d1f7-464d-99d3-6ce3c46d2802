import React from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>} from 'react-bootstrap';
import {AppState} from '../../AppState';
import {listDir} from '../../Operations';
import {finalFolder} from '../../Constants';

export default class UnlockDialog extends React.Component {

    clickOutside = (e) => {
        e.stopPropagation();
        e.preventDefault();
        this.cancel();
    }

    confirm = () => {
        let {lockedFields, fieldToUnlock, type} = AppState.value;

        // forms that needs to be unlocked
        const delInd = lockedFields.getIn([fieldToUnlock])
        delInd.forEach((value) => {
            lockedFields.forEach((arr, field) => {
                arr.forEach((val, id) => {
                    if(val === value){
                        lockedFields = lockedFields.deleteIn([field, id])
                    }
                })
            })
        })

        lockedFields.forEach((val, id) => {
            if(val.size < 1){
                lockedFields = lockedFields.deleteIn([id])
            }
        })

        AppState.transact((state) => {
            let a = state
            delInd.forEach((value) => {
                a = a.setIn(['toSign', value, 'done'], 0)
            })
            return a.setIn(['lockedFields'], lockedFields)
        })

        this.cancel()
    }

    cancel = () => {
        AppState.transact((state) => {
            return state.set('showUnlockDialog', false)
                        .set('fieldToUnlock', '')
        })
    }

    render() {
        const msgDialog = this.props.msg.signing.dialog
        return (
            <Modal show={true} onHide={this.cancel} animation={false}>
                <Modal.Body>
                    {msgDialog.editUnlocksForm}
                </Modal.Body>

                <Modal.Footer>
                    <Button className='pull-left' onClick={this.cancel}>{msgDialog.no}</Button>
                    <Button bsStyle="danger" onClick={this.confirm}>{msgDialog.yes}</Button>
                </Modal.Footer>

            </Modal>
        )
    }
}