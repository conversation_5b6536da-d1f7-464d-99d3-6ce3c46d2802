import {AppState} from '../../AppState';
import {notEmpty} from './validationHelpers'

export function blurOnEnter(e){
  if(e.charCode === 13){
    e.target.blur()
    e.preventDefault()
  }
}

export function disableField(state_, field) {
  if(state_.getIn(['metadata', field, 'editable']) !== false){
  	return state_.setIn(['metadata', field, 'value'], '')
  	  .setIn(['disabledFields', field], field)
  } else {
    return state_
  }
}

export function enableField(state_, field, value) {
  let a = state_.deleteIn(['disabledFields', field])
  if(state_.getIn(['metadata', field, 'editable']) !== false){
    if(a.getIn(['metadata', field, 'value']) === ''){
      a = a.setIn(['metadata', field, 'value'], value)
    }
  }
  return a
}

export function canConfirm(fieldValidators){
  if(!fieldValidators){
    return true
  }
  return fieldValidators
  .filter((val, id) => {return AppState.value.getIn(['metadata', id, 'editable']) !== false &&
                (AppState.value.getIn(['metadata', id, 'validated']) ||
                AppState.value.getIn(['metadata', id, 'required']))
              })
  .every((val, id) => AppState.value.getIn(['validation', id, 'valid']) )
}

export const getMaterialValue = (a, id) => {
  let res = a.getIn([id, 'value'])
  if(res === true){
    return 1
  } else if (typeof res === 'string' && !!res) {
    return parseFloat(res.replace(',', '.'))
  } 
  else if (typeof res === 'number'){
    return res
  }
  else {
    return 0
  }
}

//counts how many of given arrays are not empty
export const counter = (arr) => {
  let count = 0
  for(let i = 0; i < arr.length; i++){
    if(notEmpty(arr[i])() === true){
      count += 1
    }
  }
  return count.toString()
}

export const resolvePackages = (metadata) => {
  //resolve packages
  const options = {"zakl": "Základní", 'mini': 'Mini', 'maxi': 'Maxi'}
  let bound = (value) => {return value ? ' - Vázaný' : ' - Nevázaný'}

  let packages = []
  if(metadata.hasIn(['products_info'])){
    metadata.getIn(['products_info']).forEach(([id,text]) => {
      if(metadata.getIn([id, 'value']) === true){
        packages.push(text + bound(metadata.getIn([id, 'value'])))
      }
    })
  } else {
    if(metadata.getIn(['balicek_radio', 'value']) != null){

      // Radiobox dodatek_radio is based on checkbox
      metadata = metadata.setIn(['dodatek_radio', 'value'], metadata.getIn(['bal_bas_vaz', 'value']) ? '24' : 'neu')

      //seting name of main mackage to A) array
      packages.push(options[metadata.getIn(['balicek_radio', 'value'])])

      //check others packages
      if(metadata.getIn(['bal_extra', 'value']) === true){
        packages.push( 'Extra' + bound(metadata.getIn(['bal_extra_vaz', 'value'])) )
      }

      if(metadata.getIn(['bal_sport', 'value']) === true){
        packages.push( 'Sport' + bound(metadata.getIn(['bal_sport_vaz', 'value'])) )
      }

      if(metadata.getIn(['bal_hbo', 'value']) === true){
        packages.push( 'HBO' + bound(metadata.getIn(['bal_hbo_vaz', 'value'])) )
      }

      if(metadata.getIn(['bal_hbo_go', 'value']) === true){
        packages.push( 'HBO GO' + bound(metadata.getIn(['bal_hbo_go_vaz', 'value'])) )
      }
    }
  }
    
  let ids = ['a', 'b', 'c', 'd', 'e']
  for(let i = 0; i < ids.length; i++){
    metadata = metadata.setIn(['progb_'+ids[i]+'_chb', 'value'], i < packages.length ? true : false)
    metadata = metadata.setIn(['progb_'+ids[i], 'value'], i < packages.length ? packages[i] : '')
  }

  return metadata
}

//uzi_rcico, uzi_idc, uzi_titul
export const resolvePersonalInfo = (metadata) => {
  let uzi_rcico = ''
  if(metadata.getIn(['osoba_typ_radio', 'value']) === 'fyz'){
    uzi_rcico = metadata.getIn(['uzi_rc', 'value'])
  } else {
    uzi_rcico = metadata.getIn(['uzi_ico', 'value'])
    metadata = metadata.setIn(['uzi_idc', 'value'],
      metadata.getIn(['uzi_dic', 'value']))
    metadata = metadata.setIn(['uzi_titul', 'value'], '')
  }

  //uzi_rcico
  metadata = metadata.setIn(['uzi_rcico', 'value'], uzi_rcico)

  return metadata
}

export const resolveUserName = (metadata) => {
  let name = []
  if(metadata.getIn(['osoba_typ_radio', 'value']) === 'fyz'){
    if(!!metadata.getIn(['uzi_krestni_jmeno', 'value'])){
      name.push(metadata.getIn(['uzi_krestni_jmeno', 'value']))
    }

    if(!!metadata.getIn(['uzi_prijmeni', 'value'])){
      name.push(metadata.getIn(['uzi_prijmeni', 'value']))
    }
  } else {
    if(!!metadata.getIn(['uzi_obch_firm', 'value'])){
      name.push(metadata.getIn(['uzi_obch_firm', 'value']))
    }
  }

  metadata = metadata.setIn(['uzi_jmeno', 'value'], name.join(' '))
  return metadata
}

export const resolveSignatorName = (metadata) => {
  let podp_jmeno_uzi = notEmpty('ooj_jmeno')() === true ? 
                      metadata.getIn(['ooj_jmeno', 'value'])
                      :
                      metadata.getIn(['uzi_jmeno', 'value'])
    metadata = metadata.setIn(['podp_jmeno_uzi', 'value'], podp_jmeno_uzi)

    return metadata
}