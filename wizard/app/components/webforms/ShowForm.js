import React from 'react';
import {AppState} from '../../AppState';
import UnlockDialog from './UnlockDialog';

export default class ShowForm extends React.Component {

    render() {
        const {type, currentFormShown, showUnlockDialog, toSign} = AppState.value;
        const FormComponent = toSign.getIn([currentFormShown, 'webform']);
        return (
            <div style={{position: 'absolute', left: 0, top: 0, width: "100%"}}>
            	{showUnlockDialog && <UnlockDialog msg={this.props.msg} />}
                <FormComponent {...this.props} />
            </div>
        );
    }
}