import {remove as removeDia} from 'diacritics';
import {AppState} from '../../AppState';
import {fromJS} from 'immutable';

function toString(value){
  if(value == null){
    return ''
  }
  return value
}

export function matchRegex(field, regex, msg = 'Vstup je neplatný pro toto pole.') {
    return () => {
      return toString(AppState.value.getIn(['metadata', field, 'value'])).search(regex) === 0 ? true : msg;
    }
}

export function notEmpty(field, msg = 'Toto pole je povinné.') {
    return () => !!AppState.value.getIn(['metadata', field, 'value']) || msg;
}

export function matchDia(field, regex, msg = 'Vstup je neplatný pro toto pole.') {
    return () => removeDia(toString(AppState.value.getIn(['metadata', field, 'value']))).search(regex) === 0 ? true : msg;
}

export function isTrue(filed, msg = 'Musíte souhlasit s podmínkami'){
    return () => !!AppState.value.getIn(['metadata', field, 'value']) || msg
}

export function cond(cnd, validation) {
  return () => (!cnd()) || validation()
}

// Provide props for nice bootstrap input feedback
export function feedback(validity) {
    if(!validity){
        return {}
    }
    const {error, feedback, valid} = validity;
    if(!feedback){
        return {}
    }
    return {
        hasFeedback: true,
        bsStyle: valid ? 'success' : 'error',
        help: valid ? null : error
    };
}

export function mod11(field, msg = 'Číslo musi být dělitelné 11.'){
    return () => {
      const val = AppState.value.getIn(['metadata', field, 'value']).replace('/', '') || '0'
      return parseInt(val) % 11 === 0 ? true : msg
    } 
}

function gapYear(year){
  return (year % 4 == 0 && year % 100 != 0)
}

export function isID(field, msg = 'Neplatné rodné číslo'){
  return () => {
    let rc = AppState.value.getIn(['metadata', field, 'value']) || ''
    rc = rc.replace('/', '')
    if(!rc){
      return true
    }
    if(rc.length > 10 || rc.length < 9){
      return msg
    }

    let year = parseInt(rc.substring(0,2))
    if(rc.length == 9 && year >= 54){
      return msg
    }
    if (rc.length == 9 || (rc.length == 10 && year >= 54)) {
      year += 1900;
    }else {
      year += 2000;
    }

    let month = parseInt(rc.substring(2,4))
    if (month > 70 && year > 2003) month -= 70
    if (month > 50) month -= 50;
    if (month > 20 && year > 2003) month -= 20
    if (month < 1 || month > 12) return msg

    let day = parseInt(rc.substring(4,6))
    if (day > 50) day -= 50;
    if (day < 1 || day > 31) return msg

    if ((month == 4 || month == 6 || month == 9 ||
         month == 11) && (day > 30)) return msg

    const gYear = gapYear(year)
    if ((month == 2) && (gYear) && (day > 29)) return msg
    if ((month == 2) && (!gYear) && (day > 28)) return msg

    if (rc.length > 9) {
      const rcBase = parseInt(rc.substring(0, 9));
      const controlstring = rc.substring(9, 10);
      let controlnum = 0;
      if (controlstring == "a" || controlstring == "A") {
        controlnum = 10
      }
      else {
        controlnum = parseInt(controlstring);
      }
      const mod = rcBase % 11;
      if (mod != controlnum) {
        if (controlnum == 0 && mod == 10) return true;
        return msg;
      }
    }
  }
}

export function isPerson(){
    return AppState.value.getIn(['metadata', 'osoba_typ_radio', 'value']) === 'fyz' ? true : null
}

export function isActingPerson(){
    return !!AppState.value.getIn(['metadata', 'ooj_jmeno', 'value'])
}

export function isFirm(){
    return AppState.value.getIn(['metadata', 'osoba_typ_radio', 'value']) === 'prav' ? true : null
}

export function isSipo(){
    return AppState.value.getIn(['metadata', 'zpuhraz_radio', 'value']) === 'sipo' ? true : null
}

export function emailBilling(){
    return AppState.value.getIn(['metadata', 'dorvyu_radio', 'value']) === 'email' ? true : null
}

export function validMin(field, index, msg = 'Číslo příliš nízké.'){
  return () => {
    if(AppState.value.hasIn(['metadata', 'materials_info', index, 'min']) && notEmpty(field)() === true){
      return (parseFloat(AppState.value.getIn(['metadata', field, 'value']).replace(',', '.')) 
        >= AppState.value.getIn(['metadata', 'materials_info', index, 'min'])) ? true : msg
    } else {
      return true
    }
  }
}

export function validMax(field, index, msg = 'Číslo příliš vysoké.'){
  return () => {
    if(AppState.value.hasIn(['metadata', 'materials_info', index, 'max']) && notEmpty(field)() === true){
      return (parseFloat(AppState.value.getIn(['metadata', field, 'value']).replace(',', '.')) 
        <= AppState.value.getIn(['metadata', 'materials_info', index, 'max'])) ? true : msg
    } else {
      return true
    }
  }
}

export function validateInputsFactory(fieldValidators) {
    return () => {
      AppState.transact((state) => {
        fieldValidators.forEach((validators, id) => {
          if(!state.getIn(['metadata']).has(id) || state.getIn(['metadata', id, 'editable']) === false ){
            return;
          }

          const {editable, value, required, validated} = state.getIn(['metadata', id])
          let validationResult = null;

          if(required === true){
            let res = notEmpty(id)();
            validationResult = res === true ? null : res
          }

          if(validated === true && 
             validationResult == null
             ){
            // This way, validationResult is either null or an error message
            validationResult = validators
              .map((validator) => validator())
              .skipWhile((result) => result === true)
              .first()
          }

          const valid = validationResult == null
          const feedback = !!value || !valid

          if(required === true || validated === true){
            state = state.setIn(['validation', id], fromJS({
              valid: valid,
              feedback: feedback,
              error: validationResult 
            }))
          }
        })
        return state
      })
    }
    }
