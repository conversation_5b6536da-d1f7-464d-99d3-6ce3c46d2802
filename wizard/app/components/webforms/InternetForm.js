import React from 'react';
import {Input, Radio, Checkbox, Text, Select} from './Inputs';
import {AppState} from '../../AppState';
import {logD} from '../../Operations';
import {Input as RInput, Row, Col, Grid, FormControls} from 'react-bootstrap';
import {Navbar as MNavbar} from './Navbar';
import {blurOnEnter,  
        canConfirm,
        disableField,
        enableField,
        resolvePersonalInfo,
        resolveUserName,
        resolveSignatorName} from './formHelpers'
import {fromJS} from 'immutable';
import {matchRegex, 
        notEmpty, 
        matchDia, 
        validateInputsFactory, 
        cond} from './validationHelpers';
import {UserPart, userValidators} from './parts/internet/UserPart';
import {ServicePart, serviceValidators} from './parts/ServicePart';

import Rx from 'rx';

export class Internet extends React.Component {

  renderHeader() {
      return (
          <div className="header">
              {AppState.value.getIn(['metadata', 'zahl_radio', 'value']) === 'smlouc' ?  <div>
              <span>Dodatek ke Smlouvě č.</span>
              <Input field="zahl_smlouc" className="form-control input--medium input--inline" />
              <span>o poskytování služeb elektronických komunikací ze dne</span>
              <Input field="zahl_datum" className="form-control input--medium input--inline" />
            </div>
          :
            <div> 
              <span>Smlouva o poskytování služeb elektronických komunikací č.</span>
              <Input field="zahl_smlouc" className="input--medium form-control input--inline" wrapperClassName="displayInline"/>
            </div>
          }
          </div>
      );
  }

  render() {
      return (
          <BaseContract {...this.props} renderHeader={this.renderHeader} />
      );
  }

}

class BaseContract extends React.Component {
    validMeta = null
    validFields = null

    constructor(props) {
      super(props);

      this.fieldValidators = fromJS({
        zrizeni_telc: [matchRegex('sluzba_telc', /^\+{0,1}[0-9\ ]*$/, 'Vložte prosím platné telefonní číslo.')],
        podp_dslbiometria_chb: []
      }).merge(userValidators).merge(serviceValidators)

      this.validateInputs = validateInputsFactory(this.fieldValidators)

      this.validationStream = new Rx.Subject();
      this.subscription = this.validationStream
        .debounce(700)
        .subscribe(this.validateInputs)
    }

    componentWillMount(){
      AppState.transact((state) => {
        return state.updateIn(['formFields'], list => list.clear());
      })
      this.validateInputs();
    }

    confirm = () => {
      this.postProcessing()
      this.props.onSubmit()    
    }

    renderButtons(){
      return [{
          title: "Zpět",
          bsStyle: "primary",
          onClick: this.props.onBack,
        },
        {
          title: "OK",
          onClick: this.confirm,
          bsStyle: "success",
          disabled: !canConfirm(this.fieldValidators)
        }]
    }

    validateConstraints(){
      let state = AppState.value;

      if(state.getIn(['metadata', 'zrizeni_radio', 'value']) === 'nova'){
        state = disableField(state, 'zrizeni_servc')
        state = disableField(state, 'zrizeni_telc')
      } else {
        state = enableField(state, 'zrizeni_servc', '');
        state = enableField(state, 'zrizeni_telc', '');
      }

      AppState.transact( (state_) => {
        return state
      }) 
    }

    postProcessing(){
      //mark right checkbox for type
      let {metadata} = AppState.value;

      //uzi_jmeno
      metadata = resolveUserName(metadata)

      //podp_mesto
      metadata = metadata.setIn(['podp_mesto', 'value'], metadata.getIn(['ins_mesto', 'value']))

      //podp_jmeno_uzi
      metadata = resolveSignatorName(metadata)

      //podp_datum
      let d = new Date()
      let date = d.getDate()+'.'+(d.getMonth()+1)+'.'+d.getFullYear()
      metadata = metadata.setIn(['podp_datum', 'value'], date)

      //podp_jmeno_tech
      metadata = metadata.setIn(['podp_jmeno_tech', 'value'], metadata.getIn(['tech_jmeno', 'value']))

      let pdfMetadata = metadata

      pdfMetadata = resolvePersonalInfo(pdfMetadata)

      pdfMetadata = pdfMetadata.setIn(['dsl_radio', 'value'],
         pdfMetadata.getIn(['dslbal_vaz', 'value']) ? '24' : 'neu')

      pdfMetadata = pdfMetadata.setIn(['zahl_smlouc2', 'value'], 
        pdfMetadata.getIn(['zahl_smlouc', 'value']))

      pdfMetadata = pdfMetadata.setIn(['typzarizeni_radio', 'value'], 'typa')

      AppState.transact( (state) => {
        return state.set('pdfMetadata' , pdfMetadata)
          .set('metadata', metadata)
      })
    }

    render() {
        const validation = {
          validationStream: this.validationStream,
          validateInputs: this.validateInputs
        }

        return (
          <div className="document document-digi-smlouva" onKeyPress={blurOnEnter}>
            <Grid fluid={true}>
            <header className="header">
              <MNavbar buttons={this.renderButtons()} />
            <h1>
              {this.props.renderHeader()}
            </h1>
            </header>
            <form className="form-horizontal">

              <UserPart validation={validation} />

              <section className="section">
                <Row>
                  <h2 className="pull-left" >PŘEDMĚT PLNĚNÍ:</h2>
                </Row>
                <Row>
                  <Col xs={6}>
                    <Checkbox 
                      field="dsl_sleva_chb" 
                      label="Chci slevu za balík služeb od DCZ a prodlužuji o 24 měsíců vázanost smlouvy DIGI TV" 
                    />
                  </Col>
                  <Col xs={6}>
                    <Input field="akce_dsljmeno" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="NÁZEV AKCE" {...validation}/>
                  </Col>
                </Row>
                <Row>
                  <Col xs={6}>
                    <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Typ služby">
                      <Row>
                        <Col xs={6}>
                          <Radio option="klasik" name="sluzba_radio" className="radio" label="Klasik"/>
                          <Radio option="klasikplus" name="sluzba_radio" className="radio" label="Klasik Plus"/>
                          <Radio option="turbo" name="sluzba_radio" className="radio" label="Turbo"/>
                          <Radio option="turboplus" name="sluzba_radio" className="radio" label="Turbo Plus"/>
                        </Col>
                        <Col xs={6}>
                          <Checkbox field="dslbal_vaz" label="Vázaný" />
                        </Col>                     
                      </Row>
                    </RInput>
                  </Col>
                  <Col xs={6}>
                    <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Zařízení">
                      <Radio option="vlastni" name="zarizeni_radio" className="radio" label="Vlastní"/>
                      <Radio option="prodej" name="zarizeni_radio" className="radio" label="Prodej"/>
                      <Radio option="najem" name="zarizeni_radio" className="radio" label="Nájem"/>
                    </RInput>
                    <FormControls.Static label="Typ zařízení" labelClassName="col-xs-3" wrapperClassName="col-xs-9" value="Typ A" />
                  </Col>
                </Row>
                <Row>
                  <Col xs={6}>
                    <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Zřízení služby">
                      <Radio option="nova" name="zrizeni_radio" className="radio" label="Nová služba" onChange={this.validateConstraints} />
                      <Radio option="prenos" name="zrizeni_radio" className="radio" label="Přenos od jiného poskytovatele" onChange={this.validateConstraints} />
                    </RInput>
                  </Col>
                  <Col xs={6}>
                    <Input field="zrizeni_servc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Servisní číslo" {...validation}/>
                    <Input field="zrizeni_telc" labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Telefonní číslo" {...validation}/>
                  </Col>
                </Row>
                <Row>
                  <Col xs={6}>
                    <RInput labelClassName="col-xs-3" wrapperClassName="col-xs-9" label="Typ instalace">
                      <Radio option="samoinst" name="instalace_radio" className="radio" label="Samoinstalace (instalace na vlastní riziko a náklady)"/>
                      <Radio option="zakladni" name="instalace_radio" className="radio" label="Základní"/>
                      <Radio option="rozsirena" name="instalace_radio" className="radio" label="Rozšířená (cena za instalace ve výši dle Tarifu)"/>
                    </RInput>
                  </Col>
                </Row>
                <ServicePart validation={validation} />
                <Row>
                  <Col xs={12}>
                    <Checkbox field="podp_dslbiometria_chb" {...validation} label="Souhlasím s podpisem tohoto dokumentu za použití technologie zaznamenávající biometrické údaje podpisu (digitální vlastnoruční podpis)"/>
                  </Col>
                </Row>
              </section>
            </form>
          </Grid>
        </div>
      );
    }

}