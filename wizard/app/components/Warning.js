import React from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';

export default class Warning extends React.Component {

    render() {
        return (
            <Modal show={this.props.show} onHide={this.props.onCancel} animation={false}>
                <Modal.Body>
                    {this.props.msg.exit.warning}
                </Modal.Body>

                <Modal.Footer>
                    <Button className='pull-left' onClick={this.props.onCancel}>{this.props.msg.exit.no}</Button>
                    <Button bsStyle="danger" onClick={this.props.onConfirm}>{this.props.msg.exit.yes}</Button>
                </Modal.Footer>

            </Modal>
        )


    }
}
