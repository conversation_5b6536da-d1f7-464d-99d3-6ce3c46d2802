import React from 'react';
import {Mo<PERSON>, Button, Input as RInput} from 'react-bootstrap';
import {notEmpty} from './webforms/validationHelpers';
import Rx from 'rx';
import {AppState} from '../AppState';
import {fromJS} from 'immutable';
import {Input} from './webforms/Inputs';

export class PhotoDialog extends React.Component {

    validateInputs = () => {
        return !this.state.title
    }

    state = {
        title: '',
        description: ''
    }

    handleChange = (input) => (e) => {
        this.setState({
            [input]: e.target.value
        });
    }

    confirmDialog = () => {
        const {title, description} = this.state
        this.setState({
            title: '',
            description: ''
        });
        this.props.onConfirm({title, description});
    }


    render() {
        const disabled = this.validateInputs()
        const msgPhotos = this.props.msg.photos

        return (
            <Modal show={this.props.show} onHide={this.props.onCancel} animation={false}>
                <Modal.Header>
                    <Modal.Title>{msgPhotos.insertPhotoInfo}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <RInput field="photo_title" type="text" label={msgPhotos.title} onChange={this.handleChange('title')} value={this.state.title} />
                    <RInput type="textarea" label={msgPhotos.description} onChange={this.handleChange('description')} value={this.state.description} />
                </Modal.Body>

                <Modal.Footer>
                    <Button className="pull-left" onClick={this.props.onCancel}>{msgPhotos.cancel}</Button>
                    <Button bsStyle="primary" onClick={this.confirmDialog} disabled={disabled}>{msgPhotos.confirm}</Button>
                </Modal.Footer>
            </Modal>
        )
    }
}

export class DescriptionDialog extends React.Component {

    state = {
        description: this.props.defaultDesc
    }

    handleChange = (e) => {
        this.setState({
            description: e.target.value
        });
    }

    confirmDialog = () => {
        const {description} = this.state
        this.setState({
            description: ''
        });
        this.props.onConfirm(description);
    }

    render() {
        const msgPhotos = this.props.msg.photos
        return (
            <Modal show={this.props.show} onHide={this.props.onCancel}>
                <Modal.Header>
                    <Modal.Title>{msgPhotos.changePhotoDescription}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <RInput type="textarea" label={msgPhotos.description} onChange={this.handleChange} value={this.state.description}/>
                </Modal.Body>

                <Modal.Footer>
                    <Button className="pull-left" onClick={this.props.onCancel}>{msgPhotos.cancel}</Button>
                    <Button bsStyle="primary" onClick={this.confirmDialog}>{msgPhotos.confirm}</Button>
                </Modal.Footer>
            </Modal>
        )
    }
}