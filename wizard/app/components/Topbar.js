import React from 'react';
import {sitePredecessors,
        sendErrorSite,
        fotoDataSite,
        signingProcessSite,
        packagePasswordSite} from '../Constants';
import {AppState} from '../AppState';
import {cancel} from '../Operations';
import Warning from './Warning';
import {fromJS} from 'immutable';
import {Navbar} from './webforms/Navbar';


export function goBack() {
    AppState.transact((state) => {
        const {current_site, previous_site} = state;
        return state.set('current_site',
            current_site === sendErrorSite ? previous_site : sitePredecessors[current_site]
        );
    });
}

// Only displays the top bar with Close button and title
export default class Topbar extends React.Component {

    state = {
        warning: false
    }

    onCancelClick = (event) => {
        if (AppState.value.has('password')) {
            // Password was already entered, we have to warn the user
            this.showWarning();
        } else {
            this.exitWizard();
        }
    }

    showWarning() {
        this.setState({
            warning: true
        });
    }

    hideWarning() {
        this.setState({
            warning: false
        });
    }

    exitWizard() {
        cancel();
    }

    sendError = (event) => {
        const {current_site} = AppState.value;
        if (current_site !== sendErrorSite) {
            AppState.transact((state) => {
                return state.merge(fromJS({
                    previous_site: current_site,
                    current_site: sendErrorSite,
                }));
            });
        }
    }

    shouldRenderBackButton() {
        const {current_site} = AppState.value;
        return current_site !== sitePredecessors[current_site];
    }

    shouldRenderFotoButton() {
        return AppState.value.get('current_site') === signingProcessSite;
    }

    shouldRenderErrorButton() {
        return AppState.value.get('current_site') === signingProcessSite || 
                AppState.value.get('current_site') === packagePasswordSite;
    }

    render() {

        let buttons = []
        const msgTopbar = this.props.msg.topbar

        if(this.shouldRenderBackButton()){
            buttons.push({
                title: msgTopbar.back,
                onClick: goBack,
                bsStyle: "primary"
            })
        }

        if(this.shouldRenderErrorButton()){
            buttons.push({
                title: msgTopbar.returnWithError,
                onClick: this.sendError,
                bsStyle: "warning"
            })
        }

        buttons.push({
            title: msgTopbar.close,
            onClick: this.onCancelClick,
            bsStyle: "danger",
            className: "navigation-item-logout"
        })
        return (
            <div>
                <Warning msg={this.props.msg} show={this.state.warning} onCancel={(e) => this.hideWarning()} onConfirm={(e) => this.exitWizard()} />

                <div id="topBar">
                </div>
                <Navbar
                    title={this.props.title}
                    buttons={buttons}
                />
            </div>
        );
    }
}
