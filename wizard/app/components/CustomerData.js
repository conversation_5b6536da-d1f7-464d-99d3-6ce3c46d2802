var React = require('react');
import {AppState} from '../AppState';
import {Map} from 'immutable';

// This component is currently not shown anywhere, but it is easy to wire it back,
// so it remains here until code will be adjusted to presentation purposes

module.exports = React.createClass({

    getInitialState: function() {
        const {metadata} = AppState.value;
        return Map(metadata.valueSeq().map(({editable, key, value}) => {
            return [key, (value == null ? '' : value)];
        })).toJS();
    },

    changeField: function(field) {
        return (e) => {
            let change = {};
            change[field] = e.target.value;
            this.setState(change);
        };
    },

    blurOnEnter: function(field) {
        return (e) => {
            if (e.keyCode === 13) {
                React.findDOMNode(this.refs[field]).blur();
            }
        };
    },

    renderEditableRow: function({text, key}) {
        return (
            <div className="infoRow">
                <div className="infoLabel">{text}</div>
                <div className="infoValue">
                    <input onChange={this.changeField(key)} onKeyUp={this.blurOnEnter(key)} ref={key} type="text" value={this.state[key]} />
                </div>
            </div>
        );
    },

    renderFixedRow: function({text, value}) {
        return (
            <div className="infoRow">
                <div className="infoLabel">{text}</div>
                <div className="infoValue">{value}</div>
            </div>
        );
    },

    renderRow: function(data) {
        const {visible, editable} = data;
        if (visible) {
            if (editable) {
                return this.renderEditableRow(data);
            } else {
                return this.renderFixedRow(data);
            }
        }
    },

    nextPage: function() {
        // Check if the password has already been entered
        AppState.transact((state) => {
            return state.update('metadata', ({editable, value, key, ...rest}) => ({
                editable,
                value: (editable ? this.state[key] : value),
                key,
                ...rest
            })).set('current_site', AppState.value.hasKey('password') ? 'signing_process' : 'package_password');
        });
    },

    render: function() {
        return (
            <div id="siteContent">
                <div id="mainContent">
                    <div className="recordRow">
                        <div className="recordItem" >
                            <h1>Confirm customer data</h1>
                            {AppState.value.get('metadata').map(this.renderRow)}
                        </div>
                    </div>
                    <p className="recordItemActions" style={{paddingTop: '20px'}} >
                        <input onClick={this.nextPage} type="button" value="Next"/>
                    </p>
                    <br style={{clear: 'left'}} />
                </div>
            </div>
        );
    }
});
