import React from 'react';
import {<PERSON>rid, <PERSON>, Col, Button} from 'react-bootstrap';
import {remove as removeDia} from 'diacritics';
import {scanFolder, finalFolder} from '../Constants';
import {AppState} from '../AppState';
import {listDir,
        jpgToPdf,
        saveText,
        zip,
        send,
        cancel,
        logD,
        scan,
        formPdf,
        signPdf,
        preview,
        createFolder,
        copy,
        deleteFolder,
        deleteFile} from '../Operations';
import ShowForm from './webforms/ShowForm.js';
import {goBack} from './Topbar.js';
import {fromJS} from 'immutable';

import {PhotoDialog, DescriptionDialog} from './PhotoDialog';

const statusIcons = [
    'process-not-done',
    'process-done',
    'process-cancel',
    ''
];

// Screen for signing process - scans and signatures
export default class PhotoData extends React.Component {

    state = {
        photoDialog: false,
        descriptionDialog: false,
        defaultDescription: ''
    }

    currentPhoto = {
        type: '',
        id: ''
    }

    renderButton(isEnabled, onClick, text, className) {
        return <input className={className + ' photoButton ' + (isEnabled ? '' : ' disabled')} onClick={onClick} type='button' value={text}  />;
    }

    addCustomPhoto = () => {
        this.currentPhoto = {
            type: 'new',
            id: ''
        };
        this.showPhotoDialog();
    }

    showPhotoDialog() {
        this.setState({
            photoDialog: true
        });
    }

    hidePhotoDialog() {
        this.setState({
            photoDialog: false
        });
    }
    showDescriptionDialog() {
        this.setState({
            descriptionDialog: true
        });
    }deleteF

    hideDescriptionDialog() {
        this.setState({
            descriptionDialog: false
        });
    }

    newPhotoRecord = (title ,description, folder, filePath) => {
        AppState.transact((state) => {
            const data = fromJS(
                {
                    text: title,
                    description,
                    folder,
                    done: 1,
                    filePath
                });

            return state.updateIn(['metadata' ,'scans_info', 'custom'], list => list.push(data));
        });
    }

    deleteOldFolder(type, id){
        const oldFolder = AppState.value.getIn(['metadata', 'scans_info', type, id, 'folder']);

        console.log("delete folder", scanFolder+'/'+oldFolder)
        if(type !== 'new' && oldFolder != null){
            deleteFolder(scanFolder+'/'+oldFolder);
            AppState.transact((state) => {
                return state.setIn(['metadata', 'scans_info', type, id, 'done'], 0);
            });
        }
    }

    deleteOldFile = (type, id) => {
        const oldFile = AppState.value.getIn(['metadata', 'scans_info', type, id, 'filePath']);

        if(type !== 'new' && oldFile != null){
            deleteFile(oldFile);
            AppState.transact((state) => {
                return state.setIn(['metadata', 'scans_info', type, id, 'done'], 0);
            });
        }
    }

    deleteId = (type ,id) => {
        this.deleteOldFolder(type, id);

        AppState.transact((state) => {
            return state.updateIn(['metadata', 'scans_info', type], list => list.delete(id));
        });
    }

    descriptionDialog = (type, id) => {
        this.currentPhoto = {
            type: type,
            id: id
        };
        this.setState({
            defaultDescription: AppState.value.getIn(['metadata', 'scans_info', type, id, 'description'])
        });

        this.showDescriptionDialog();
    }

    confirmDescriptionDialog = (desc) => {
        const {type, id} = this.currentPhoto;
        const folderName = AppState.value.getIn(['metadata', 'scans_info', type, id, 'folder']);
        if(folderName != null){
            saveText(scanFolder+'/'+folderName + '/description.txt', desc);
        }
        AppState.transact((state) => {
            return state.setIn(['metadata', 'scans_info', type, id, 'description'], desc);
        });

        this.hideDescriptionDialog();
    }

    getUniqueFolderName = (title) => {
        let folderName = removeDia(title);
        folderName = folderName.replace(/[^a-z0-9]/gi, '_');
        //folderName = folderName;

        const {scans_info} = AppState.value.getIn(['metadata']);
        while(!scans_info.every((type) => {return type.every(({folder}) => {return folder !== folderName})})){
            folderName += '_';
        }

        return folderName;
    }

    okEnabled = () => {
        const {scans_info} = AppState.value.getIn(['metadata']);
        return scans_info.getIn(['mandatory']).every(({done}) => done)
    }

    done = () => {
        if(this.okEnabled()){
            goBack()
        }
    }

    confirmPhotoDialog = (s) => {
        let {type, id} = this.currentPhoto;

        //create safe name for folder
        let folderName = this.getUniqueFolderName(s.title);

        createFolder(scanFolder+'/'+folderName)
        AppState.transact((state) => {
            return state.setIn(['metadata', 'scans_info', type, id, 'folder'], folderName)
        })

        this.takePhoto(s)
    }
    

    takePhoto = (s) => {
        //type and index of current photo
        let {type, id} = this.currentPhoto;

        let folderName = AppState.value.getIn(['metadata', 'scans_info', type, id, 'folder'])

        scan(scanFolder+'/'+folderName).then((result) => {
            if (result !== 'cancel') {
                //delete old folder
                //this.deleteOldFolder(type, id);
                //delete old file
                this.deleteOldFile(type, id)

                //create description file
                //copy('description.txt', folderName);// TODO delete this in production
                saveText(folderName + '/description.txt', s.description);

                if(type === 'new') {
                    this.newPhotoRecord(s.title, s.description, folderName, result);
                } else {
                    AppState.transact((state) => {
                        return state.mergeIn(['metadata', 'scans_info', type, id], 
                            fromJS({folder: folderName, 
                                    done: 1, 
                                    filePath: result}))
                    });
                }
            } else if(type === 'new'){
                deleteFolder(scanFolder+'/'+folderName)
            }
            //listDir(scanFolder)
        });
        this.hidePhotoDialog();
    }

    scanSelectedId = (type ,id) => {
        this.currentPhoto = {
            type: type,
            id: id
        };

        const data = {
            title: AppState.value.getIn(['metadata', 'scans_info', type, id, 'text']),
            description: AppState.value.getIn(['metadata', 'scans_info', type, id, 'description'])
        }

        this.takePhoto(data);
    }

    renderStatusIcon(statusIndex) {
        return <div className={"image-holder "+statusIcons[statusIndex.status]} onClick={statusIndex.onClick}> </div>
    }

    renderEmbedButtonsInAction(action, description, statusIndex) {
        return (
            <Row>
                <Col xs={4}>{this.renderButton(true, action.onClick, action.text)}</Col>
                <Col xs={7} onClick={description.onClick}><div className="description-holder"><p>{description.description}</p></div></Col>
                <Col xs={1} className="photoTable-center">{this.renderStatusIcon(statusIndex)}</Col>
            </Row>
        );
    }

    renderScansAction(scan, description, statusIndex) {
        return this.renderEmbedButtonsInAction(scan, description, statusIndex);
    }

    renderMandatoryScansAction(items){
        return items.map(({text, description, done}, index) =>
            this.renderScansAction(
                {
                    onClick: () =>  this.scanSelectedId('mandatory' ,index),
                    text
                },
                {
                    description,
                    onClick: () => {}
                },
                {
                    status : done
                }
            )
        );
    }

    renderOptionalScansAction(items){
        return items.map(({text, description, done}, index) =>
            this.renderScansAction(
                {
                    onClick: () =>  this.scanSelectedId('optional', index),
                    text
                },
                {
                    description,
                    onClick: () => {}
                },
                {
                    status : done === 0 ? 3 : done
                }
            )
        );
    }

    renderCustomScansAction(items){
        return items.map(({text, description, done}, index) =>
            this.renderScansAction(
                {
                    onClick: () =>  this.scanSelectedId('custom', index),
                    text
                },
                {
                    description,
                    onClick: () => this.descriptionDialog('custom', index)
                },
                {
                    onClick: () =>  this.deleteId('custom', index),
                    status : 2
                }
            )
        );
    }

    renderTableWrap(children) {
        return (
            <Grid className='photoTable'>
                {children}
            </Grid>
        );
    }

    renderScans() {
        const {mandatory, optional, custom} = AppState.value.getIn(['metadata', 'scans_info']);
        const msgPhotos = this.props.msg.photos
        return  <div>
            {mandatory.size > 0 && <div><h2>{msgPhotos.mandatory}</h2>{this.renderTableWrap(this.renderMandatoryScansAction(mandatory))}</div>}
            {optional.size > 0 && <div><h2>{msgPhotos.optional}</h2>{this.renderTableWrap(this.renderOptionalScansAction(optional))}</div>}
            {custom.size > 0 && <div><h2>{msgPhotos.custom}</h2>{this.renderTableWrap(this.renderCustomScansAction(custom))}</div>}
        </div>
    }

    render() {
        const {showForm, currentFormShown} = AppState.value;
        return (
            <div>
                {this.state.photoDialog && <PhotoDialog
                    msg={this.props.msg}
                    show={this.state.photoDialog}
                    onCancel={(e) => this.hidePhotoDialog()}
                    onConfirm={(e) => this.confirmPhotoDialog(e)}
                    />
                }
                {this.state.descriptionDialog && <DescriptionDialog
                    msg={this.props.msg}
                    defaultDesc={this.state.defaultDescription}
                    show={this.state.descriptionDialog}
                    onCancel={(e) => this.hideDescriptionDialog()}
                    onConfirm={(e) => this.confirmDescriptionDialog(e)}
                />}
                <div id="siteContent">
                    <div id="mainContent" className="photoContainer" style={{padding: '20px'}}>
                        {this.renderScans()}
                        <p className="recordItemActions" style={{padding: '0'}}>
                            {this.renderButton(true , this.addCustomPhoto, this.props.msg.photos.addPhoto)}
                            {this.renderButton(this.okEnabled(), this.done, this.props.msg.photos.ok, 'pull-right greenColor') }
                            <br style={{clear: 'left'}} />
                        </p>
                    </div>
                </div>
            </div>
        );
    }
}
