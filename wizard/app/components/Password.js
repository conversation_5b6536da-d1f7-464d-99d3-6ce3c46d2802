import React from 'react';
import {unzipTo, signingProcessSite, finalFolder, scanFolder} from '../Constants';
import {AppState} from '../AppState';
import {formTypes} from '../FormTypes';
import {unZip, createFolder} from '../Operations';
import {fromJS} from 'immutable';

// Screen where the password for zip is requested
// This also decides, which documents are to sign, and how many scans have to be done
export default class Password extends React.Component {

    state = {
        inputVal: ''
    }

    componentDidMount() {
        this.tryUnzip(false);
    }

    handleInputChange = (event) => {
        // Only for making the input work in reactjs way
        this.setState({
            inputVal: event.target.value
        });
    }

    handleSubmit = (event) => {
        this.tryUnzip(true);
        event.preventDefault();
    }

    tryUnzip = (showAlert) => {
        // Currently, this supposes that 'document.zip' is the zip file which
        // contains the documents for client - this should be discussed
        if (unZip('document.zip', unzipTo, this.state.inputVal)) {
            // Unzips the zip into directory unzipTo
            const toSign = [];
            
            AppState.value.getIn(['metadata', 'documents_info']).forEach(({title, file, required}) => {
                console.log("title: ",title)
                toSign.push({
                    title: title,
                    //webform: formTypes[type]['webform'],
                    required: required,
                    path: unzipTo + '/' + file,
                    name: file,//formTypes[type]['pdf'],
                    done: 0 
                })
            })

            // Redirect
            AppState.transact((state) => {
                const newValues = fromJS({
                    password: this.state.inputVal,
                    current_site: signingProcessSite,
                    toSign,
                    //scans: AppState.value.get('scans_info')
                });
                return state.merge(newValues);
            });
    
            let scans = AppState.value.getIn(['metadata', 'scans_info'])
            scans.forEach((val, type) => {
                val.forEach(({folder}, id) => {
                    createFolder(scanFolder+'/'+folder)
                    scans = scans.setIn([type, id, 'done'], 0)
                })
            })
            scans = scans.setIn(['custom'], fromJS([]))
            AppState.transact((state) => {
                return state.setIn(['metadata', 'scans_info'], scans)
            })

        } else if (showAlert) {
            alert('Wrong password !');
        }
    }

    render() {
        return (
            <div id="siteContent">
                <div id="mainContent">
                    <div id="newContactForm" className="centerElement" title="New contract">
                        <form onSubmit={this.handleSubmit} className="newRecordForm" name="passwordForm" >
                            <div className="dialogInput dialogInput-password" style={{paddingTop: '50px'}}>
                                <label htmlFor="password">{this.props.msg.password.insertPassword}</label>
                                <input
                                    className="text ui-widget-content ui-corner-all"
                                    id="password"
                                    name="password"
                                    onChange={this.handleInputChange}
                                    type="password"
                                    value={this.state.inputVal} />
                            </div>


                            <p className="recordItemActions recordItemActions-password">
                                <input name="ok" onClick={this.tryUnzip} style={{margin: '0 auto', width: '100px', float: 'none'}} type="button" value="OK" />
                            </p>
                        </form>

                    </div>
                </div>
            </div>
        );
    }
}
