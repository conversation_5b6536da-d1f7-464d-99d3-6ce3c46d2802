var React = require('react');


// This should be a generic component, which gets a Map in props and displays it
// in key - value form
module.exports = React.createClass({

    renderInnerData: function() {
        var transformed = [];
        for (var key in this.props.data) {
            transformed.push(this.renderRow(key, this.props.data[key]));
        }
        return transformed;
    },

    renderRow: function(key, value) {
        return (
            <div className="infoRow">
                <div className="infoLabel">{key}</div>
                <div className="infoValue">{value}</div>
                <br style={{clear: 'left'}}/>
            </div>
        );
    },

    render: function() {
        return (
            <div className="recordRow">
                <div className="recordItem" >
                    <h1>{this.props.title}</h1>
                    {this.renderInnerData()}
                </div>
            </div>
        );
    }
});
