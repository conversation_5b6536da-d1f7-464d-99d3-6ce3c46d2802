import React from 'react';
import {finalFolder} from '../Constants';
import {AppState} from '../AppState';
import {saveText, zip, send, cancel} from '../Operations';
import {fromJS} from 'immutable';
import {Grid, Input as RInput, Row, Col} from 'react-bootstrap';

export default class SendError extends React.Component {

    state = {
        'errorCode': null,
        'textVal': ''
    }

    handleSubmit = (event) => {
        event.preventDefault();
        React.findDOMNode(this.refs.errorInput).blur();
    }

    isDisabled = () => {
        return this.state.errorCode != null ? "" : " disabled"
    }

    send = (event) => {
        if(!!this.isDisabled()){
            return
        }
        const {metadata} = AppState.value;
        // Add code and text to metadata, and save it to metadata.json
        saveText(finalFolder + '/metadata.json', JSON.stringify(metadata.set(
            'error',
            fromJS({
                'code': this.state.errorCode,
                'text': this.state.textVal
            })
        ).toJS()));

        const zipName = 'ErrorDocument.zip';
        zip(zipName, finalFolder);
        send(zipName).then((result) => {
            alert('Error package sent !');
            cancel();
        });
    }

    onCodeClick = (type, text) => {
        return (event) => {
            this.setState({
                'errorCode': type,
                'textVal': text
            });
        };
    }

    renderErrorCode({type, text}) {
        return (
            <Col xs={6}>
                <RInput 
                    name="error"
                    type="radio" 
                    id={type} 
                    onChange={this.onCodeClick(type, text)} 
                    label={text}/>
            </Col>
        );
    }

    render() {
        return (
            <div id="siteContent">
                <div id="mainContent">
                    <div id="newContactForm">
                    <Grid fluid={true} className="errorFormHolder">
                        <form className="newRecordForm form-horizontal" name="errorMessageForm" onSubmit={this.handleSubmit} >
                            <Row>
                                {AppState.value.getIn(['metadata', 'error_info']).map((code) => this.renderErrorCode(code))}
                            </Row>
                            <p className="recordItemActions">
                                <input className={"errorMessageForm-button pull-right greenColor"+this.isDisabled()} name="ok" onClick={this.send} type="button" value={this.props.msg.error.send}/>
                                <br style={{clear: 'right'}} />
                            </p>
                        </form>
                    </Grid>
                </div>
                </div>
            </div>
        );
    }
}
