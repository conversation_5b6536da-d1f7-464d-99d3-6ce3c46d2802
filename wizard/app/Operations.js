var Promise = require('bluebird');

let idToResolvers = {};
let lastId = 0;

function getUniqueId() {
    return lastId++;
}

function createPromise(id) {
    return new Promise(function (resolve, reject) {
        idToResolvers[id] = resolve;
    });
}

// This one has to be in global namespace, because the anasoft app will call it
window.resolvePromise = function(json) {
    var params = JSON.parse(json);
    var result = 'parameter' in params ? params.parameter : '';
    idToResolvers[params.id](result);
};


function promisifyFn(fn, params) {
    var id = getUniqueId();
    var promise = createPromise(id);
    params.call = 'resolvePromise';
    params.parameter = {'id': id};
    fn(params);
    return promise;
}

// Redefine functions provided by anasoft API
// Async functions now return Promise

module.exports.scan = function(folder) {
    return promisifyFn(window.scan, {'folder': folder});
};

// TODO Check if <PERSON> created also 'signPdf'. Drop the French equivalent if possible.

module.exports.signPdf = function(path, savePath, enableCheckbox) {
    return promisifyFn(window.signePdf, {'path': path, 'savePath': savePath, 'enableCheckbox': enableCheckbox});
};

module.exports.getQRcode = function() {
    return promisifyFn(window.getQRcode, {});
};

module.exports.send = function(file) {
    return promisifyFn(window.send, {'file': file});
};

module.exports.preview = function(path) {
    return window.preview({'path': path});
};

module.exports.unZip = function(file, path, password) {
    return window.unZip({'file': file, 'path': path, 'password': password});
};

module.exports.zip = function(file, folder, password) {
    return window.zip({'file': file, 'folder': folder, 'password': password});
};

module.exports.copy = function(file, folder) {
    return window.copy({'file': file, 'folder': folder});
};

module.exports.move = function(file, folder) {
    return window.move({'file': file, 'folder': folder});
};

module.exports.deleteFile = function(file) {
    return window.deleteFile({'file': file});
};

module.exports.deleteFolder = function(folder) {
    return window.deleteFolder({'folder': folder});
};

module.exports.saveText = function(file, text) {
    return window.saveText({'file': file, 'text': text});
};

module.exports.cancel = function() {
    return window.cancel({});
};

module.exports.listDir = function(folder) {
    return window.listDir({'folder': folder});
};

module.exports.createFolder = function(folder) {
    return window.createFolder({'folder': folder});
};

module.exports.formPdf = function(file, data, csv) {
    return window.formPdf({'file': file, 'data': data, 'csv': csv});
};

module.exports.jpgToPdf = function(file, list) {
    return window.jpgToPdf({'file': file, 'list': list});
};

module.exports.loadFile = function(file) {
    return window.loadFile({'file' : file});
};

module.exports.setLogLevel = function(level) {
    return window.setLogLevel(level);
};

module.exports.logD = function(message) {
    return window.logD(message);
};

module.exports.logE = function(message) {
    return window.logE(message);
};

module.exports.alert = function(message) {
    return window.alert(message);
};

module.exports.alertD = function(message) {
    return window.alertD(message);
};
