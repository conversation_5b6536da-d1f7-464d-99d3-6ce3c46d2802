import CustomerData from './components/CustomerData';
import Password from './components/Password';
import SigningProcess from './components/SigningProcess';
import SendError from './components/SendError';
import PhotoData from './components/PhotoData';
import {customerDataSite, packagePasswordSite, signingProcessSite, sendErrorSite, photoDataSite} from './Constants';

export default {
	[customerDataSite]: {
		'handler': CustomerData,
		'title': 'Customer information'
	},
	[packagePasswordSite]: {
		'handler': Password,
		'title': 'Package protected'
	},
	[signingProcessSite]: {
		'handler': SigningProcess,
		'title': 'Wizard'
	},
	[sendErrorSite] : {
		'handler': SendError,
		'title': '<PERSON><PERSON><PERSON><PERSON>t s chybou'
	},
	[photoDataSite] : {
		'handler': PhotoData,
		'title': 'Fotoevidence'
	}
};
