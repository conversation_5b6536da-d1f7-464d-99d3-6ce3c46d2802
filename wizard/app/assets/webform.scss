$color: #222;

@mixin clearfix {
  &::before,
  &::after {
    content: ' ';
    display: table;
  }
  &::after {
    clear: both;
  }
}

* {
  box-sizing: border-box;
}

.document-digi-smlouva {
  color: $color;
  font-family: sans-serif;
  font-size: 14px;
  padding: 2em;
  background-color: white;

  .header {
    h1 {
      padding-top:30px;
      font-size: 2em;
      margin-top: 0.67em;
      margin-bottom: 0.67em;
      font-weight: bold;
      text-align: center;
    }
  }

  .input {
    &--small {
      width: 100px;
    }

    &--medium {
      width: 170px;
    }
    &--xmedium {
      width: 200px;
    }
    &--large {
      width: 300px;
    }
    &--xlarge {
      width: 450px;
    }

    &--inline {
      display: inline;
      margin :0 5px;
    }
  
  }

  .input-slash {
    padding: 0 .5rem;
  }

  .span-center {
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
  }

  .label {
    max-width: 200px;
    min-width: 40px;
    vertical-align: super;
    display: inline-block;
    color: black;
    font-size:100%;

    &::after {
      content: ':';
    }
    &--full {
      width: 15%;
    }
    &--without-colon {
      &::after {
        content: '';
      }
    }
  }

  .sectionRow {
    /*@include clearfix;*/
    margin: 1rem 0;

    /*.strong {
      float: left;
    }*/
  }

  strong.strong {
    height: 40px;
    line-height: 40px;
    vertical-align: middle;
    display: inline-block;
    margin-right: 1em;
    min-width: 150px;

    &::after {
      content: ':';
    }
  }

  .item {
    float: left;

  }

  .item + .item  {
    margin-left: .5rem;
  }

  .pull-left {
    float: left;
  }

  .pull-right {
    float: right;
  }

  h2 {
    font-size: 1.3em;
    text-transform: uppercase;
    text-decoration: underline;

    &::after {
      content: ':';
    }
  }

  h3 {
    text-transform: uppercase;

    small {
      text-transform: none;
    }
  }

  .control-label {
    padding: 7px 0 0 0;
    font-weight: 700
  }

  .small-col {
    padding: 0;
  }

  .form-group {
    margin-left: 0;
    margin-right: 0;
  }

  div.displayInline{
    display:inline;
  } 

  .header .form-group{
    display:inline;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
  }

}


.document-digi-formular-provedeni {
  .header {
    h1 {
      font-size: 2em;
      margin-top: 0.67em;
      margin-bottom: 0.67em;
      font-weight: bold;
      text-align: center;
      clear: both;
    }

    &__company-info {
      width: 20%;
      float: left;
    }
  }

  .logo {
    max-width: 200px;
    width: 20%;
    margin-right: 10%;
    float: left;
  }

  /*.input {
    background: #fff;
    border: 0;
    color: $color;
    padding: .25em;
    font-size: 1.5rem;
    border-radius: 0;
    border-bottom: 1px solid rgba(0,0,0,0);
    position: relative;

    &:focus {
      outline: none;
      border-color: #999;
    }

    &--small {
      width: 100px;
    }

    &--medium {
      width: 170px;
    }
    &--xmedium {
      width: 200px;
    }
    &--large {
      width: 300px;
    }
    &--xlarge {
      width: 450px;
    }
    &--full {
      width: 100%;
    }

    &--width-description {
      &::before {
        content: attr(title);
        position: absolute;
        top: -15px;
      }
    }
  }*/
  .column-labels {
    margin-left: 230px;
    &__label {
      width: 100px;
      display: inline-block;
      text-align: center;
      font-style: italic;
    }
  }
  .radio {
    float: left;
    margin-right: .5rem;

    + label {
      display: inline-block;
      max-width: 500px;
    }
  }
  .input-slash {
    padding: 0 .5rem;
  }

  .span-center {
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
  }

  .label {
    max-width: 200px;
    min-width: 40px;
    vertical-align: super;
    display: inline-block;

    &::after {
      content: ':';
    }

    &--without-width {
      max-width: 100%;
    }
    &--fixed-width {
      width: 150px;
    }
    &--fixed-width-large {
      width: 230px;
      max-width: 230px;
    }
    &--without-colon {
      &::after {
        content: '';
      }
    }
    &--block {
      display: block;
    }
  }

  /*.row {
    background-color: #e0f3f7;
    padding: .5rem;
    @include clearfix;
    margin: 1rem 0;

    .strong {
      float: left;
    }
  }*/
  .item-row {
    @include clearfix;
    clear: both;
  }
  .one-fifth {
    width: 19%;
  }
  .half {
    width: 50%;
    float: left;

    &:first-child {
      padding-right: 15px;
    }
    &:last-child {
      padding-left: 15px;
    }
  }

  strong.strong {
    height: 40px;
    line-height: 40px;
    vertical-align: middle;
    display: inline-block;
    margin-right: 1em;
    min-width: 150px;

    &::after {
      content: ':';
    }
  }

  .item {
    float: left;
    &--without-float {
      float: none;
    }
  }

  .item + .item  {
    margin-left: .5rem;
  }

  .item + .item.item--without-margin {
    margin-left: 0;
  }

  .pull-left {
    float: left;
  }

  .pull-right {
    float: right;
  }

  h2 {
    font-size: 1.3em;
    text-transform: uppercase;
    text-decoration: underline;

    &::after {
      content: ':';
    }
  }

  h3 {
    text-transform: uppercase;
    clear: both;

    small {
      text-transform: none;
    }
    &.margin {
      margin-top: 15px;
    }
  }
  .clear {
    @include clearfix;
  }

}

.document-digi-provedeni {
  color: $color;
  font-family: sans-serif;
  font-size: 14px;
  padding: 2em;
  background-color: white;

  .header {
    h1 {
      padding-top:30px;
      font-size: 2em;
      margin-top: 0.67em;
      margin-bottom: 0.67em;
      font-weight: bold;
      text-align: center;
    }
  }

  .input {
    &--small {
      width: 100px;
    }

    &--medium {
      width: 170px;
    }
    &--xmedium {
      width: 200px;
    }
    &--large {
      width: 300px;
    }
    &--xlarge {
      width: 450px;
    }

    &--inline {
      display: inline;
      margin :0 5px;
    }
  
  }

  .input-slash {
    padding: 0 .5rem;
  }

  .span-center {
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
  }

  .label {
    max-width: 200px;
    min-width: 40px;
    vertical-align: super;
    display: inline-block;
    color: black;
    font-size:100%;

    &::after {
      content: ':';
    }
    &--full {
      width: 15%;
    }
    &--without-colon {
      &::after {
        content: '';
      }
    }
  }

  .sectionRow {
    /*@include clearfix;*/
    margin: 1rem 0;

    /*.strong {
      float: left;
    }*/
  }

  strong.strong {
    height: 40px;
    line-height: 40px;
    vertical-align: middle;
    display: inline-block;
    margin-right: 1em;
    min-width: 150px;

    /*&::after {
      content: ':';
    }*/
  }

  .item {
    float: left;

  }

  .item + .item  {
    margin-left: .5rem;
  }

  .pull-left {
    float: left;
  }

  .pull-right {
    float: right;
  }

  h2 {
    font-size: 1.3em;
    text-transform: uppercase;
    text-decoration: underline;

    &::after {
      content: ':';
    }
  }

  h3 {
    text-transform: uppercase;

    small {
      text-transform: none;
    }
  }

  .control-label {
    padding: 7px 0 0 0;
    font-weight: 700
  }

  .small-col {
    padding: 0;
  }

  .form-group {
    margin-left: 0;
    margin-right: 0;
  }

  div.displayInline{
    display:inline;
  } 

  .header .form-group{
    display:inline;
  }

  .col-description{
    text-align: center; 
  }

  .note{
    margin-bottom: 15px;
  }

  .has-scans .has-feedback .form-control-feedback {
    top: 0;
    right: -14px;
  }
  
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
  }

}
