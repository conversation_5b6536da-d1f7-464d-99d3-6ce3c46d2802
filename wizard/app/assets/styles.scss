@charset "utf-8";

html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, code, del, dfn, em, img, q, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
    border: 0 none;
    font-family: inherit;
    font-size: 100%;
    font-style: inherit;
    font-weight: inherit;
    margin: 0;
    padding: 0;
    vertical-align: baseline;
}

* {
	-webkit-box-sizing: border-box;
	   -moz-box-sizing: border-box;
	        box-sizing: border-box;
}

body {
	// background: transparent url("images/background.jpg") repeat-x center;
  background: #ddd;
	background-size: 100%;
	// background-color:#C1D397;
	color: #222;
  font-family: "Helvetica Neue",Arial,Helvetica,sans-serif;
}

#bottomBar {
    background-image: url(images/axepto-logo.png);
    background-repeat: no-repeat;
    margin-bottom: 20px;
    margin-left: 20px;
    margin-right: 20px;
    height: 50px;
    width: 195px;
    float: right;
}

.btn-success {
	background-color: #72D54A;
	border-color: #72D54A; 
}

.btn-success[disabled] {
	background-color: #C0C0C0;
	border-color: #C0C0C0; 
}

.btn-success[disabled]:hover {
	background-color: #C0C0C0;
	border-color: #C0C0C0; 
}

.btn-success[disabled]:focus {
	background-color: #C0C0C0;
	border-color: #C0C0C0; 
}

#siteWrapper  {
	width: 100%;
	margin: 20px auto;
}

#logoImage {
	float: left;
}

#twentyYears
{
	float: right;
	margin-top: 50px;
	margin-right:20px;
}

#navigation {
	background-color:#0073bc;
	border-radius: 10px 10px 0 0;
	font-size: 18px;
}
#navigation::before,
#navigation::after {
	display: table;
	content: " ";
}
#navigation::after {
	clear: both;
}
.navigation-title,
.navigation-item {
	text-shadow: 1px 1px rgba(56, 76, 0, .1);
}
.navigation-title {
	color: white;
	float: left;
	padding: 10px 20px;
	font-weight: bold;
}
.navigation-item {
	padding: 10px 20px;
	text-align: center;
	float: right;
	color: white;
}
.navigation-item + .navigation-item {
	border-right: 1px solid #002e4b;
}
.navigation-item:last-child {
	border-left: 1px solid #002e4b;
}
.navigation-item-logout::after {
	background-image: url('images/logout.png');
	background-repeat: no-repeat;
	background-size: 14px 14px;
	height: 14px;
	width: 14px;
	display: inline-block;
	position: relative;
	top: 2px;
	left: 5px;
	content: " ";
}

.scan-camera::after {
	background-image: url('images/camera.png');
	background-repeat: no-repeat;
	background-size: 20px 20px;
	height: 20px;
	width: 20px;
	display: inline-block;
	position: relative;
	right:1px;
	content: " ";
}

#siteContent {
	margin-top: 65px;
	background-color: white;
  border-bottom: 2px solid #d0d0d0;
  margin-bottom: 1rem;
}

#pagination {
 	padding-top: 10px;
	padding-left: 5px;
	padding-right: 5px;
	text-decoration: none;
	color: #464646 !important;
}

a {
	text-decoration: none;
	color: #464646;

}

a.disabled {
	text-decoration: none;
	color: #B8B8B8;
}

a.selected {
	font-weight: bold;
	color:black;
}

#breadcrumb {
	background: url("images/breadcrumbBackground.png") repeat-x scroll 0 bottom transparent;
	background-color: #ffffff;
	height: 60px;
	border-bottom: 2px solid #e1e1e1;
	line-height: 60px;
	padding: 0px 10px;
}

.breadcrumbHeading {
	border: 2px solid #e1e1e1;
	border-radius: 5px;
	padding: 7px 5px;
	font-weight: bold;
	height: 30px;
	vertical-align: middle;
	text-decoration: none;
	color: #464646;
}

.pdfLink {
	text-decoration: none;
	color: #464646;
}

.breadcrumbDelimiter {
	vertical-align: middle;
}

#mainContent {
	padding: 0;
	padding-bottom: 20px;
}

.errorFormHolder{
	padding: 0;
}

.recordItem {
	border-radius: 5px;
	width: 100%;
	padding: 20px;
}

.photoTable {
	width:100%;
	padding-left:0;
	padding-right:0;
	margin-bottom: 20px;
}

.photoTable .row {
	//padding:20px;
	border: 2px solid #e1e1e1;
  background-color: #fafafa;
	display: flex;
	margin: 0;
  padding: 10px 0;
}

.photoTable [class^=col-] {
  display: flex;
  align-items: center;

}

.photoTable .photoTable-center {
  justify-content: center;
  display: flex;
  align-items: center;
}


.photoTable [class^=col-] .image-holder .description-holder{
	align-self: center;
}

.photoTable input.disabled {
  background-color: #E0E0E0;
  border-color: #e0e0e0;
  color: #aaa;
}

.photoTable input {
  border: 1px solid #002e4b;
  border-radius: 5px;
  background-color: #0073bc;
  text-shadow: 1px 1px rgba(56, 76, 0, .1);
  color: white;
  float: right;
  cursor: pointer;
  font-size: 100%;
  padding: 10px 20px;
  height:40px;
}

.photoTable .row + .row {
	border-top:0;
}

.photoCol {
	min-height: 44px;
	border: 1px solid #e1e1e1;
}

//-----
.clientTable  {
	border-collapse:collapse;
	background-color: #fafafa;
	margin-bottom: 20px;
}

.clientTable tr {
	padding:20px;
	border: 2px solid #e1e1e1;
	border-radius: 5px;

}
.clientTable   td {
	padding:20px;
}

/* added preview button */
/*
TODO real CSS style
copied from style of send button
*/
.clientTable  td  input.disabled {
  background-color: #E0E0E0;
  border-color: #e0e0e0;
  color: #aaa;
}

.clientTable td input {
  border: 1px solid #002e4b;
  border-radius: 5px;
  background-color: #0073bc;
  text-shadow: 1px 1px rgba(56, 76, 0, .1);
  color: white;
  float: right;
  cursor: pointer;
  font-size: 100%;
  padding: 10px 20px;
}

/* end of added preview button */


.clientTable   th {
	padding:15px;
	font-weight:bold;
	background:url("images/breadcrumbBackground.png") repeat-x scroll 0 bottom transparent;
}

#welcome {
	border: 1px solid black;
	border-radius: 5px;
	padding:20px;
	font-size: 120%;
}

#welcome-title {
	font-size: 140%;
	font-weight:bold;
	padding-bottom:20px;
}

#welcome-text {

	padding-bottom:10px;
}

#welcome-button {

	padding-top:20px;
	padding-bottom:10px;
}

#welcome-button  input {
	border: 2px solid #23765a;
	border-radius: 3px;
	height: 40px;
	width:220px;
	background-color: #23765a;
  color: white;
  padding: 5px;
  cursor: pointer;
  font-size: 100%;
	margin:10px;
}

.title  {
	font-weight:bold;
	color: #464646;
}

.recordItem h1 {
	font-size: 150%;
	font-weight: bold;
	margin-bottom: 20px;
}

.recordItem h1 img {
	margin-bottom: 3px;
}

.recordItem p.address {
	line-height: 25px;
}

.recordItem p.recordItemActions {
	margin: 15px 10px 5px;
}

.recordItem p.customerInfo {
	text-align: justify;
	display: none;
	margin-top: 15px;
}

.recordItemActions {
	overflow: hidden;
	padding: 0;
}
.recordItemActions input {
	border: 1px solid #002e4b;
	border-radius: 5px;
	background-color: #0073bc;
	text-shadow: 1px 1px rgba(56, 76, 0, .1);
	color: white;
	float: right;
	cursor: pointer;
	font-size: 100%;
	padding: 10px 20px;
}

.recordItemActions input.greenColor {
	background-color: #72D54A;
	border: 1px solid #72D54A;
}

.recordItemActions-password {
  text-align: center;
}
.recordItemActions input.disabled{
	background-color: #E0E0E0;
  border-color: #e0e0e0;
  color: #aaa;
}

.secondRecordItem {
	margin-right: 0px;
}

.infoRow:nth-child(even) {
	background-color: #f0f0f0;
}
.infoLabel {
	float: left;
	font-weight: bold;
	width: 35%;
	vertical-align: middle;
	line-height: 45px;
	height: 45px;
	font-size: 1.2em;
	padding-left: .5em;
}
.infoRow {
  overflow: hidden;
}
.infoValue {
	float: left;
	width: 65%;
	text-align: right;
	padding-right: 1em;
	line-height: 45px;
	height: 45px;
	font-size: 1.2em;
}
.infoValue input {
  margin-bottom: 12px;
	width: 80%;
  text-align: right;
	padding: .3em .5em;
  margin-top: 5px;
  margin-right: -.5em;
	font-size: 18px;
	border-radius: 10px;
	border: 1px solid #ccc;
}

.infoValue input:focus {
  outline: none;
}

.detailLabel {
	line-height: 30px;
	height: 30px;
	font-size: 100%;
}

.detailValue {
	line-height: 30px;
	height: 30px;
	font-size: 100;
}

.enlargedInfoRow {
	display: none;
}

/*
juquery ui override
*/

/*
.ui-dialog .ui-dialog-titlebar {
    padding: 5px;
}*/

.dialogInput-label {
  display: block;
  margin-bottom: 10px;
  margin-top: 10px;
}

.dialogInput-password {
  text-align: center;
}
.dialogInput-password label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
}

#newContactForm {
  padding: 10px 20px;
}
#newContactForm	input.text, #newContactForm select.text {
	margin-bottom: 12px;
	width: 40%;
	padding: .3em 1em;
	font-size: 18px;
	margin: 0 auto;
	border-radius: 10px;
	border: 1px solid #ccc;
}

#newContactForm	input.text:focus {
	outline: none;
}

#newContactForm select.text {
	width: 100%
}

#newContactForm select.text option {
	/*padding: 5px;*/
}

#newContactForm label.radio {
	text-align: left;
	font-weight: normal;
}

#newContactForm [class^=col-] {
	height: 40px;
	margin: 10px 0 10px 0;
}

#newContactForm .container-fluid .row{
	margin: 0;
}

/*#newContactForm label span {
  margin-left: 5px;
  padding-top: 3px;
  display: inline-block;
  padding-bottom: 3px;
}*/

/*#newContactForm input[type="radio"] {
  margin: 0;
  position: relative;
  top: -1px;
}*/

#newContactForm .radioRow {
  padding: 10px 0;
}

#newContactForm p.recordItemActions {
	margin-top: 15px;
}

#newContactForm .errorMessageForm-button {
  float: none;
  margin-left: -20px;
}

.process-done {
    background-image: url("images/axepto-green.png");
    background-size: 35px 19px;
    width: 35px;
    height: 19px;
}

.process-not-done {
    background-image: url("images/busy.png");
    background-size: 35px 19px;
    width: 13px;
    height: 13px;

    &.wider {
      width: 25px;
    }
}

.process-cancel {
    background-image: url("images/cancel.png");
    background-size: 20px 20px;
    width: 20px;
    height: 20px;
}

.process-cancel:hover {
    cursor: hand;
}

.ui-widget {
	font-size: inherit;
}

.warning-overlay {
	position: absolute;
	top: 0;
	left: 0;
  right: 0;
  bottom: 0;
	z-index: 1;
  background-color: #777;
	background-color: rgba(119, 119, 119, .5);
}
.warning-window {
  border-radius: 10px;
  padding: 1.5em;
  z-index: 2;
  background-color: white;
  width: 40%;
  min-width: 200px;
  position: absolute;
  top: 30%;
  left: 30%;
  -webkit-box-shadow: 0 0 15px rgba(0, 0, 0, .25);
  -moz-box-shadow: 0 0 15px rgba(0, 0, 0, .25);
  -ms-box-shadow: 0 0 15px rgba(0, 0, 0, .25);
  box-shadow: 0 0 15px rgba(0, 0, 0, .25);
}
.warning-window .message {
  text-align: center;
  margin-bottom: 2em;
  font-weight: bold;
}
.warning-window .button {
  border: 1px solid #002e4b;
	border-radius: 5px;
	background-color: #0073bc;
	text-shadow: 1px 1px rgba(56, 76, 0, .1);
	color: white;
	cursor: pointer;
	font-size: 100%;
	padding: 10px 20px;
  min-width: 100px;
  text-align: center;
}
.warning-window .button:focus {
  outline: none;
}
.warning-window .button:first-child {
  float: left;
}
.warning-window .button:last-child {
  float: right;
}

.modal {
  text-align: center;
  padding: 0!important;
}

.modal:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}

.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

input.photoButton {
  float: left;
  white-space: normal;

}

#mainContent h2{
	font-weight: bold;
	font-size: 105%;
}
.navbar {
  padding: 10px 0;
}
.navbar .container{
   width: auto;
}

.logo {
  width: 150px;
  height: 24px;
  background-image: url(images/cfh_logo.png);
  background-size: 100%;
}

.headerRow [class^=col-]{
  margin: auto 0 auto 0;
}

.headerRow span {
 	font-size: 16px;
}

.vertical-align {
  display: flex;
  flex-direction: row;
}

.headerRow button {
 	margin-left: 25px;
}

.headerRow button:last-child {
 	margin-left: 0;
}

/*
override end
*/

.form-group .form-group {
  margin-bottom: 6px;
}

.min-height {
  min-height: 13em;
  margin-bottom: 1em;
}

.centerElement{
	margin: 0 auto;
}

.materialElement .checkbox{
	text-align: center;
}

.materialElement span{
	margin: 0 auto;
}

.materialElement .checkbox input{
	margin: 7px 0 0 -8px;
}