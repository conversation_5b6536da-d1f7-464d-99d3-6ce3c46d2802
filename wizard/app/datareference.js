export default class DataReference {
    constructor(value) {
        this.value = value;
        this.listeners = [];
    }

    onChange(fn) {
        this.listeners.push(fn);
    }

    transact(transFn) {
        let valueCopy = JSON.parse(JSON.stringify(this.value));
        this.value = transFn(this.value);
        for (var i = 0; i < this.listeners.length; i++) {
            this.listeners[i]([valueCopy, this.value]);
        }
    }
}
