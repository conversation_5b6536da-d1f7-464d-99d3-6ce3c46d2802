import 'babel-polyfill';
import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import {setLogLevel} from './Operations';
import {Iterable} from 'immutable';

setLogLevel(6);

window.onerror = function(errorMsg, url, line, column, errorObj) {
    window.logE('Error: ' + errorMsg + ', url: ' + url + ', line: ' + line + ', column: ' + column + ', error obj: ' + errorObj);
};

// Define endsWith function
if (!String.prototype.endsWith) {
    String.prototype.endsWith = function(searchString, position) {
        var subjectString = this.toString();
        if (position === undefined || position > subjectString.length) {
            position = subjectString.length;
        }
        position -= searchString.length;
        var lastIndex = subjectString.indexOf(searchString, position);
        return lastIndex !== -1 && lastIndex === position;
    };
}

if (!String.prototype.startsWith) {
    String.prototype.startsWith = function(searchString, position) {
        position = position || 0;
        return this.indexOf(searchString, position) === position;
    };
}

// Define immutable destructuring
Iterable.prototype[Symbol.for('get')] = function(value) {return this.get(value) }

document.addEventListener('DOMContentLoaded', () => ReactDOM.render(<App />, document.getElementById('app')));
