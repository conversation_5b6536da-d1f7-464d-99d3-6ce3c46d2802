import 'bootstrap/dist/css/bootstrap.min.css'
import './assets/axepto.scss';
import {AppState} from './AppState';
import React from 'react';
import TopBar from './components/Topbar';
import {finalFolder, scanFolder, signedFolder, zipFolder} from './Constants';
import Sites from './Sites';
import {createFolder, copy, loadFile} from './Operations';
import {fromJS} from 'immutable';
import {formTypes} from './FormTypes';
import msg from './translations/sk'

// Handlers with corresponding
// identification string are defined here

// AppState currently contains the full state of the app, of all the pages
/* AppState scheme:

current_site: String
scans: List < Map [text: String, done: Bool] >
toSign: List < Map [name: String, path: String, done: Bool]>
password: String or undefined
metadata: Map<String, String>

*/

/* Metadata should be a list of Maps, each following this scheme:

key: String (no spaces, no accents, refers to field in pdf)
text: String (any text representation of this key, which will be displayed)
value: String (default value of this key)
editable: Boolean (should this field be editable while creating pdf)
required: Boolean (is it required - not empty - to build the pdf)

*/

export default class App extends React.Component {

    componentWillMount() {
        createFolder(finalFolder);
        createFolder(scanFolder);
        createFolder(signedFolder);
        createFolder(zipFolder);
        copy('metadata.json', finalFolder);

        // We are not listening to changes yet - this doesn't and should not
        // invoke a render. It's only an initial parse of data
        console.log('Transact attempt');
        AppState.transact((state) => {
            console.log('transacting');
            const metadata = JSON.parse(loadFile('metadata.json'));
            console.log(metadata);
            return state.set('metadata', fromJS(metadata)) //raw data drom inputs
                .set('pdfMetadata', fromJS({})) //metadata after form specific postprocessing
                .set('fieldData', fromJS({}))
                .set('formFields', fromJS({}))
                .set('lockedFields', fromJS({})) // fields locked after signing warinig shown when try editing
                .set('disabledFields', fromJS({})) // temporary locked fields for validity of data
                .set('type', metadata.type);
        });

        AppState.onChange((chng) => {
            this.setState({});
        });
    }

    render() {
        const {current_site, metadata, showForm} = AppState.value;
        console.log(current_site, metadata);
        console.log(AppState.value.get('current_site'));
        let site = Sites[current_site];
        return (
            <div>
                <div id="siteWrapper">
                    {showForm ? null : <TopBar title={site.title} msg={msg} />}
                    <site.handler msg={msg} />
                    <div id="bottomBar"/>
                </div>
            </div>
        );
    }
}