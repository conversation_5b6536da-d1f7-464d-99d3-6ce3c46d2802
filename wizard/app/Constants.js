import {fromJS} from 'immutable';

export const finalFolder = 'final_docs';
export const scanFolder = finalFolder + '/scans';
export const unzipTo = 'documents';
export const signedFolder = 'signed';
export const zipFolder = 'zip'; 
export const customerDataSite = 'customer_data';
export const packagePasswordSite = 'package_password';
export const signingProcessSite = 'signing_process';
export const sendErrorSite = 'send_error';
export const photoDataSite = 'photo_data';
export const sitePredecessors = {
	[customerDataSite]: customerDataSite,
	[packagePasswordSite]: packagePasswordSite,
	[signingProcessSite]: signingProcessSite,
	[photoDataSite]: signingProcessSite
};
export const initialState = fromJS({
	'current_site': packagePasswordSite
});
