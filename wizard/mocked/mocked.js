'use strict';

// Uses 'filesystem' global Map as representation of filesystem
// every node in filesystem has '__isFile' flag (folders vs files)
// folder has other nodes as keys
// file has '__content'
// files may have other keys  ('__isZip', '__signed', ... )

var filesystem = {
    '__isFile': false,
    'css': {
        '__isFile': false,
        'styles': {
            '__isFile': false,
            'styles.css': {
                '__isFile': true,
                '__content': 'Some styles inside..'
            }
        },
        'images': {
            '__isFile': false,
            'logout.png': {
                '__isFile': true,
                '__content': 'image content ...'
            },
            'greenStatus.png': {
                '__isFile': true,
                '__content': 'other image content .'
            }
        }
    },
    'wizard.html': {
        '__isFile': true,
        '__content': 'Some html code'
    },
    'signing.properties': {
        '__isFile': true,
        '__content': 'Some properties'
    },
    'main.js': {
        '__isFile': true,
        '__content': 'well this is a js code'
    },
    'index.html': {
        '__isFile': true,
        '__content': 'I ran out of ideas for template contents'
    },
    'document.zip': {
        '__isFile': true,
        '__isZip': true,
        '__password': '123',
        'zmluva_cfh.pdf': {
            '__isFile': true,
            '__content': 'Now this is some serious content to be signed'
        }
    },
    'metadata.json': {
        '__isFile': true,
        '__content': JSON.stringify(


{
    "documents_info": [
        {
            "title": "Zmluva",
            "file": "zmluva_cfh.pdf",
            "required": true
        }
    ],

    "scans_info": {
        "mandatory": [
          {
            "text": "OP/pas predná strana",
            "description": "",
            "folder": "op_predna"
          }
        ],
        "optional": [
          {
            "text": "OP zadná strana",
            "description": "",
            "folder": "op_zadna"
          }
        ]
    },

    "error_info": [
        {
          "type": 0,
          "text": "Zákazník nebol zastihnutý"
        },
        {
          "type": 1,
          "text": "Zákazník odmietol podpísať"
        },
        {
          "type": 2,
          "text": "Zákazník nepoznal heslo"
        }
    ],

    "client_id": "47",
    "type": "Zmluva",
    "zakaznik_meno": "Jožko",
    "zakaznik_priezvisko": "Mrkvička",
    "zakaznik_adresa": "Malinova 23"
}


            )
    },
    'description.txt': {
        '__isFile': true,
        '__content': ''
    },
};

window.deepCopy = function(obj) {
    return JSON.parse(JSON.stringify(obj));
}

window.getFileByPath = function(path) {
    var split = path.split('/');
    var currentNode = filesystem;
    for (var i = 0; i < split.length; i++) {
        if ((split[i] in currentNode) && (!currentNode.__isFile)) {
            currentNode = currentNode[split[i]];
        } else {
            throw 'Invalid path: ' + path + 'n on filesystem: ' + filesystem;
        }
    }
    return currentNode;
}

window.saveFileToPath = function(file, path) {
    var split = path.split('/');
    var currentNode = filesystem;
    for (var i = 0; i < split.length - 1; i++) {
        if ((split[i] in currentNode) && (!currentNode.__isFile)) {
            currentNode = currentNode[split[i]];
        } else if (currentNode.__isFile) {
            throw 'Invalid path (file instead of folder): ' + path + 'n on filesystem: ' + filesystem;
        } else {
            currentNode[split[i]] = {};
            currentNode = currentNode[split[i]];
        }
    }
    currentNode[split[split.length - 1]] = file;
}

window.callFn = function(fname, params) {
    var fn = window[fname];
    if (typeof fn === 'function') {
        fn(JSON.stringify(params === 'undefined' ? {} : params));
    } else {
        throw 'There is no such function ' + fname;
    }
}

window.getQRcode = function(json) {
    var qcode = '123456789012';
    var params = deepCopy(json.parameter);
    params.parameter = qcode;
    callFn(json.call, params);
}

window.signePdf = function(json) {
    var pdf = getFileByPath(json.path);
    pdf.__signed = true;
    saveFileToPath(pdf, json.savePath);
    callFn(json.call, json.parameter);
}


// Should only throw when there is no such file
window.preview = function(json) {
    getFileByPath(json.path);
}

window.unZip = function(json) {
    console.log("trying to unzip " + json.file);
    var zipped = getFileByPath(json.file);
    if (zipped.__password !== json.password) {
        return false;
    }
    var unzipped = deepCopy(zipped);
    unzipped.__isZip = false;
    unzipped.__isFile = false;
    saveFileToPath(unzipped, json.path);
    console.log('unzipped');
    return true;
}

window.zip = function(json) {
    var unzipped = getFileByPath(json.folder);
    var zipped = deepCopy(unzipped);
    zipped.__isZip = true;
    zipped.__isFile = true;
    zipped.__password = json.password;
    saveFileToPath(zipped, json.file);
}

window.send = function(json) {
    getFileByPath(json.file);
    callFn(json.call, json.parameter);
}

window.scan = function(json) {
    var img = {
        '__isFile': true,
        '__content': 'Some important scanned image',
    };
    var name = 'file' in json ? json.file : 'img' + Math.floor(Math.random()*1000000)+'.jpg';
    var path = json.folder + '/' + name;
    saveFileToPath(img, path);
    var params = deepCopy(json.parameter);
    params.parameter = path;
    callFn(json.call, params);
}

window.copy = function(json) {
    var file = deepCopy(getFileByPath(json.file));
    var split = json.file.split('/')
    saveFileToPath(file, json.folder + '/' + split[split.length - 1]);
}

window.deleteFile = function(json) {
    var split = json.file.split('/');
    var last = split[split.length - 1];
    split.splice(split.length - 1, 1);//this does changes on original array
    var path = split.join('/');
    var containingFolder = getFileByPath(path);
    if (last in containingFolder) {
        delete containingFolder[last];
    } else {
        throw 'The file/folder on path ' + json.file + ' does not exist';
    }
}

window.deleteFolder = function(json) {
    deleteFile({'file': json.folder});
}

window.move = function(json) {
    copy(json);
    deleteFile(json);
}

window.saveText = function(json) {
    var file = {
        '__isFile': true,
        '__content': json.text
    };
    saveFileToPath(file, json.file);
}

window.cancel = function(json) {
    console.log("The wizard will close");
}

window.listDir = function(json) {
    var dir = getFileByPath(json.folder);
    var keys = Object.keys(dir);
    var files = keys.filter(function(el) {
        return !el.startsWith('__');
    });
    var res = [];
    console.log(files);
    for (var i = 0; i < files.length; i++) {
        if (dir[files[i]]['__isFile']) {
            res.push({
                'name': files[i],
                'path': json.folder + '/' + files[i],
                'length': dir[files[i]]['__content'].length
            });
        }
    }
    return res;
}

window.createFolder = function(json) {
    var folder = {};
    folder.__isFile = false;
    saveFileToPath(folder, json.folder);
}

window.formPdf = function(json) {
    if ('csv' in json) {
        saveFileToPath({
            '__isFile': true,
            '__content': JSON.stringify(json.data),
            '__signed': false
        }, json.csv);
    }
}

window.jpgToPdf = function(json) {
    var pdf = {
        '__isFile': true,
        '__content': '',
        '__signed': false
    };
    console.log(json.list);
    for (var key in json.list) {
        console.log(json.list[key]);
        pdf.__content += getFileByPath(json.list[key]['path'])['__content'];
    }
    saveFileToPath(pdf, json.file);
}

window.loadFile = function(json) {
    return getFileByPath(json.file)['__content'];
}

window.alert = function(text) {
    console.warn(text);
}

window.alertD = function(text) {
    console.warn('Debug: ' + text);
}

window.logD = function(text) {
    console.log(text);
}

window.logE = function(text) {
    console.error(text);
}

window.setLogLevel = function(lvl) {
    console.log('Log level set to: ' + lvl);
}