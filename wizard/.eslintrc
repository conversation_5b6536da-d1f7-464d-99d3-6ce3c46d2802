{
"parser": "babel-eslint",
"rules": {
    "brace-style": [1, "1tbs", {"allowSingleLine": true}],
    "camelcase": 1,
    "comma-dangle": [0, "always-multiline"],
    "comma-spacing": [1, {"before": false, "after": true}],
    "comma-style": [1, "last"],
    "consistent-this": [1, "self"],
    "curly": [2, "all"],
    "eqeqeq": [2, "allow-null"],
    "eol-last": 1,
    "guard-for-in": 0,
    "indent": [2, 4],
    "key-spacing": 0,
    "new-cap": [1, {"capIsNew": false, "newIsCap": true}],
    "new-parens": 0,
    "no-alert": 0,
    "no-bitwise": 0,
    "no-cond-assign": 1,
    "no-console": 0,
    "no-constant-condition": 1,
    "no-delete-var": 2,
    "no-debugger": 1,
    "no-dupe-keys": 1,
    "no-duplicate-case": 1,
    "no-empty-class": 1,
    "no-eval": 1,
    "no-ex-assign": 1,
    "no-extend-native": 0,
    "no-floating-decimal": 1,
    "no-func-assign": 1,
    "no-implied-eval": 1,
    "no-inner-declarations": 1,
    "no-invalid-regexp": 1,
    "no-irregular-whitespace": 1,
    "no-iterator": 1,
    "no-labels": 1,
    "no-lonely-if": 1,
    "no-loop-func": 0,
    "no-mixed-spaces-and-tabs": 1,
    "no-native-reassign": 2,
    "no-negated-in-lhs": 1,
    "no-new-func": 1,
    "no-new-object": 1,
    "no-new-wrappers": 2,
    "no-obj-calls": 1,
    "no-octal": 1,
    "no-plusplus": 0,
    "no-proto": 2,
    "no-redeclare": 1,
    "no-return-assign": 1,
    "no-self-compare": 1,
    "no-sequences": 1,
    "no-shadow": 0,
    "no-spaced-func": 1,
    "no-sparse-arrays": 1,
    "no-throw-literal": 1,
    "no-trailing-spaces": 1,
    "no-unused-vars": [1, {"vars": "all", "args": "none"}],
    "no-undef": 1,
    "no-undefined": 2,
    "no-undef-init": 1,
    "no-underscore-dangle": 0,
    "no-unreachable": 1,
    "no-use-before-define": [1, "nofunc"],
    "no-warning-comments": [0, {"terms": ["todo", "fixme"]}],
    "no-wrap-func": 1,
    "quotes": [1, "single", "avoid-escape"],
    "quote-props": [0, "as-needed"],
    "radix": 1,
    "semi": [1, "always"],
    "semi-spacing": [1, {"before": false, "after": true}],
    "space-after-keywords": [1, "always"],
    "space-before-blocks": [1, "always"],
    "space-before-function-parentheses": [1, "never"],
    "space-infix-ops": 1,
    "space-in-brackets": 1,
    "space-in-parens": [1, "never"],
    "space-return-throw-case": 1,
    "space-unary-ops": [1, {"words": true, "nonwords": false}],
    "use-isnan": 1,
    "valid-typeof": 1,
    "vars-on-top": 0,  // disabled for now, see github.com/eslint/eslint/issues/2099
    "wrap-iife": [1, "inside"],
    "yoda": 0,
    "handle-callback-err": [1, "^(err|error)$"],
    "no-mixed-requires": 1,
    "no-new-require": 1,
    "no-path-concat": 1,
    "react/jsx-boolean-value": 1,
    "react/jsx-quotes": 1,
    "react/jsx-no-undef": 1,
    "react/jsx-sort-props": 0,
    "react/jsx-sort-prop-types": 1,
    "react/jsx-uses-react": 1,
    "react/jsx-uses-vars": 1,
    "react/no-did-update-set-state": 1,
    "react/no-multi-comp": 0,
    "react/no-unknown-property": 1,
    "react/prop-types": 0,
    "react/react-in-jsx-scope": 1,
    "react/self-closing-comp": 1,
    "react/wrap-multilines": 1
  },
  "env": {
      "es6": true,
      "browser": true,
      "node": true,
  },
  "ecmaFeatures": {
      "jsx": true,
      "modules": true
  },
  "plugins": [
      "react"
  ]
}
