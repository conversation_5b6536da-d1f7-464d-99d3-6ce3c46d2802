{"name": "axepto-wizard", "version": "0.0.1", "description": "wizard for signing for Axepto project", "repository": {"type": "git", "url": "https://github.com/vacuumlabs/axepto_wizard.git"}, "author": "Vacuumlabs", "dependencies": {"autoprefixer-loader": "^3.1.0", "babel": "^5.6.23", "babel-loader": "^5.0.0", "babel-plugin-extensible-destructuring": "^1.0.2", "babel-polyfill": "^6.3.14", "bluebird": "^2.9.25", "bootstrap": "^3.3.5", "css-loader": "^0.23.0", "diacritics": "^1.2.3", "file-loader": "^0.8.4", "gulp-sass": "^2.0.4", "gulp-util": "^3.0.7", "immutable": "^3.7.5", "node-notifier": "^4.3.1", "node-sass": "^3.4.2", "piping": "^0.3.0", "react": "^0.14.0", "react-bootstrap": "^0.28.3", "react-dom": "^0.14.3", "react-hot-loader": "^1.3.0", "rx": "^4.0.7", "sass-loader": "^3.1.1", "style-loader": "^0.13.0", "url-loader": "^0.5.6", "webpack": "^1.13.0", "webpack-dev-server": "1.13.0"}, "devDependencies": {"babel-core": "^5.8.19", "babel-eslint": "^3.1.17", "del": "^1.2.0", "eslint": "^0.23.0", "eslint-plugin-react": "^2.5.2", "globby": "^2.1.0", "gulp": "^3.9.0", "gulp-replace": "^0.5.3", "gulp-shell": "^0.4.1", "gulp-uglify": "^1.2.0", "run-sequence": "^1.1.1", "yargs": "^3.15.0"}}