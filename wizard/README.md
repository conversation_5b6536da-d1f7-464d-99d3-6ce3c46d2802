# axepto_wizard

How to begin:

- make sure you have node.js, npm and gulp installed
- clone the repo
- npm install
- gulp build -w wizard2
  - creates package out/package.zip . Template 'wizard_files/wizard2' is used for package static files. Package is also unzipped in out/package for convenience
- gulp build-mock 
  - creates mocked package, which can be opened in browser. Since the browser does not support necessary Android functions, capabilities of such package are very limited.
