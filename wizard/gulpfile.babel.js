'use strict';

var Promise = require('bluebird');

var gulp = require('gulp');
var replace = require('gulp-replace');
var shell = require('gulp-shell');
var fs = require('fs');
var del = require('del');
var runSequence = require('run-sequence');
var sass = require('gulp-sass');

var eslint = require('eslint/lib/cli');
var globby = require('globby');
import makeWebpackConfig from './webpack/makeconfig';
import webpackBuild from './webpack/build';
import webpackDevServer from './webpack/devserver';


// get wizard command line arg
// maybe TODO: add list of options to w argument
var argv = require('yargs').alias('w', 'wizard').default('w', 'wizard0').argv;

var ASSETS_FOLDER = './app/assets/';
var BUILD_FOLDER = './out/';
var BUILD_PACKAGE = `${BUILD_FOLDER}package/`;
var JS_FOLDER = `build/`;
var JS_NAME = 'app.js';

var wizardSourceDir = `wizard_files/${argv.wizard}`;
var templateSourceDir = `wizard_files/templates/${argv.wizard}`;
var generatedSourceDir = `wizard_files/generated/`;

// Base data to random generated wizard
var possibleNames = [
    'John Smith',
    'Albert Taylor',
    'Monica White ',
    'Július Satinský',
    'Milan Lasica',
    'Rasťo Piško',
    'Jožo Mrkvička',
    'Martin Mečúň',
    'Konrád Malina',
];

var possibleAddresses = [
    'Mariánska 32, 821 02 Bratislava',
    'Šípova 47, Horná Dolná',
    'Modra 32, Zelená dolina',
    'Čierny les 14, Martin',
    'Maliarska 32, Podbrezová',
    'Horná 32, Rakovce',
    'Viničná 76, Orešany',
    'Lipová 23, Svidník',
    'Stratená 66, Oščadnica'
];

var possibleEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];

var wizardTitle = {
    'wizard0': 'Personal loan',
    'wizard1': 'Purchase order'
};

// Fields to inject to templates
const fieldsToInject = {
    'wizard0': [
        'name', 'surname', 'id', 'birth', 'request-date', 'personal-number', 'address',
        'phone', 'email', 'client-id', 'display-name'
    ],
    'wizard1': [
        'requestor', 'id', 'birth', 'request-date', 'personal-number', 'address', 'phone',
        'email', 'client-id', 'display-name'
    ],
    'html': [
        'fullname', 'address', 'wizard-title', 'password'
    ]
};

// Returns random integer from interval [from, to)
// in case of one parameter, interval is [0, param)
function randomInt(from, to) {
    if (arguments.length === 1) {
        to = from;
        from = 0;
    }
    return Math.floor(Math.random() * (to - from)) + from;
}

function randomDate(start, end) {
    var d = new Date(randomInt(start.getTime(), end.getTime()));
    return '' + d.getDate() + '.' + (d.getMonth() + 1) + '.' + d.getFullYear();
}

function randomTriplet() {
    return ('00' + randomInt(0, 1000)).slice(-3);
}

function getRandomData() {
    const nameindex = randomInt(possibleNames.length);
    const name = possibleNames[nameindex].split(' ')[0];
    const surname = possibleNames[nameindex].split(' ')[1];
    const birth = randomDate(new Date(1930, 0, 1), new Date(1998, 0, 1));
    const requestDate = randomDate(new Date(), new Date());
    return {
        name: name,
        surname: surname,
        id: randomInt(1000000),
        birth: birth,
        personalNumber: randomInt(10000),
        address: possibleAddresses[randomInt(possibleAddresses.length)],
        phone: '+421 907 ' + randomTriplet() + ' ' + randomTriplet(),
        email: possibleEmails[nameindex],
        clientId: randomInt(1000000),
        requestDate: requestDate
    };
}

function getReplaceObject(fields, data) {
    const replaceObj = {};
    for (let field of fields) {
        switch (field) {
        case 'requestor':
            replaceObj['requestor'] = data.name + ' ' + data.surname;
            break;
        case 'fullname':
            replaceObj['fullname'] = data.name + ' ' + data.surname;
            break;
        case 'personal-number':
            replaceObj['personal-number'] = data.personalNumber;
            break;
        case 'client-id':
            replaceObj['client-id'] = data.clientId;
            break;
        case 'display-name':
            replaceObj['display-name'] = data.name + ' ' + data.surname + ', born ' + data.birth;
            break;
        case 'wizard-title':
            replaceObj['wizard-title'] = data.title;
            break;
        case 'password':
            replaceObj['password'] = '1234';
            break;
        case 'request-date':
            replaceObj['request-date'] = data.requestDate;
            break;
        default:
            replaceObj[field] = data[field];
        }
    }
    return replaceObj;
}

function injectDataToFile(filePath, randomData, fieldsToInject) {
    return new Promise(function(resolve, reject) {
        const replaceObject = getReplaceObject(
            fieldsToInject, randomData
        );
        let stream = gulp.src(filePath);
        for (var field in replaceObject) {
            if (replaceObject.hasOwnProperty(field)) {
                stream = stream.pipe(replace('@@inject-' + field, replaceObject[field]));
            }
        }
        stream = stream.pipe(gulp.dest(generatedSourceDir));
        stream.on('end', resolve).on('error', reject);
    });
}


function injectContentToFile(src, toInjectSrc, tag) {
    return gulp.src(src)
        .pipe(replace(tag, fs.readFileSync(toInjectSrc, 'utf8')));
}

function injectJsToHtml(htmlSrc, jsSrc) {
    return injectContentToFile(htmlSrc, jsSrc, '@@inject-js-code-here');
}

function injectCssToHtml(htmlSrc, cssSrc, tag) {
    return injectContentToFile(htmlSrc, cssSrc, '@@inject-css');
}

// ----- Random wizard ----- //

gulp.task('__clean-generated', function(callback) {
    del([generatedSourceDir + '**/*'], callback);
});

gulp.task('__copy-template-files', ['__clean-generated'], function() {
    return gulp.src(templateSourceDir + '/**/*')
        .pipe(gulp.dest(generatedSourceDir));
});

gulp.task('__generate-template', ['__copy-template-files'], function() {
    const randomData = getRandomData();
    randomData.title = wizardTitle[argv.wizard];
    const promises = [
        injectDataToFile(generatedSourceDir + '/metadata.json', randomData, fieldsToInject[argv.wizard]),
        injectDataToFile(generatedSourceDir + '/metadata.html', randomData, fieldsToInject['html'])
    ];

    return Promise.all(promises);
});

gulp.task('__generated-files', ['__generate-template'], function() {
    return gulp.src(generatedSourceDir + '/**/*')
        .pipe(gulp.dest(BUILD_PACKAGE));
});

// ----------


// ----- Wizard from file ----- //

gulp.task('__files', function() {
    return gulp.src(wizardSourceDir + '/**/*')
      .pipe(gulp.dest(BUILD_PACKAGE));
});

// ----------

// ----- Build tasks ----- //

gulp.task('default', ['build-mocked', 'build-webpack-dev']);
gulp.task('build-webpack-dev', webpackDevServer(makeWebpackConfig(true)));
gulp.task('build-webpack-prod', webpackBuild(makeWebpackConfig(false)));

gulp.task('__clean', function(cb) {
    del([BUILD_FOLDER + '**/*'], cb);
});

gulp.task('build-mocked', function() {
    return gulp.src('mocked/mocked.js')
        .pipe(gulp.dest(JS_FOLDER));
});

// Build scss files
gulp.task('__sass', function() {
    return gulp.src(ASSETS_FOLDER + 'axepto.scss')
        .pipe(sass())
        .pipe(gulp.dest(BUILD_PACKAGE + 'css'));
});

// Images copy to build folder
gulp.task('__img', function() {
    return gulp.src('app/assets/images/**/*')
      .pipe(gulp.dest(BUILD_PACKAGE + 'images'));
});

// Inject build js to html
gulp.task('__html', ['build-webpack-prod'], function() {
    return injectJsToHtml('html/index.html', JS_FOLDER + JS_NAME)
        .pipe(gulp.dest(BUILD_PACKAGE));
});

gulp.task('__inject-styles', function() {
    return injectCssToHtml(BUILD_PACKAGE + 'index.html', BUILD_PACKAGE + 'css/axepto.css')
        .pipe(gulp.dest(BUILD_PACKAGE));
});

/*
 * TODO: refactor this; this task creates zip, another task moves it to the desired directory. It
 * should be possible to handle this in just one task.
 */

gulp.task('__bare-zip', shell.task([
    'zip -r package.zip *'
], {
    cwd: BUILD_PACKAGE,
    ignoreErrors: true
}));

gulp.task('__zip', ['__bare-zip'], function() {
    return gulp.src(`${BUILD_PACKAGE}/package.zip`)
        .pipe(gulp.dest(`${BUILD_FOLDER}`));
});


// ----- Main tasks to run from commandline ----- //

// Task to build wizard from prepared wizard file
gulp.task('build', function() {
    return runSequence(
        '__clean',
        ['__files', '__img', '__sass', '__html'],
        '__inject-styles',
        '__zip'
    );
});

// TODO rename to build-random, not sure if name chande dont break something else
// Task to build randomly generated wizard from template
gulp.task('create-random-wizard', function() {
    return runSequence(
        '__clean',
        ['__generated-files', '__img', '__css', '__html'],
        '__inject-styles',
        '__zip'
    );
});

// TODO update to eslint 1.0.0 and use gulp-eslint
// TODO currently using old installed eslint
gulp.task('eslint', function(callback) {
    const sources = [
        'gulpfile.babel.js',
        'app/*.js',
        'app/components/**/*.js'
    ];
    globby(sources, function(error, paths) {
        if (error) {
            callback(error);
            return;
        }
        eslint.execute(paths.join(' '));
        callback();
    });
});
