# Axepto - Functional Specification (English)

## 1. Application Overview

### 1.1 Purpose and Main Features
Axepto is a comprehensive document signing and delivery management system designed for courier companies. The application facilitates the digital signing of documents during package delivery, providing a complete workflow from document upload to signed document retrieval.

**Key Features:**
- Digital document signing via tablet interface
- Multi-tenant courier company support
- Document workflow management
- Real-time tracking and notifications
- Audit logging and compliance
- Survey and feedback system
- Quarantine management for problematic documents

### 1.2 Technology Stack
- **Backend:** Django 3.2.8 (Python web framework)
- **Database:** PostgreSQL with JSON field support
- **Storage:** AWS S3 for document storage
- **Frontend:** React-based signing wizard for tablets
- **Web Interface:** Django templates with <PERSON>trap
- **Authentication:** Django authentication with fail2ban protection
- **Email:** AWS SES for notifications
- **Monitoring:** Sentry for error tracking
- **Deployment:** Docker with uWSGI and Nginx

### 1.3 Architecture Overview
The system follows a multi-layered architecture:
- **REST API Layer:** Handles mobile app and operator system integration
- **Web Interface Layer:** Provides dashboard for operators, managers, and clients
- **Business Logic Layer:** Manages document workflows and business rules
- **Data Layer:** PostgreSQL database with audit logging
- **Storage Layer:** AWS S3 for document files
- **Integration Layer:** External courier company systems

## 2. Data Models and Entities

### 2.1 Core Models

#### Document
The central entity representing a package/document in the system.
- **Fields:** id, client_id, timestamp, rest (JSON metadata), tracking (JSON), parent (self-reference), signed_locally, signed_remotely, author, customer, version, zip_size, deleted, hard_delete
- **States:** unsigned → signed/error
- **Relationships:** Parent-child for unsigned/signed versions, belongs to Customer, assigned to Courier

#### Customer
Represents courier company clients (Telekom, Orange, O2, Union, etc.)
- **Fields:** id (primary key), name, shipper_id, removal limits for different document types, has_password_protected_packages, active
- **Purpose:** Multi-tenant support for different courier companies

#### Courier
Represents delivery personnel who sign documents
- **Fields:** id, user (OneToOne with Django User), documents (ManyToMany), active, center, deleted
- **Functionality:** Document assignment, driver list management, center assignment

#### History
Tracks all important actions in the system
- **Actions:** ASSIGN, DELETE, DOWNLOAD, LOGIN_REST, SIGNED_REMOTELY, ACKNOWLEDGED
- **Fields:** author, user, document, created_at, action, auto (boolean for automatic actions)

#### Survey
Customer satisfaction survey system
- **Fields:** subject, couriers, courier_centers, active, repeating, description, form_url, package
- **Purpose:** Collect feedback from customers after delivery

#### Quarantine
Handles problematic documents that cannot be processed normally
- **Fields:** id, timestamp, zip_size, url_id, courier, customer, deleted
- **Purpose:** Isolate and manage documents with processing errors

### 2.2 User Roles and Permissions

#### Courier
- Access driver list (assigned documents)
- Download unsigned documents
- Upload signed documents
- Submit survey responses
- Acknowledge document receipt

#### Operator
- Upload unsigned documents
- Assign documents to couriers
- View signed/unsigned document lists
- Manage document lifecycle

#### Manager
- Full system access
- View all reports and analytics
- Manage couriers and surveys
- Access quarantine and audit logs
- System configuration

#### Client
- View documents and courier information
- Access reports and summaries
- Manage courier accounts

## 3. REST API Endpoints

### 3.1 Authentication Endpoints
```
POST /login/ - User authentication (Basic Auth or form-based)
POST /logout/ - User logout
GET /auth/ - Get session token for courier apps
```

### 3.2 Document Management Endpoints
```
GET /active/ - Get courier's assigned documents (driver list)
POST /active/add/ - Assign document to courier by scanning
GET /document/unsigned/{id}/ - Download unsigned document
POST /document/unsigned/ - Upload unsigned document (operator)
GET /document/signed/{id}/ - Download signed document
POST /document/signed/{id}/ - Upload signed document (courier)
POST /document/unsigned/{id}/ack/ - Acknowledge document receipt
POST /document/mark-signed/ - Mark document as remotely signed
```

### 3.3 Data Synchronization Endpoints
```
GET /document/signed/since/{timestamp}/ - List signed documents since timestamp
GET /document/unsigned/since/{timestamp}/ - List unsigned documents since timestamp
```

### 3.4 Assignment and Management
```
POST /assign/ - Assign documents to couriers (operator)
POST /assign2/ - Bulk assignment endpoint
```

### 3.5 Logging and Surveys
```
POST /logs/ - Submit courier logs
GET /logs/{id}/ - Retrieve logs (manager)
POST /survey/ - Submit survey responses
```

### 3.6 User Management
```
POST /register/ - Register new courier
POST /changepassword/ - Change courier password
```

## 4. Web Dashboard Functionality

### 4.1 Authentication and Login

#### Login Page (`/web/login/`)
**Purpose:** Secure access to the web dashboard for all user types.

**Visual Layout:**
```
┌─────────────────────────────────────┐
│              AXEPTO LOGO            │
├─────────────────────────────────────┤
│  Username: [________________]      │
│  Password: [________________]      │
│                                     │
│           [Sign in]                 │
└─────────────────────────────────────┘
```

**Functionality:**
- Centered login card with company logo
- Username and password fields with validation
- Fail2ban protection against brute force attacks
- Automatic redirection to dashboard after successful login
- Support for "next" parameter to redirect to intended page
- Theme-aware logo (light/dark mode support)

**User Experience:**
- Clean, professional interface
- Responsive design for various screen sizes
- Error messages for invalid credentials
- Session management with configurable timeout

### 4.2 Main Dashboard (`/web/dashboard/`)

#### Dashboard Overview
**Purpose:** Central hub for document management and monitoring.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Search: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Parcel List                                                 │
│ Predefined filters:                                         │
│ [Last Work Day] | [This Week] | [Last Week] | [This Month] │
├─────────────────────────────────────────────────────────────┤
│ Filters: [Begin Date] [End Date] [Status▼] [Courier▼]     │
│          [Customer▼] [Show] [Export] [Calculate]           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID        │ Client ID │ Status  │ Courier │ Date      │ │
│ │ DOC001    │ CLI123    │ Signed  │ John D. │ 21/01/04  │ │
│ │ DOC002    │ CLI124    │ Unsigned│ Jane S. │ 21/01/04  │ │
│ │ DOC003    │ CLI125    │ Error   │ Bob M.  │ 21/01/04  │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Previous] Page 1 of 10 [Next >]                         │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Search Bar:** Real-time document search by ID, client ID, or other fields
- **Predefined Filters:** Quick access to common date ranges
- **Advanced Filtering:** Date range, status, courier, and customer filters
- **Document Table:** Sortable columns with status indicators
- **Pagination:** Efficient navigation through large document sets
- **Export Functionality:** Excel export with current filters applied
- **Calculate Feature:** Real-time statistics calculation

**Status Indicators:**
- 🟢 **Signed:** Successfully completed documents
- 🟡 **Unsigned:** Pending signature documents
- 🔴 **Error:** Documents with processing issues
- 🔵 **Assigned:** Documents assigned to couriers

### 4.3 Document Information (`/web/document_info/{id}/`)

#### Document Detail View
**Purpose:** Comprehensive view of individual document with complete history and metadata.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ [SIGNED] [UNSIGNED] [ERROR] ← Status tabs                  │
├─────────────────────────────────────────────────────────────┤
│ Document Details                                            │
│ ┌─────────────────┬─────────────────────────────────────┐   │
│ │ Basic Info      │ Metadata                            │   │
│ │ ID: DOC001      │ Customer Name: John Smith           │   │
│ │ Client ID: CLI  │ Address: 123 Main St               │   │
│ │ Status: Signed  │ Phone: +421901234567               │   │
│ │ Size: 2.5 MB    │ Email: <EMAIL>            │   │
│ │ Date: 21/01/04  │ Contract Type: Internet Service    │   │
│ └─────────────────┴─────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ Documents:                                                  │
│ ✓ Contract PDF | Required | contract.pdf                   │
│ ✓ ID Scan | Required | id_scan.jpg                         │
│ ✓ Signature | Required | signature.pdf                     │
├─────────────────────────────────────────────────────────────┤
│ Scans:                                                      │
│ ✓ Front ID | Identity verification | Complete              │
│ ✓ Back ID | Identity verification | Complete               │
│ ✓ Customer Photo | Visual confirmation | Complete          │
├─────────────────────────────────────────────────────────────┤
│ Action History:                                             │
│ 2024-01-04 10:30 - ASSIGNED to courier John Doe           │
│ 2024-01-04 11:15 - DOWNLOADED by courier                   │
│ 2024-01-04 14:22 - SIGNED by customer                      │
│ 2024-01-04 14:25 - UPLOADED signed version                 │
└─────────────────────────────────────────────────────────────┘
```

**Functionality:**
- **Status Navigation:** Tabs showing document progression (unsigned → signed/error)
- **Metadata Display:** Customer information parsed from document metadata
- **File Management:** List of required and optional documents with completion status
- **Scan Verification:** Visual confirmation of identity documents and photos
- **History Timeline:** Complete audit trail of all document actions
- **Download Links:** Direct access to document files (permission-based)
- **Tracking Data:** Wizard start/close times and signing duration (manager only)

### 4.4 Courier Management (`/web/couriers/`)

#### Courier List View
**Purpose:** Comprehensive courier management interface for administrators.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Search: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Couriers List                                               │
│ Filters: [Active▼] [Center▼] [Show] [Export]              │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │Username │ ID    │Assigned│Signed│Last Login │Active│   │ │
│ │john.doe │JD001  │   15   │  12  │21/01/04   │  ✓  │Edit│ │
│ │jane.sm  │JS002  │    8   │   8  │20/01/04   │  ✓  │Edit│ │
│ │bob.mil  │BM003  │    3   │   1  │19/01/04   │  ✗  │Edit│ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Previous] Page 1 of 5 [Next >]                          │
│                                    [Add Courier]           │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Search Functionality:** Find couriers by username or ID
- **Status Filtering:** Filter by active/inactive status and courier center
- **Performance Metrics:** Assigned vs. signed document counts
- **Activity Tracking:** Last login timestamps
- **Bulk Operations:** Export courier data to Excel
- **Quick Actions:** Direct edit links for each courier

#### Add/Edit Courier Form
**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ Add New Courier                                             │
├─────────────────────────────────────────────────────────────┤
│ Username: [________________]                               │
│ Courier ID: [________________]                             │
│ Password: [________________]                               │
│ Confirm Password: [________________]                       │
│ Center: [Select Center ▼]                                 │
│ Active: ☑ Enabled                                          │
│                                                             │
│ [Cancel] [Save Courier]                                    │
└─────────────────────────────────────────────────────────────┘
```

**Functionality:**
- **User Creation:** Automatic Django user account creation
- **Password Validation:** Configurable password strength requirements
- **Center Assignment:** Link courier to specific delivery center
- **Status Management:** Enable/disable courier accounts
- **Validation:** Username uniqueness and courier ID validation

### 4.5 Survey Management (`/web/surveys/`)

#### Survey List View
**Purpose:** Manage customer satisfaction surveys and feedback collection.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ Survey List                                                 │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Name                    │ Active │ Couriers │          │ │
│ │ Customer Satisfaction   │   ✓    │    25    │   Edit   │ │
│ │ Service Quality Check   │   ✗    │    12    │   Edit   │ │
│ │ Delivery Experience     │   ✓    │    30    │   Edit   │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Previous] Page 1 of 3 [Next >]                          │
│                                    [Add Survey]            │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Survey Overview:** List of all surveys with status and assignment count
- **Active Status:** Enable/disable surveys for courier assignment
- **Courier Assignment:** Track how many couriers have access to each survey
- **Survey Records:** Click survey name to view responses and analytics

#### Survey Creation/Edit Form
**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ Create New Survey                                           │
├─────────────────────────────────────────────────────────────┤
│ Subject: [________________________________]               │
│ Form URL: [________________________________]              │
│ Description:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Please rate your delivery experience...                 │ │
│ │ - How satisfied were you with delivery time?           │ │
│ │ - Was the courier professional and courteous?          │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Repeating: [___] times (optional)                          │
│ Package File: [Choose File] survey_package.zip            │
│ Active: ☑ Enabled                                          │
│                                                             │
│ Assigned Couriers:                                          │
│ ☑ John Doe (JD001)    ☑ Jane Smith (JS002)               │
│ ☐ Bob Miller (BM003)  ☑ Alice Johnson (AJ004)            │
│                                                             │
│ Assigned Centers:                                           │
│ ☑ Center 01 - Downtown  ☐ Center 02 - Suburbs            │
│                                                             │
│ [Cancel] [Save Survey]                                     │
└─────────────────────────────────────────────────────────────┘
```

**Functionality:**
- **Survey Configuration:** Subject, description, and form URL setup
- **Package Upload:** Survey wizard package for tablet deployment
- **Courier Assignment:** Individual courier or center-based assignment
- **Repeat Settings:** Configure survey frequency for regular feedback
- **Status Control:** Enable/disable survey availability

### 4.6 Quarantine Management (`/web/quarantine/`)

#### Quarantine Dashboard
**Purpose:** Monitor and manage documents with processing errors.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ Quarantine Dashboard                                        │
│ Show: [All ▼] [Active] [Deleted]                          │
├─────────────────────────────────────────────────────────────┤
│ ⚠️  Documents requiring attention                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID      │ Error Type        │ Date     │ Size │ Action │ │
│ │ DOC001  │ Invalid signature │ 21/01/04 │ 2.1M │ Review │ │
│ │ DOC002  │ Missing metadata  │ 21/01/04 │ 1.8M │ Review │ │
│ │ DOC003  │ Corrupt ZIP file  │ 20/01/04 │ 0.5M │ Delete │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Previous] Page 1 of 2 [Next >]                          │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Error Classification:** Different error types with specific handling
- **Status Filtering:** View all, active, or deleted quarantine items
- **Size Monitoring:** Track storage usage of quarantined documents
- **Action Management:** Review, restore, or permanently delete options
- **Automatic Cleanup:** Configurable retention policies

### 4.7 System Logs (`/web/logs/`)

#### Logs Dashboard
**Purpose:** Monitor system activity and courier actions.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ System Logs                                                 │
├─────────────────────────────────────────────────────────────┤
│ 📊 Activity Overview                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Timestamp    │ Courier  │ Action    │ Document │ Size  │ │
│ │ 21/01/04 14:30│ john.doe │ DOWNLOAD  │ DOC001   │ 2.1M │ │
│ │ 21/01/04 14:25│ jane.sm  │ UPLOAD    │ DOC002   │ 1.8M │ │
│ │ 21/01/04 14:20│ bob.mil  │ LOGIN     │ -        │ -    │ │
│ │ 21/01/04 14:15│ alice.j  │ ASSIGN    │ DOC003   │ 0.9M │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Previous] Page 1 of 50 [Next >]                         │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Real-time Monitoring:** Live feed of system activities
- **Action Tracking:** Login, download, upload, assign operations
- **Courier Activity:** Individual courier performance monitoring
- **File Size Tracking:** Monitor data transfer volumes
- **Historical Analysis:** Long-term activity pattern analysis

### 4.8 Summary Reports (`/web/summary/`)

#### Analytics Dashboard
**Purpose:** Generate comprehensive reports and statistics.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ Summary Reports                                             │
│ Predefined filters:                                         │
│ [Last Work Day] | [This Week] | [Last Week] | [This Month] │
├─────────────────────────────────────────────────────────────┤
│ Custom Range: [Begin Date] [End Date] [Customer▼]          │
│ [Generate Report] [Export Excel]                           │
├─────────────────────────────────────────────────────────────┤
│ 📈 Performance Metrics                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Metric Type      │ Count │ Percentage │ Trend          │ │
│ │ Signed Documents │  1,245│    87.5%   │ ↗ +5.2%       │ │
│ │ Unsigned Docs    │   178 │    12.5%   │ ↘ -2.1%       │ │
│ │ Error Rate       │    23 │     1.6%   │ ↘ -0.8%       │ │
│ │ Avg Process Time │  2.3h │     -      │ ↗ +0.2h       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📊 Customer Breakdown                                       │
│ Telekom: 45% | Orange: 28% | O2: 15% | Union: 12%         │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Predefined Periods:** Quick access to common reporting periods
- **Custom Date Ranges:** Flexible reporting timeframes
- **Customer Filtering:** Multi-tenant reporting capabilities
- **Performance Metrics:** Key performance indicators with trends
- **Export Functionality:** Excel export for further analysis
- **Visual Analytics:** Charts and graphs for data visualization

### 4.9 Settings Management (`/web/settings/`)

#### System Configuration
**Purpose:** Configure system parameters and business rules.

**Visual Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│ System Settings                                             │
├─────────────────────────────────────────────────────────────┤
│ 🔧 Configuration Parameters                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Setting Name              │ Value        │ Type │ Edit │ │
│ │ DELETE_OLD_DOCUMENTS      │ 30 days      │ INT  │ ✏️   │ │
│ │ NOTIFICATION_MAIL         │ admin@...    │ STR  │ ✏️   │ │
│ │ VALIDATE_PASSWORDS        │ True         │ BOOL │ ✏️   │ │
│ │ DELAY_NOTIFICATION_LIMIT  │ 5 days       │ TIME │ ✏️   │ │
│ │ AWS_STORAGE_BUCKET_NAME   │ axepto-docs  │ STR  │ ✏️   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📋 Customer Schemas                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Customer │ Schema Version │ Last Updated │ Edit        │ │
│ │ Telekom  │ v2.1.3        │ 21/01/04     │ Configure   │ │
│ │ Orange   │ v1.8.7        │ 20/01/04     │ Configure   │ │
│ │ O2       │ v2.0.1        │ 19/01/04     │ Configure   │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Key Features:**
- **Dynamic Configuration:** Runtime parameter modification
- **Type Validation:** Proper data type enforcement
- **Schema Management:** Customer-specific document schemas
- **Notification Settings:** Email and alert configuration
- **Storage Configuration:** AWS S3 and file management settings
- **Security Parameters:** Password policies and authentication settings

### 4.10 User Interface Flow Diagrams

#### Main Dashboard Navigation Flow

```mermaid
flowchart TD
    A[Login Page] --> B{Authentication}
    B -->|Success| C[Main Dashboard]
    B -->|Failure| A
    C --> D[Document List]
    C --> E[Search & Filter]
    C --> F[Export Data]
    D --> G[Document Info]
    G --> H[View History]
    G --> I[Download Files]
    E --> J[Filtered Results]
    F --> K[Excel Export]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style G fill:#fff3e0
```

#### Courier Management Workflow

```mermaid
flowchart TD
    A[Couriers List] --> B[Search Couriers]
    A --> C[Add New Courier]
    A --> D[Edit Existing Courier]
    B --> E[Filtered Results]
    C --> F[Courier Form]
    D --> F
    F --> G{Validation}
    G -->|Valid| H[Save Courier]
    G -->|Invalid| I[Show Errors]
    H --> J[Update List]
    I --> F
    E --> K[Select Courier]
    K --> D

    style A fill:#e8f5e8
    style F fill:#fff3e0
    style H fill:#e1f5fe
```

#### Document Processing States

```mermaid
stateDiagram-v2
    [*] --> Uploaded: Operator uploads document
    Uploaded --> Assigned: Assign to courier
    Assigned --> Downloaded: Courier downloads
    Downloaded --> Signed: Customer signs on tablet
    Downloaded --> Error: Signing fails
    Signed --> Completed: Operator downloads
    Error --> Quarantine: Move to quarantine
    Quarantine --> Assigned: Resolve and reassign
    Quarantine --> Deleted: Permanent removal
    Completed --> [*]
    Deleted --> [*]
```

#### Survey Management Process

```mermaid
flowchart TD
    A[Survey List] --> B[Create Survey]
    A --> C[Edit Survey]
    A --> D[View Responses]
    B --> E[Survey Form]
    C --> E
    E --> F[Assign Couriers]
    F --> G[Assign Centers]
    G --> H[Upload Package]
    H --> I{Validation}
    I -->|Valid| J[Activate Survey]
    I -->|Invalid| K[Show Errors]
    J --> L[Deploy to Tablets]
    K --> E
    D --> M[Analytics Dashboard]

    style A fill:#f3e5f5
    style E fill:#fff3e0
    style J fill:#e8f5e8
```

## 5. Business Processes and Workflows

### 5.1 Document Signing Workflow

```mermaid
flowchart TD
    A[Operator uploads unsigned document] --> B[Document stored in S3]
    B --> C[Document assigned to courier]
    C --> D[Courier downloads document via mobile app]
    D --> E[Customer signs on tablet using wizard]
    E --> F[Signed document uploaded to system]
    F --> G{Upload successful?}
    G -->|Yes| H[Document marked as signed]
    G -->|No| I[Document moved to quarantine]
    H --> J[Operator downloads signed document]
    I --> K[Manual review and resolution]
```

### 5.2 Courier Assignment Process

```mermaid
flowchart TD
    A[Unsigned document available] --> B{Assignment method}
    B -->|Manual| C[Operator assigns to specific courier]
    B -->|Automatic| D[Courier scans QR/barcode]
    C --> E[Document added to courier's driver list]
    D --> E
    E --> F[History record created]
    F --> G[Courier notified of new assignment]
```

### 5.3 Error Handling and Quarantine

```mermaid
flowchart TD
    A[Document processing error] --> B[Error details logged]
    B --> C[Document moved to quarantine]
    C --> D[Notification sent to managers]
    D --> E[Manual review initiated]
    E --> F{Resolution possible?}
    F -->|Yes| G[Document returned to workflow]
    F -->|No| H[Document marked for deletion]
    G --> I[Normal processing resumed]
    H --> J[Cleanup after retention period]
```

### 5.4 Survey Management Process

```mermaid
flowchart TD
    A[Manager creates survey] --> B[Survey assigned to couriers/centers]
    B --> C[Survey appears in courier's driver list]
    C --> D[Customer completes survey on tablet]
    D --> E[Survey response recorded]
    E --> F[Manager reviews survey results]
    F --> G[Reports generated for analysis]
```

## 6. Frontend Signing Wizard

### 6.1 Wizard Architecture
The signing wizard is a React-based application that runs on Android tablets used by couriers during delivery.

**Key Components:**
- **SigningProcess:** Main signing interface
- **FormTypes:** Dynamic form generation based on customer schemas
- **AppState:** Global state management using Immutable.js
- **Operations:** File handling and PDF manipulation

### 6.2 Signing Process Flow
1. **Document Loading:** Wizard loads unsigned document package
2. **Form Display:** Customer-specific forms are rendered
3. **Data Collection:** Customer information is captured
4. **Document Signing:** PDF documents are digitally signed
5. **Package Creation:** Signed documents are packaged with metadata
6. **Upload:** Completed package is uploaded to server

### 6.3 Supported Operations
- PDF form filling with customer data
- Digital signature application
- Document preview functionality
- Multi-document signing support
- Offline capability with sync when online

## 7. Integration Points

### 7.1 AWS Services Integration
- **S3 Storage:** Document file storage with versioning
- **SES Email:** Automated notifications and reports
- **CloudWatch:** Monitoring and logging (via Sentry)

### 7.2 Courier Company Systems
- **Telekom:** Custom schema and notification integration
- **Orange:** Specialized document formats and workflows
- **O2:** Specific validation rules and processes
- **Union:** Custom reporting and data export
- **Packeta/InTime:** Delivery company integrations

### 7.3 External APIs
- **Certificate Validation:** Digital certificate management
- **Notification Services:** SMS and email delivery
- **Tracking Systems:** Package tracking integration

## 8. Security and Authentication

### 8.1 Authentication Mechanisms
- **Session-based:** Web dashboard authentication
- **Token-based:** Mobile app authentication
- **Basic Auth:** API access for external systems
- **Fail2ban:** Brute force protection

### 8.2 Security Measures
- **HTTPS:** All communications encrypted
- **Certificate Management:** Digital signing certificates
- **Access Control:** Role-based permissions
- **Audit Logging:** Complete action tracking
- **Data Encryption:** Sensitive data protection

### 8.3 Compliance Features
- **Audit Trail:** Complete history of all actions
- **Data Retention:** Configurable retention policies
- **Export Controls:** Secure data export functionality
- **Privacy Protection:** GDPR compliance features

## 9. System Administration

### 9.1 Configuration Management
- **Settings System:** Dynamic configuration via web interface
- **Schema Management:** Customer-specific document schemas
- **Notification Rules:** Configurable alert thresholds
- **Retention Policies:** Automated cleanup configuration

### 9.2 Monitoring and Maintenance
- **Health Checks:** System status monitoring
- **Performance Metrics:** Response time and throughput tracking
- **Error Tracking:** Sentry integration for error monitoring
- **Backup Systems:** Automated data backup procedures

### 9.3 Cron Jobs and Automation
- **Document Cleanup:** Automated removal of old documents
- **Notification Processing:** Scheduled notification delivery
- **Report Generation:** Automated report creation
- **Archive Management:** Document archiving processes

## 10. Technical Implementation Details

### 10.1 Database Schema
- **PostgreSQL:** Primary database with JSON field support
- **Migrations:** Django migration system for schema changes
- **Indexing:** Optimized indexes for performance
- **Constraints:** Data integrity enforcement

### 10.2 File Storage Architecture
- **S3 Buckets:** Organized by customer and document type
- **Versioning:** Document version management
- **Encryption:** Server-side encryption for sensitive data
- **Access Control:** IAM-based access management

### 10.3 Performance Optimization
- **Caching:** Redis/Memcached for session and data caching
- **Database Optimization:** Query optimization and connection pooling
- **CDN Integration:** Static asset delivery optimization
- **Load Balancing:** Multi-instance deployment support
