# Axepto - Functional Specification (English)

## 1. Application Overview

### 1.1 Purpose and Main Features
Axepto is a comprehensive document signing and delivery management system designed for courier companies. The application facilitates the digital signing of documents during package delivery, providing a complete workflow from document upload to signed document retrieval.

**Key Features:**
- Digital document signing via tablet interface
- Multi-tenant courier company support
- Document workflow management
- Real-time tracking and notifications
- Audit logging and compliance
- Survey and feedback system
- Quarantine management for problematic documents

### 1.2 Technology Stack
- **Backend:** Django 3.2.8 (Python web framework)
- **Database:** PostgreSQL with JSON field support
- **Storage:** AWS S3 for document storage
- **Frontend:** React-based signing wizard for tablets
- **Web Interface:** Django templates with <PERSON>trap
- **Authentication:** Django authentication with fail2ban protection
- **Email:** AWS SES for notifications
- **Monitoring:** Sentry for error tracking
- **Deployment:** Docker with uWSGI and Nginx

### 1.3 Architecture Overview
The system follows a multi-layered architecture:
- **REST API Layer:** Handles mobile app and operator system integration
- **Web Interface Layer:** Provides dashboard for operators, managers, and clients
- **Business Logic Layer:** Manages document workflows and business rules
- **Data Layer:** PostgreSQL database with audit logging
- **Storage Layer:** AWS S3 for document files
- **Integration Layer:** External courier company systems

## 2. Data Models and Entities

### 2.1 Core Models

#### Document
The central entity representing a package/document in the system.
- **Fields:** id, client_id, timestamp, rest (JSON metadata), tracking (JSON), parent (self-reference), signed_locally, signed_remotely, author, customer, version, zip_size, deleted, hard_delete
- **States:** unsigned → signed/error
- **Relationships:** Parent-child for unsigned/signed versions, belongs to Customer, assigned to Courier

#### Customer
Represents courier company clients (Telekom, Orange, O2, Union, etc.)
- **Fields:** id (primary key), name, shipper_id, removal limits for different document types, has_password_protected_packages, active
- **Purpose:** Multi-tenant support for different courier companies

#### Courier
Represents delivery personnel who sign documents
- **Fields:** id, user (OneToOne with Django User), documents (ManyToMany), active, center, deleted
- **Functionality:** Document assignment, driver list management, center assignment

#### History
Tracks all important actions in the system
- **Actions:** ASSIGN, DELETE, DOWNLOAD, LOGIN_REST, SIGNED_REMOTELY, ACKNOWLEDGED
- **Fields:** author, user, document, created_at, action, auto (boolean for automatic actions)

#### Survey
Customer satisfaction survey system
- **Fields:** subject, couriers, courier_centers, active, repeating, description, form_url, package
- **Purpose:** Collect feedback from customers after delivery

#### Quarantine
Handles problematic documents that cannot be processed normally
- **Fields:** id, timestamp, zip_size, url_id, courier, customer, deleted
- **Purpose:** Isolate and manage documents with processing errors

### 2.2 User Roles and Permissions

#### Courier
- Access driver list (assigned documents)
- Download unsigned documents
- Upload signed documents
- Submit survey responses
- Acknowledge document receipt

#### Operator
- Upload unsigned documents
- Assign documents to couriers
- View signed/unsigned document lists
- Manage document lifecycle

#### Manager
- Full system access
- View all reports and analytics
- Manage couriers and surveys
- Access quarantine and audit logs
- System configuration

#### Client
- View documents and courier information
- Access reports and summaries
- Manage courier accounts

## 3. REST API Endpoints

### 3.1 Authentication Endpoints
```
POST /login/ - User authentication (Basic Auth or form-based)
POST /logout/ - User logout
GET /auth/ - Get session token for courier apps
```

### 3.2 Document Management Endpoints
```
GET /active/ - Get courier's assigned documents (driver list)
POST /active/add/ - Assign document to courier by scanning
GET /document/unsigned/{id}/ - Download unsigned document
POST /document/unsigned/ - Upload unsigned document (operator)
GET /document/signed/{id}/ - Download signed document
POST /document/signed/{id}/ - Upload signed document (courier)
POST /document/unsigned/{id}/ack/ - Acknowledge document receipt
POST /document/mark-signed/ - Mark document as remotely signed
```

### 3.3 Data Synchronization Endpoints
```
GET /document/signed/since/{timestamp}/ - List signed documents since timestamp
GET /document/unsigned/since/{timestamp}/ - List unsigned documents since timestamp
```

### 3.4 Assignment and Management
```
POST /assign/ - Assign documents to couriers (operator)
POST /assign2/ - Bulk assignment endpoint
```

### 3.5 Logging and Surveys
```
POST /logs/ - Submit courier logs
GET /logs/{id}/ - Retrieve logs (manager)
POST /survey/ - Submit survey responses
```

### 3.6 User Management
```
POST /register/ - Register new courier
POST /changepassword/ - Change courier password
```

## 4. Web Dashboard Functionality

### 4.1 Dashboard Views
- **Main Dashboard:** Document list with filtering, sorting, and export
- **Document Info:** Detailed document view with history and files
- **Courier Info:** Courier-specific document list and statistics

### 4.2 Management Interfaces
- **Couriers Management:** Add, edit, delete couriers; assign to centers
- **Surveys Management:** Create, edit surveys; view responses
- **Quarantine Management:** View and manage problematic documents
- **Settings Management:** System configuration and parameters

### 4.3 Reporting and Analytics
- **Summary Reports:** Document statistics and metrics
- **History Tracking:** Complete audit trail of all actions
- **Logs Dashboard:** System and courier activity logs
- **Export Functionality:** Excel export for all major data views

## 5. Business Processes and Workflows

### 5.1 Document Signing Workflow

```mermaid
flowchart TD
    A[Operator uploads unsigned document] --> B[Document stored in S3]
    B --> C[Document assigned to courier]
    C --> D[Courier downloads document via mobile app]
    D --> E[Customer signs on tablet using wizard]
    E --> F[Signed document uploaded to system]
    F --> G{Upload successful?}
    G -->|Yes| H[Document marked as signed]
    G -->|No| I[Document moved to quarantine]
    H --> J[Operator downloads signed document]
    I --> K[Manual review and resolution]
```

### 5.2 Courier Assignment Process

```mermaid
flowchart TD
    A[Unsigned document available] --> B{Assignment method}
    B -->|Manual| C[Operator assigns to specific courier]
    B -->|Automatic| D[Courier scans QR/barcode]
    C --> E[Document added to courier's driver list]
    D --> E
    E --> F[History record created]
    F --> G[Courier notified of new assignment]
```

### 5.3 Error Handling and Quarantine

```mermaid
flowchart TD
    A[Document processing error] --> B[Error details logged]
    B --> C[Document moved to quarantine]
    C --> D[Notification sent to managers]
    D --> E[Manual review initiated]
    E --> F{Resolution possible?}
    F -->|Yes| G[Document returned to workflow]
    F -->|No| H[Document marked for deletion]
    G --> I[Normal processing resumed]
    H --> J[Cleanup after retention period]
```

### 5.4 Survey Management Process

```mermaid
flowchart TD
    A[Manager creates survey] --> B[Survey assigned to couriers/centers]
    B --> C[Survey appears in courier's driver list]
    C --> D[Customer completes survey on tablet]
    D --> E[Survey response recorded]
    E --> F[Manager reviews survey results]
    F --> G[Reports generated for analysis]
```

## 6. Frontend Signing Wizard

### 6.1 Wizard Architecture
The signing wizard is a React-based application that runs on Android tablets used by couriers during delivery.

**Key Components:**
- **SigningProcess:** Main signing interface
- **FormTypes:** Dynamic form generation based on customer schemas
- **AppState:** Global state management using Immutable.js
- **Operations:** File handling and PDF manipulation

### 6.2 Signing Process Flow
1. **Document Loading:** Wizard loads unsigned document package
2. **Form Display:** Customer-specific forms are rendered
3. **Data Collection:** Customer information is captured
4. **Document Signing:** PDF documents are digitally signed
5. **Package Creation:** Signed documents are packaged with metadata
6. **Upload:** Completed package is uploaded to server

### 6.3 Supported Operations
- PDF form filling with customer data
- Digital signature application
- Document preview functionality
- Multi-document signing support
- Offline capability with sync when online

## 7. Integration Points

### 7.1 AWS Services Integration
- **S3 Storage:** Document file storage with versioning
- **SES Email:** Automated notifications and reports
- **CloudWatch:** Monitoring and logging (via Sentry)

### 7.2 Courier Company Systems
- **Telekom:** Custom schema and notification integration
- **Orange:** Specialized document formats and workflows
- **O2:** Specific validation rules and processes
- **Union:** Custom reporting and data export
- **Packeta/InTime:** Delivery company integrations

### 7.3 External APIs
- **Certificate Validation:** Digital certificate management
- **Notification Services:** SMS and email delivery
- **Tracking Systems:** Package tracking integration

## 8. Security and Authentication

### 8.1 Authentication Mechanisms
- **Session-based:** Web dashboard authentication
- **Token-based:** Mobile app authentication
- **Basic Auth:** API access for external systems
- **Fail2ban:** Brute force protection

### 8.2 Security Measures
- **HTTPS:** All communications encrypted
- **Certificate Management:** Digital signing certificates
- **Access Control:** Role-based permissions
- **Audit Logging:** Complete action tracking
- **Data Encryption:** Sensitive data protection

### 8.3 Compliance Features
- **Audit Trail:** Complete history of all actions
- **Data Retention:** Configurable retention policies
- **Export Controls:** Secure data export functionality
- **Privacy Protection:** GDPR compliance features

## 9. System Administration

### 9.1 Configuration Management
- **Settings System:** Dynamic configuration via web interface
- **Schema Management:** Customer-specific document schemas
- **Notification Rules:** Configurable alert thresholds
- **Retention Policies:** Automated cleanup configuration

### 9.2 Monitoring and Maintenance
- **Health Checks:** System status monitoring
- **Performance Metrics:** Response time and throughput tracking
- **Error Tracking:** Sentry integration for error monitoring
- **Backup Systems:** Automated data backup procedures

### 9.3 Cron Jobs and Automation
- **Document Cleanup:** Automated removal of old documents
- **Notification Processing:** Scheduled notification delivery
- **Report Generation:** Automated report creation
- **Archive Management:** Document archiving processes

## 10. Technical Implementation Details

### 10.1 Database Schema
- **PostgreSQL:** Primary database with JSON field support
- **Migrations:** Django migration system for schema changes
- **Indexing:** Optimized indexes for performance
- **Constraints:** Data integrity enforcement

### 10.2 File Storage Architecture
- **S3 Buckets:** Organized by customer and document type
- **Versioning:** Document version management
- **Encryption:** Server-side encryption for sensitive data
- **Access Control:** IAM-based access management

### 10.3 Performance Optimization
- **Caching:** Redis/Memcached for session and data caching
- **Database Optimization:** Query optimization and connection pooling
- **CDN Integration:** Static asset delivery optimization
- **Load Balancing:** Multi-instance deployment support
