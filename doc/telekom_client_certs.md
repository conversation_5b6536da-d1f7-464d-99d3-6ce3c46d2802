# Steps to renew Telekom client certificates

This guide is for renewing Telekom client certificates for notifications.

## 1. Generate new RSA private key and CSR

Currently we use RSA 4096-bit key, but in future it may change. Ask Telekom for new key specs beforehand.

```bash
openssl req -nodes -newkey rsa:4096 -keyout cert.key -out cert.csr
```

## 2. Send CSR to Telekom

Send CSR to Telekom, they will generate new certificate and send it back to you.

## 3. Convert Telekom certificate to PEM format (optional)

If Telekom sends certificate in DER format, but we need it in PEM format.

```bash
openssl x509 -inform der -in cert.cer -outform pem -out cert.pem
```

## 4. Test new certificate

Test new certificate by sending request to Telekom notification endpoint. Set environment variable
`TELEKOM_CERTIFICATE_PATH` to path to new certificate send by Telekom. Set environment variable
`TELEKOM_SERVER_KEY_PATH` to path to generated private key from step 1. Connect to our aws VPN instance
to send request.

```bash
source bin/activate
python manage.py runcrons axepto.cron.SendTelekomNotifications --force
```

It it only returns 401 status error and no SSL errors than it works. If it returns SSL error, investigate.

## 5. Update environment variables

Open ticket in [Vacuumlabs DevOps portal](https://vacuum.atlassian.net/servicedesk/customer/portal/1) to update
environment variables `TELEKOM_CERTIFICATE` and `TELEKOM_SERVER_KEY`, but don't include secrets in ticket. The assigned
DevOps will aks for them just before deployment and will ask you to check if the Telekom notifications are going
through.