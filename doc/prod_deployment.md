# Deploying changes to production

**You should test if everything is working on test environment before deploying to production.**

*All commits to master are automatically deployed to test environment, but it is not always turned on. You can also run the Github actions workflow manually.*

## 1. Create tag for release

Tags are automatically deployed to production. Tags with format `sps/X.Y.Z` are deployed to SPS, tags with format `packeta/X.Y.Z` are deployed to Packeta.

To create a tag, run:

```bash
git tag sps/X.Y.Z
git tag packeta/X.Y.Z
```

## 2. Wait for deployment

Wait GitHub action to finish with status `success`.

## 3. Make some basic checks

Check if dashboard is working.