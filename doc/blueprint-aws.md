# Creating AWS environment ECS

![ECS architecture](./axepto-infra-architecture.png)

This diagram shows the deployment of an application using Amazon Elastic Container Service (ECS).

Components:

- ECS: A containerization platform that allows you to run and manage containers in the cloud.
- ECS task: A container that runs an application.
- Nginx: Reverse proxy server that mediates requests to the application and is serving static files. Requests are proxied to UWSGI. 
- UWSGI: Web server that runs the application exposed as socket.
- Application: Django application.
- S3: Storing files that application generates.

Nginx and uWSGI configuration is stored in the /conf directory.

# Deployment
Using Github actions workflows (/.github/workflows/...)

## Development
Automatically after push to master will start the Github actions workflow to deploy to development

## Production
When git tag is pushed, it will start the Github actions workflow to deploy to production
