# Dev Quickstart

This is for Ubuntu 18.04, localhost development.

## 1. Setup environment

Create missing `log` folder and `.env`

    mkdir log
    cp example.env .env

and set `DJANGO_SETTINGS_MODULE` in `.env` to the settings module you wish to use (i.e. `axepto.settings.development`)

Install dependencies

    python3 -m venv venv
    source venv/bin/activate
    pip install -r dev-requirements.txt
    pre-commit install

## 2. Setup DB

Run postgresql. Create DB and user:

    sudo -u postgres createdb axepto
    sudo -u postgres createuser -P axepto

When prompted for a password, use whatever you want and save it in the `.env` file to `DB_PASSWORD` environment
variable.

    sudo -u postgres psql
    GRANT ALL PRIVILEGES ON DATABASE axepto TO axepto;
    ALTER USER axepto CREATEDB;
    \q

Init DB

    python dev-manage.py migrate

## 3. Run

Create a super user and run server

    python dev-manage.py createsuperuser
    python dev-manage.py runserver

## 4. Create users

Open http://localhost:8000/web/admin/ login with superuser and create users (at least manager and operator, you can
also add courier or create it later with manager account on the dashboard).

Add one empty document (make it deleted and hard deleted) to the database, because there is a bug with the first document creation (it was never a big
enough problem, to be fixed).

## 5. Upload unsigned document

Use `testing_utils/api_utils.py` to upload unsigned document you will need an operator account assigned to customer.

    python testing_utils/api_utils.py [username (operator)] [password] [host (i.e. http://localhost:8000)] [customer (i.e. o2)]

## 6. Use the web interface

Browse http://localhost:8000/web/login/

## Troubleshooting

### Missing ICU_VERSION

If you get the `Please set the ICU_VERSION environment variable to the version of ICU you have installed.` error
during the `pip install -r requirements.txt` step, uou need to install the ICU lib via

    sudo apt-get install libicu-dev

### 404 for all static files

Ensure you have `DEBUG = True` in the settings you use (in the `axepto/settings` folder).

### "403 CSRF verification failed. Request aborted." when trying to login

Use `CSRF_COOKIE_SECURE = False` in the settings you use.

You may also need ot use `SESSION_COOKIE_SECURE = False` in development.
