# Axepto - Funkč<PERSON>if<PERSON> (Slovenčina)

## 1. Prehľad Aplikácie

### 1.1 Účel a Hlavné Funkcie
Axepto je komplexný systém na správu podpisovania dokumentov a doručovania pre kuriérske spoločnosti. Aplikácia umožňuje digitálne podpisovanie dokumentov počas doručovania balíkov a poskytuje kompletný workflow od nahratia dokumentu až po získanie podpísaného dokumentu.

**Kľúčové Funkcie:**
- Digitálne podpisovanie dokumentov cez tablet rozhranie
- Podpora viacerých kuriérskych spoločností (multi-tenant)
- Správa workflow dokumentov
- Sledovanie v reálnom čase a notifikácie
- Audit logging a compliance
- Systém prieskumov a spätnej väzby
- Správa karantény pre problematické dokumenty

### 1.2 Technologický Stack
- **Backend:** Django 3.2.8 (Python web framework)
- **Databáza:** PostgreSQL s podporou JSON polí
- **Úložisko:** AWS S3 pre ukladanie dokumentov
- **Frontend:** React-based podpisovací wizard pre tablety
- **Web Rozhranie:** Django templates s Bootstrap
- **Autentifikácia:** Django autentifikácia s fail2ban ochranou
- **Email:** AWS SES pre notifikácie
- **Monitoring:** Sentry pre sledovanie chýb
- **Deployment:** Docker s uWSGI a Nginx

### 1.3 Prehľad Architektúry
Systém využíva viacvrstvovú architektúru:
- **REST API Vrstva:** Spracováva integráciu mobilných aplikácií a operátorských systémov
- **Web Rozhranie Vrstva:** Poskytuje dashboard pre operátorov, manažérov a klientov
- **Business Logic Vrstva:** Spravuje workflow dokumentov a obchodné pravidlá
- **Dátová Vrstva:** PostgreSQL databáza s audit loggingom
- **Storage Vrstva:** AWS S3 pre súbory dokumentov
- **Integračná Vrstva:** Externé systémy kuriérskych spoločností

## 2. Dátové Modely a Entity

### 2.1 Základné Modely

#### Document (Dokument)
Centrálna entita reprezentujúca balík/dokument v systéme.
- **Polia:** id, client_id, timestamp, rest (JSON metadata), tracking (JSON), parent (self-reference), signed_locally, signed_remotely, author, customer, version, zip_size, deleted, hard_delete
- **Stavy:** unsigned (nepodpísaný) → signed/error (podpísaný/chyba)
- **Vzťahy:** Parent-child pre nepodpísané/podpísané verzie, patrí k Customer, priradený k Courier

#### Customer (Zákazník)
Reprezentuje klientov kuriérskych spoločností (Telekom, Orange, O2, Union, atď.)
- **Polia:** id (primárny kľúč), name, shipper_id, limity pre odstránenie rôznych typov dokumentov, has_password_protected_packages, active
- **Účel:** Multi-tenant podpora pre rôzne kuriérske spoločnosti

#### Courier (Kuriér)
Reprezentuje doručovateľov, ktorí podpisujú dokumenty
- **Polia:** id, user (OneToOne s Django User), documents (ManyToMany), active, center, deleted
- **Funkcionalita:** Priradenie dokumentov, správa driver listu, priradenie k centru

#### History (História)
Sleduje všetky dôležité akcie v systéme
- **Akcie:** ASSIGN, DELETE, DOWNLOAD, LOGIN_REST, SIGNED_REMOTELY, ACKNOWLEDGED
- **Polia:** author, user, document, created_at, action, auto (boolean pre automatické akcie)

#### Survey (Prieskum)
Systém prieskumov spokojnosti zákazníkov
- **Polia:** subject, couriers, courier_centers, active, repeating, description, form_url, package
- **Účel:** Zbieranie spätnej väzby od zákazníkov po doručení

#### Quarantine (Karanténa)
Spracováva problematické dokumenty, ktoré nemožno normálne spracovať
- **Polia:** id, timestamp, zip_size, url_id, courier, customer, deleted
- **Účel:** Izolácia a správa dokumentov s chybami spracovania

### 2.2 Používateľské Roly a Oprávnenia

#### Courier (Kuriér)
- Prístup k driver listu (priradené dokumenty)
- Sťahovanie nepodpísaných dokumentov
- Nahrávanie podpísaných dokumentov
- Odosielanie odpovedí na prieskumy
- Potvrdenie prijatia dokumentu

#### Operator (Operátor)
- Nahrávanie nepodpísaných dokumentov
- Priradenie dokumentov kuriérom
- Zobrazenie zoznamov podpísaných/nepodpísaných dokumentov
- Správa životného cyklu dokumentov

#### Manager (Manažér)
- Plný prístup k systému
- Zobrazenie všetkých reportov a analytík
- Správa kuriérov a prieskumov
- Prístup ku karanténe a audit logom
- Konfigurácia systému

#### Client (Klient)
- Zobrazenie dokumentov a informácií o kuriéroch
- Prístup k reportom a súhrnom
- Správa účtov kuriérov

## 3. REST API Endpointy

### 3.1 Autentifikačné Endpointy
```
POST /login/ - Autentifikácia používateľa (Basic Auth alebo form-based)
POST /logout/ - Odhlásenie používateľa
GET /auth/ - Získanie session tokenu pre kuriérske aplikácie
```

### 3.2 Endpointy pre Správu Dokumentov
```
GET /active/ - Získanie priradených dokumentov kuriéra (driver list)
POST /active/add/ - Priradenie dokumentu kuriérovi skenovaním
GET /document/unsigned/{id}/ - Stiahnutie nepodpísaného dokumentu
POST /document/unsigned/ - Nahratie nepodpísaného dokumentu (operátor)
GET /document/signed/{id}/ - Stiahnutie podpísaného dokumentu
POST /document/signed/{id}/ - Nahratie podpísaného dokumentu (kuriér)
POST /document/unsigned/{id}/ack/ - Potvrdenie prijatia dokumentu
POST /document/mark-signed/ - Označenie dokumentu ako vzdialene podpísaného
```

### 3.3 Endpointy pre Synchronizáciu Dát
```
GET /document/signed/since/{timestamp}/ - Zoznam podpísaných dokumentov od časovej značky
GET /document/unsigned/since/{timestamp}/ - Zoznam nepodpísaných dokumentov od časovej značky
```

### 3.4 Priradenie a Správa
```
POST /assign/ - Priradenie dokumentov kuriérom (operátor)
POST /assign2/ - Hromadné priradenie endpoint
```

### 3.5 Logovanie a Prieskumy
```
POST /logs/ - Odoslanie kuriérskych logov
GET /logs/{id}/ - Získanie logov (manažér)
POST /survey/ - Odoslanie odpovedí na prieskum
```

### 3.6 Správa Používateľov
```
POST /register/ - Registrácia nového kuriéra
POST /changepassword/ - Zmena hesla kuriéra
```

## 4. Funkcionalita Web Dashboardu

### 4.1 Autentifikácia a Prihlásenie

#### Prihlasovacia Stránka (`/web/login/`)
**Účel:** Bezpečný prístup k web dashboardu pre všetky typy používateľov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────┐
│              AXEPTO LOGO            │
├─────────────────────────────────────┤
│  Používateľské meno: [____________] │
│  Heslo: [________________________] │
│                                     │
│           [Prihlásiť sa]            │
└─────────────────────────────────────┘
```

**Funkcionalita:**
- Centrovaná prihlasovacia karta s logom spoločnosti
- Polia pre používateľské meno a heslo s validáciou
- Fail2ban ochrana proti brute force útokom
- Automatické presmerovanie na dashboard po úspešnom prihlásení
- Podpora "next" parametra pre presmerovanie na zamýšľanú stránku
- Téma-aware logo (podpora svetlého/tmavého režimu)

**Používateľská Skúsenosť:**
- Čisté, profesionálne rozhranie
- Responzívny dizajn pre rôzne veľkosti obrazoviek
- Chybové správy pre neplatné prihlasovacie údaje
- Správa relácií s konfigurovateľným timeoutom

### 4.2 Hlavný Dashboard (`/web/dashboard/`)

#### Prehľad Dashboardu
**Účel:** Centrálne centrum pre správu a monitoring dokumentov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Hľadať: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Zoznam Balíkov                                              │
│ Prednastavené filtre:                                       │
│ [Posledný Pracovný Deň] | [Tento Týždeň] | [Minulý Týždeň] │
├─────────────────────────────────────────────────────────────┤
│ Filtre: [Začiatok] [Koniec] [Stav▼] [Kuriér▼] [Zákazník▼] │
│         [Zobraziť] [Export] [Vypočítať]                    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID        │ ID Klienta│ Stav    │ Kuriér  │ Dátum     │ │
│ │ DOC001    │ CLI123    │ Podpísaný│ Ján D. │ 21/01/04  │ │
│ │ DOC002    │ CLI124    │ Nepodpís.│ Jana S.│ 21/01/04  │ │
│ │ DOC003    │ CLI125    │ Chyba   │ Bob M.  │ 21/01/04  │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 10 [Ďalšia >]               │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Vyhľadávací Panel:** Vyhľadávanie dokumentov v reálnom čase podľa ID, ID klienta alebo iných polí
- **Prednastavené Filtre:** Rýchly prístup k bežným dátumovým rozsahom
- **Pokročilé Filtrovanie:** Filtre pre dátumový rozsah, stav, kuriéra a zákazníka
- **Tabuľka Dokumentov:** Triediteľné stĺpce s indikátormi stavu
- **Stránkovanie:** Efektívna navigácia cez veľké množstvo dokumentov
- **Export Funkcionalita:** Excel export s aktuálnymi filtrami
- **Funkcia Výpočtu:** Výpočet štatistík v reálnom čase

**Indikátory Stavu:**
- 🟢 **Podpísaný:** Úspešne dokončené dokumenty
- 🟡 **Nepodpísaný:** Dokumenty čakajúce na podpis
- 🔴 **Chyba:** Dokumenty s problémami spracovania
- 🔵 **Priradený:** Dokumenty priradené kuriérom

### 4.3 Informácie o Dokumente (`/web/document_info/{id}/`)

#### Detailné Zobrazenie Dokumentu
**Účel:** Komplexný pohľad na jednotlivý dokument s úplnou históriou a metadátami.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [PODPÍSANÝ] [NEPODPÍSANÝ] [CHYBA] ← Záložky stavu          │
├─────────────────────────────────────────────────────────────┤
│ Detaily Dokumentu                                           │
│ ┌─────────────────┬─────────────────────────────────────┐   │
│ │ Základné Info   │ Metadáta                            │   │
│ │ ID: DOC001      │ Meno zákazníka: Ján Novák          │   │
│ │ ID Klienta: CLI │ Adresa: Hlavná 123                 │   │
│ │ Stav: Podpísaný │ Telefón: +421901234567             │   │
│ │ Veľkosť: 2.5 MB │ Email: <EMAIL>             │   │
│ │ Dátum: 21/01/04 │ Typ zmluvy: Internetová služba     │   │
│ └─────────────────┴─────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ Dokumenty:                                                  │
│ ✓ Zmluva PDF | Povinný | zmluva.pdf                        │
│ ✓ Sken OP | Povinný | op_sken.jpg                          │
│ ✓ Podpis | Povinný | podpis.pdf                            │
├─────────────────────────────────────────────────────────────┤
│ Skeny:                                                      │
│ ✓ Predná strana OP | Overenie totožnosti | Dokončené       │
│ ✓ Zadná strana OP | Overenie totožnosti | Dokončené        │
│ ✓ Foto zákazníka | Vizuálne potvrdenie | Dokončené         │
├─────────────────────────────────────────────────────────────┤
│ História Akcií:                                             │
│ 2024-01-04 10:30 - PRIRADENÝ kuriérovi Ján Novák          │
│ 2024-01-04 11:15 - STIAHNUTÝ kuriérom                     │
│ 2024-01-04 14:22 - PODPÍSANÝ zákazníkom                   │
│ 2024-01-04 14:25 - NAHRANÁ podpísaná verzia               │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Navigácia Stavu:** Záložky zobrazujúce progres dokumentu (nepodpísaný → podpísaný/chyba)
- **Zobrazenie Metadát:** Informácie o zákazníkovi parsované z metadát dokumentu
- **Správa Súborov:** Zoznam povinných a voliteľných dokumentov so stavom dokončenia
- **Overenie Skenov:** Vizuálne potvrdenie dokladov totožnosti a fotografií
- **Časová Os Histórie:** Kompletný audit trail všetkých akcií dokumentu
- **Odkazy na Stiahnutie:** Priamy prístup k súborom dokumentov (na základe oprávnení)
- **Tracking Dáta:** Časy spustenia/ukončenia wizarda a doba podpisovania (iba manažér)

### 4.4 Správa Kuriérov (`/web/couriers/`)

#### Zobrazenie Zoznamu Kuriérov
**Účel:** Komplexné rozhranie pre správu kuriérov pre administrátorov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Hľadať: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Zoznam Kuriérov                                             │
│ Filtre: [Aktívny▼] [Centrum▼] [Zobraziť] [Export]         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │Meno     │ ID    │Priradené│Podpísané│Posl.prihl│Aktívny│ │ │
│ │jan.novak│JN001  │   15    │   12    │21/01/04  │  ✓   │Upr│ │
│ │jana.sm  │JS002  │    8    │    8    │20/01/04  │  ✓   │Upr│ │
│ │bob.mil  │BM003  │    3    │    1    │19/01/04  │  ✗   │Upr│ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 5 [Ďalšia >]                │
│                                    [Pridať Kuriéra]        │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Vyhľadávacia Funkcionalita:** Nájdenie kuriérov podľa mena alebo ID
- **Filtrovanie Stavu:** Filtrovanie podľa aktívneho/neaktívneho stavu a kuriérskeho centra
- **Metriky Výkonu:** Počty priradených vs. podpísaných dokumentov
- **Sledovanie Aktivity:** Časové značky posledného prihlásenia
- **Hromadné Operácie:** Export dát kuriérov do Excelu
- **Rýchle Akcie:** Priame odkazy na úpravu pre každého kuriéra

#### Formulár Pridania/Úpravy Kuriéra
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Pridať Nového Kuriéra                                       │
├─────────────────────────────────────────────────────────────┤
│ Používateľské meno: [________________]                     │
│ ID Kuriéra: [________________]                             │
│ Heslo: [________________]                                  │
│ Potvrdiť heslo: [________________]                         │
│ Centrum: [Vybrať centrum ▼]                                │
│ Aktívny: ☑ Povolený                                        │
│                                                             │
│ [Zrušiť] [Uložiť Kuriéra]                                 │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Vytvorenie Používateľa:** Automatické vytvorenie Django používateľského účtu
- **Validácia Hesla:** Konfigurovateľné požiadavky na silu hesla
- **Priradenie Centra:** Prepojenie kuriéra s konkrétnym doručovacím centrom
- **Správa Stavu:** Povolenie/zakázanie účtov kuriérov
- **Validácia:** Jedinečnosť používateľského mena a validácia ID kuriéra

### 4.5 Správa Prieskumov (`/web/surveys/`)

#### Zobrazenie Zoznamu Prieskumov
**Účel:** Správa prieskumov spokojnosti zákazníkov a zbieranie spätnej väzby.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Zoznam Prieskumov                                           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Názov                   │ Aktívny │ Kuriéri │          │ │
│ │ Spokojnosť zákazníkov   │    ✓    │   25    │   Upraviť│ │
│ │ Kontrola kvality služby │    ✗    │   12    │   Upraviť│ │
│ │ Skúsenosť s doručením   │    ✓    │   30    │   Upraviť│ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 3 [Ďalšia >]                │
│                                    [Pridať Prieskum]       │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Prehľad Prieskumov:** Zoznam všetkých prieskumov so stavom a počtom priradení
- **Aktívny Stav:** Povolenie/zakázanie prieskumov pre priradenie kuriérom
- **Priradenie Kuriérov:** Sledovanie počtu kuriérov s prístupom k jednotlivým prieskumom
- **Záznamy Prieskumov:** Kliknutie na názov prieskumu zobrazí odpovede a analytiku

#### Formulár Vytvorenia/Úpravy Prieskumu
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Vytvoriť Nový Prieskum                                      │
├─────────────────────────────────────────────────────────────┤
│ Predmet: [________________________________]               │
│ URL formulára: [________________________________]          │
│ Popis:                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Prosím ohodnoťte vašu skúsenosť s doručením...          │ │
│ │ - Ako ste boli spokojní s časom doručenia?              │ │
│ │ - Bol kuriér profesionálny a zdvorilý?                 │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Opakovanie: [___] krát (voliteľné)                         │
│ Súbor balíka: [Vybrať súbor] prieskum_balik.zip           │
│ Aktívny: ☑ Povolený                                        │
│                                                             │
│ Priradení Kuriéri:                                          │
│ ☑ Ján Novák (JN001)    ☑ Jana Svoboda (JS002)             │
│ ☐ Bob Miller (BM003)   ☑ Alica Johnson (AJ004)            │
│                                                             │
│ Priradené Centrá:                                           │
│ ☑ Centrum 01 - Centrum  ☐ Centrum 02 - Predmestie         │
│                                                             │
│ [Zrušiť] [Uložiť Prieskum]                                 │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Konfigurácia Prieskumu:** Nastavenie predmetu, popisu a URL formulára
- **Nahratie Balíka:** Balík prieskumového wizarda pre nasadenie na tablet
- **Priradenie Kuriérov:** Individuálne priradenie kuriéra alebo na základe centra
- **Nastavenia Opakovania:** Konfigurácia frekvencie prieskumu pre pravidelnú spätnú väzbu
- **Kontrola Stavu:** Povolenie/zakázanie dostupnosti prieskumu

### 4.6 Správa Karantény (`/web/quarantine/`)

#### Dashboard Karantény
**Účel:** Monitoring a správa dokumentov s chybami spracovania.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Dashboard Karantény                                         │
│ Zobraziť: [Všetky ▼] [Aktívne] [Zmazané]                  │
├─────────────────────────────────────────────────────────────┤
│ ⚠️  Dokumenty vyžadujúce pozornosť                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID      │ Typ chyby         │ Dátum    │ Veľk.│ Akcia  │ │
│ │ DOC001  │ Neplatný podpis   │ 21/01/04 │ 2.1M │ Preskúm│ │
│ │ DOC002  │ Chýbajú metadáta  │ 21/01/04 │ 1.8M │ Preskúm│ │
│ │ DOC003  │ Poškodený ZIP     │ 20/01/04 │ 0.5M │ Zmazať │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 2 [Ďalšia >]                │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Klasifikácia Chýb:** Rôzne typy chýb so špecifickým spracovaním
- **Filtrovanie Stavu:** Zobrazenie všetkých, aktívnych alebo zmazaných položiek karantény
- **Monitoring Veľkosti:** Sledovanie využitia úložiska dokumentov v karanténe
- **Správa Akcií:** Možnosti preskúmania, obnovenia alebo trvalého zmazania
- **Automatické Čistenie:** Konfigurovateľné retenčné politiky

### 4.7 Systémové Logy (`/web/logs/`)

#### Dashboard Logov
**Účel:** Monitoring systémovej aktivity a akcií kuriérov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Systémové Logy                                              │
├─────────────────────────────────────────────────────────────┤
│ 📊 Prehľad Aktivity                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Čas         │ Kuriér   │ Akcia     │ Dokument │ Veľk. │ │
│ │ 21/01/04 14:30│ jan.novak│ STIAHNUTIE│ DOC001   │ 2.1M │ │
│ │ 21/01/04 14:25│ jana.sm  │ NAHRATIE  │ DOC002   │ 1.8M │ │
│ │ 21/01/04 14:20│ bob.mil  │ PRIHLÁS.  │ -        │ -    │ │
│ │ 21/01/04 14:15│ alice.j  │ PRIRADENIE│ DOC003   │ 0.9M │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 50 [Ďalšia >]               │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Monitoring v Reálnom Čase:** Živý prehľad systémových aktivít
- **Sledovanie Akcií:** Operácie prihlásenia, stiahnutia, nahratia, priradenia
- **Aktivita Kuriérov:** Monitoring výkonu jednotlivých kuriérov
- **Sledovanie Veľkosti Súborov:** Monitoring objemov prenosu dát
- **Historická Analýza:** Analýza dlhodobých vzorov aktivity

### 4.8 Súhrnné Reporty (`/web/summary/`)

#### Dashboard Analytiky
**Účel:** Generovanie komplexných reportov a štatistík.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Súhrnné Reporty                                             │
│ Prednastavené filtre:                                       │
│ [Posledný Pracovný Deň] | [Tento Týždeň] | [Minulý Týždeň] │
├─────────────────────────────────────────────────────────────┤
│ Vlastný rozsah: [Začiatok] [Koniec] [Zákazník▼]           │
│ [Generovať Report] [Export Excel]                          │
├─────────────────────────────────────────────────────────────┤
│ 📈 Metriky Výkonu                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Typ Metriky      │ Počet │ Percento │ Trend           │ │
│ │ Podpísané Dok.   │ 1,245 │   87.5%  │ ↗ +5.2%        │ │
│ │ Nepodpísané Dok. │  178  │   12.5%  │ ↘ -2.1%        │ │
│ │ Miera Chýb       │   23  │    1.6%  │ ↘ -0.8%        │ │
│ │ Priem. Čas Spraco│ 2.3h  │    -     │ ↗ +0.2h        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📊 Rozdelenie Zákazníkov                                    │
│ Telekom: 45% | Orange: 28% | O2: 15% | Union: 12%         │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Prednastavené Obdobia:** Rýchly prístup k bežným reportovacím obdobiam
- **Vlastné Dátumové Rozsahy:** Flexibilné časové rámce reportovania
- **Filtrovanie Zákazníkov:** Multi-tenant reportovacie schopnosti
- **Metriky Výkonu:** Kľúčové ukazovatele výkonu s trendmi
- **Export Funkcionalita:** Excel export pre ďalšiu analýzu
- **Vizuálna Analytika:** Grafy a diagramy pre vizualizáciu dát

### 4.9 Správa Nastavení (`/web/settings/`)

#### Konfigurácia Systému
**Účel:** Konfigurácia systémových parametrov a obchodných pravidiel.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Nastavenia Systému                                          │
├─────────────────────────────────────────────────────────────┤
│ 🔧 Konfiguračné Parametre                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Názov Nastavenia          │ Hodnota      │ Typ  │ Upraviť│ │
│ │ DELETE_OLD_DOCUMENTS      │ 30 dní       │ INT  │ ✏️     │ │
│ │ NOTIFICATION_MAIL         │ admin@...    │ STR  │ ✏️     │ │
│ │ VALIDATE_PASSWORDS        │ True         │ BOOL │ ✏️     │ │
│ │ DELAY_NOTIFICATION_LIMIT  │ 5 dní        │ TIME │ ✏️     │ │
│ │ AWS_STORAGE_BUCKET_NAME   │ axepto-docs  │ STR  │ ✏️     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📋 Schémy Zákazníkov                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Zákazník │ Verzia Schémy │ Posl. Aktualizácia │ Upraviť │ │
│ │ Telekom  │ v2.1.3        │ 21/01/04           │ Konfig. │ │
│ │ Orange   │ v1.8.7        │ 20/01/04           │ Konfig. │ │
│ │ O2       │ v2.0.1        │ 19/01/04           │ Konfig. │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Dynamická Konfigurácia:** Modifikácia parametrov za behu
- **Validácia Typov:** Vynucovanie správnych dátových typov
- **Správa Schém:** Schémy dokumentov špecifické pre zákazníka
- **Nastavenia Notifikácií:** Konfigurácia emailov a upozornení
- **Konfigurácia Úložiska:** AWS S3 a nastavenia správy súborov
- **Bezpečnostné Parametre:** Politiky hesiel a nastavenia autentifikácie

### 4.10 Audit a Bany (`/web/audit/`, `/web/bans/`)

#### Audit Dashboard
**Účel:** Sledovanie zmien systému a compliance.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Audit Systému                                               │
│ Status: [🟢 Aktívny] [Zapnúť/Vypnúť Audit]                 │
├─────────────────────────────────────────────────────────────┤
│ Export Dát: [Od Dátumu] [Do Dátumu] [Exportovať]           │
├─────────────────────────────────────────────────────────────┤
│ 📋 Posledné Záznamy                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Čas         │ Operátor │ Kuriér   │ Akcia            │ │
│ │ 21/01/04 15:30│ admin   │ jan.novak│ Nahratie dokumentu│ │
│ │ 21/01/04 15:25│ operator│ jana.sm  │ Priradenie       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Správa Banov
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Správa Banов                                                │
│ Hľadať: [________________] [🔍]                            │
│ Čas: [Od Dátumu] [Zobraziť]                                │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Používateľ │ IP Adresa    │ Od       │ Do       │ Akcia │ │
│ │ hacker123  │ ***********  │ 21/01/04 │ 22/01/04 │ Unban │ │
│ │ -          │ ********     │ 20/01/04 │ 21/01/04 │ Unban │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Audit Kontrola:** Zapnutie/vypnutie audit logovania
- **Export Funkcionalita:** CSV export audit záznamov
- **Ban Management:** Správa zakázaných používateľov a IP adries
- **Automatické Bany:** Fail2ban integrácia pre bezpečnosť
- **Unban Funkcionalita:** Manuálne odblokovanie používateľov

### 4.11 Diagramy Toku Používateľského Rozhrania

#### Tok Navigácie Hlavného Dashboardu

```mermaid
flowchart TD
    A[Prihlasovacia Stránka] --> B{Autentifikácia}
    B -->|Úspech| C[Hlavný Dashboard]
    B -->|Neúspech| A
    C --> D[Zoznam Dokumentov]
    C --> E[Hľadanie a Filtrovanie]
    C --> F[Export Dát]
    D --> G[Info o Dokumente]
    G --> H[Zobrazenie Histórie]
    G --> I[Stiahnutie Súborov]
    E --> J[Filtrované Výsledky]
    F --> K[Excel Export]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style G fill:#fff3e0
```

#### Workflow Správy Kuriérov

```mermaid
flowchart TD
    A[Zoznam Kuriérov] --> B[Hľadanie Kuriérov]
    A --> C[Pridať Nového Kuriéra]
    A --> D[Upraviť Existujúceho Kuriéra]
    B --> E[Filtrované Výsledky]
    C --> F[Formulár Kuriéra]
    D --> F
    F --> G{Validácia}
    G -->|Platný| H[Uložiť Kuriéra]
    G -->|Neplatný| I[Zobraziť Chyby]
    H --> J[Aktualizovať Zoznam]
    I --> F
    E --> K[Vybrať Kuriéra]
    K --> D

    style A fill:#e8f5e8
    style F fill:#fff3e0
    style H fill:#e1f5fe
```

#### Stavy Spracovania Dokumentov

```mermaid
stateDiagram-v2
    [*] --> Nahraný: Operátor nahráva dokument
    Nahraný --> Priradený: Priradenie kuriérovi
    Priradený --> Stiahnutý: Kuriér stiahne
    Stiahnutý --> Podpísaný: Zákazník podpíše na tablete
    Stiahnutý --> Chyba: Podpisovanie zlyhá
    Podpísaný --> Dokončený: Operátor stiahne
    Chyba --> Karanténa: Presun do karantény
    Karanténa --> Priradený: Vyriešenie a opätovné priradenie
    Karanténa --> Zmazaný: Trvalé odstránenie
    Dokončený --> [*]
    Zmazaný --> [*]
```

#### Proces Správy Prieskumov

```mermaid
flowchart TD
    A[Zoznam Prieskumov] --> B[Vytvoriť Prieskum]
    A --> C[Upraviť Prieskum]
    A --> D[Zobraziť Odpovede]
    B --> E[Formulár Prieskumu]
    C --> E
    E --> F[Priradiť Kuriérov]
    F --> G[Priradiť Centrá]
    G --> H[Nahrať Balík]
    H --> I{Validácia}
    I -->|Platný| J[Aktivovať Prieskum]
    I -->|Neplatný| K[Zobraziť Chyby]
    J --> L[Nasadiť na Tablety]
    K --> E
    D --> M[Dashboard Analytiky]

    style A fill:#f3e5f5
    style E fill:#fff3e0
    style J fill:#e8f5e8
```

#### Architektúra Systémových Komponentov

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Web Dashboard]
        B[React Signing Wizard]
        C[Mobile Courier App]
    end

    subgraph "Application Layer"
        D[Django Web Views]
        E[REST API Views]
        F[Business Logic]
    end

    subgraph "Data Layer"
        G[PostgreSQL Database]
        H[AWS S3 Storage]
        I[Audit Logs]
    end

    subgraph "External Services"
        J[AWS SES Email]
        K[Sentry Monitoring]
        L[Courier Company APIs]
    end

    A --> D
    B --> E
    C --> E
    D --> F
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style G fill:#e8f5e8
```

## 5. Obchodné Procesy a Workflow

### 5.1 Workflow Podpisovania Dokumentov

#### Prehľad Procesu
Workflow Podpisovania Dokumentov je základný obchodný proces systému Axepto, ktorý umožňuje kompletný životný cyklus spracovania dokumentov od počiatočného nahratia až po získanie finálneho podpísaného dokumentu. Tento proces zabezpečuje bezpečné, sledovateľné a efektívne spracovanie právnych dokumentov, ktoré vyžadujú podpis zákazníka počas doručovania balíkov.

#### Mermaid Diagram
```mermaid
flowchart TD
    A[Operátor nahráva nepodpísaný dokument] --> B[Dokument uložený v S3]
    B --> C[Dokument priradený kuriérovi]
    C --> D[Kuriér stiahne dokument cez mobilnú aplikáciu]
    D --> E[Zákazník podpíše na tablete pomocou wizarda]
    E --> F[Podpísaný dokument nahraný do systému]
    F --> G{Nahratie úspešné?}
    G -->|Áno| H[Dokument označený ako podpísaný]
    G -->|Nie| I[Dokument presunutý do karantény]
    H --> J[Operátor stiahne podpísaný dokument]
    I --> K[Manuálne preskúmanie a riešenie]
```

#### Podrobný Popis Krokov

**Krok 1: Nahratie Dokumentu**
- **Spúšťač:** Operátor dostane nepodpísané dokumenty zo systémov kuriérskej spoločnosti
- **Zodpovedná Rola:** Operátor
- **Proces:** Operátor používa web dashboard na nahratie balíkov dokumentov (ZIP súbory obsahujúce PDF, metadáta a informácie o zákazníkovi)
- **Spracovávané Dáta:** Metadáta dokumentu (detaily zákazníka, typ zmluvy, doručovacia adresa), PDF súbory, tracking informácie
- **Validácia:** Systém validuje formát súboru, limity veľkosti, úplnosť metadát a súlad so schémou zákazníka
- **Spracovanie Chýb:** Neplatné dokumenty sú odmietnuté so špecifickými chybovými správami; operátor musí opraviť problémy pred opätovným nahratím

**Krok 2: Uloženie Dokumentu**
- **Spúšťač:** Úspešná validácia a nahratie dokumentu
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Balík dokumentu je uložený v AWS S3 s jedinečným identifikátorom, metadáta sú uložené do PostgreSQL databázy
- **Spracovávané Dáta:** Súbory dokumentu, metadáta, tracking informácie, záznam audit trail
- **Obchodné Pravidlá:** Dokumenty sú uložené so štruktúrou priečinkov špecifickou pre zákazníka, aplikované sú retenčné politiky
- **Integračné Body:** AWS S3 úložisko, logovanie databázových transakcií

**Krok 3: Priradenie Kuriéra**
- **Spúšťač:** Dokument úspešne uložený a pripravený na spracovanie
- **Zodpovedná Rola:** Operátor alebo Kuriér (v závislosti od metódy priradenia)
- **Proces:** Dokument je priradený konkrétnemu kuriérovi buď manuálne operátorom alebo automaticky keď kuriér naskenuje čiarový kód dokumentu
- **Spracovávané Dáta:** ID kuriéra, ID dokumentu, časová značka priradenia, informácie o doručovacom centre
- **Obchodné Pravidlá:** Kuriéri môžu byť priradení iba k dokumentom pre ich určené centrá; iba aktívni kuriéri
- **Spracovanie Chýb:** Priradenie zlyhá ak je kuriér neaktívny alebo dokument už priradený

**Krok 4: Stiahnutie Dokumentu**
- **Spúšťač:** Kuriér dostane notifikáciu o priradení
- **Zodpovedná Rola:** Kuriér
- **Proces:** Kuriér používa mobilnú aplikáciu na stiahnutie priradených dokumentov do tablet zariadenia
- **Spracovávané Dáta:** Balík dokumentu, informácie o zákazníkovi, pokyny na podpisovanie
- **Obchodné Pravidlá:** Dokumenty môže stiahnuť iba priradený kuriér; stiahnutie je sledované pre audit
- **Integračné Body:** API mobilnej aplikácie, prístup k S3 úložisku, systém audit logovania

**Krok 5: Podpisovanie Zákazníkom**
- **Spúšťač:** Kuriér príde na miesto zákazníka pre doručenie
- **Zodpovedná Rola:** Zákazník (s asistenciou kuriéra)
- **Proces:** Zákazník používa tablet-based podpisovací wizard na preskúmanie a podpísanie dokumentov
- **Spracovávané Dáta:** Podpis zákazníka, fotografie na overenie totožnosti, dáta formulára, časová značka
- **Obchodné Pravidlá:** Všetky povinné polia musia byť vyplnené; overenie totožnosti je povinné pre určité typy dokumentov
- **Spracovanie Chýb:** Neúplné podpisy spúšťajú validačné chyby; proces môže byť reštartovaný

**Krok 6: Nahratie Podpísaného Dokumentu**
- **Spúšťač:** Úspešné dokončenie podpisovacieho procesu na tablete
- **Zodpovedná Rola:** Systém (automatizované cez kuriérsku aplikáciu)
- **Proces:** Balík podpísaného dokumentu je nahraný z tabletu na server
- **Spracovávané Dáta:** Podpísané PDF, dáta podpisu, fotografie, metadáta, časová značka dokončenia
- **Validácia:** Kontroly integrity súborov, validácia podpisu, overenie úplnosti
- **Integračné Body:** API nahrávania mobilnej aplikácie, S3 úložisko, aktualizácie databázy

**Krok 7: Validácia Nahratia**
- **Spúšťač:** Dokončenie nahratia podpísaného dokumentu
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Systém validuje nahrané podpísané dokumenty na úplnosť a integritu
- **Kritériá Úspechu:** Všetky požadované podpisy prítomné, súbory nie sú poškodené, metadáta úplné
- **Chybové Scenáre:** Poškodené súbory, chýbajúce podpisy, neúplné dáta → dokument presunutý do karantény

**Krok 8A: Úspešné Spracovanie**
- **Spúšťač:** Úspešná validácia nahratia
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Stav dokumentu aktualizovaný na "podpísaný", vytvorený záznam histórie, odoslané notifikácie
- **Spracovávané Dáta:** Aktualizácia stavu, časová značka dokončenia, záznam audit trail
- **Integračné Body:** Notifikačný systém, audit logovanie, systémy zákazníckych reportov

**Krok 8B: Spracovanie Karantény**
- **Spúšťač:** Zlyhanie validácie nahratia
- **Zodpovedná Rola:** Systém (automatizované), Manažér (manuálne preskúmanie)
- **Proces:** Dokument presunutý do karantény na manuálne preskúmanie a riešenie
- **Spracovávané Dáta:** Detaily chyby, časová značka karantény, dôvod zlyhania
- **Riešenie:** Manažér preskúma problém, buď ho vyrieši a vráti do workflow alebo označí na zmazanie

**Krok 9: Získanie Dokumentu**
- **Spúšťač:** Dokument úspešne spracovaný a označený ako podpísaný
- **Zodpovedná Rola:** Operátor
- **Proces:** Operátor stiahne dokončené podpísané dokumenty z web dashboardu
- **Spracovávané Dáta:** Balík podpísaného dokumentu, metadáta dokončenia, audit informácie
- **Obchodné Pravidlá:** Iba autorizovaní operátori môžu stiahnuť podpísané dokumenty
- **Integračné Body:** Web dashboard, S3 úložisko, audit logovanie

#### Obchodné Pravidlá
- Dokumenty musia dodržiavať schémy a validačné pravidlá špecifické pre zákazníka
- Iba aktívni kuriéri môžu byť priradení k dokumentom
- Overenie totožnosti je povinné pre zmluvy s vysokou hodnotou
- Všetky akcie sú logované pre audit compliance
- Dokumenty majú konfigurovateľné retenčné obdobia
- Neúspešné nahratia po 3 pokusoch sú automaticky umiestnené do karantény

#### Integračné Body
- **AWS S3:** Ukladanie a získavanie dokumentov
- **Mobilné Aplikácie:** Stiahnutie a nahratie dokumentov kuriérom
- **Zákaznícke Systémy:** Integrácia metadát dokumentov a trackingu
- **Notifikačné Služby:** Emailové upozornenia na dokončenie/zlyhania procesov
- **Audit Systém:** Kompletné sledovanie akcií a compliance logovanie

#### Kritériá Úspechu
- Dokument úspešne nahraný a validovaný
- Priradenie kuriéra dokončené bez chýb
- Podpis zákazníka zachytený s požadovaným overením totožnosti
- Podpísaný dokument nahraný a úspešne validovaný
- Dokument označený ako dokončený a dostupný na stiahnutie operátorom
- Všetky kroky procesu zalogované v audit trail

### 5.2 Proces Priradenia Kuriéra

#### Prehľad Procesu
Proces Priradenia Kuriéra spravuje alokáciu nepodpísaných dokumentov doručovateľom. Tento proces podporuje ako manuálne priradenie operátormi, tak automatické samo-priradenie kuriérmi prostredníctvom skenovania čiarových kódov, zabezpečujúc flexibilnú a efektívnu distribúciu dokumentov pri zachovaní správneho sledovania a zodpovednosti.

#### Mermaid Diagram
```mermaid
flowchart TD
    A[Nepodpísaný dokument dostupný] --> B{Metóda priradenia}
    B -->|Manuálne| C[Operátor priradí konkrétnemu kuriérovi]
    B -->|Automaticky| D[Kuriér naskenuje QR/čiarový kód]
    C --> E[Dokument pridaný do driver listu kuriéra]
    D --> E
    E --> F[Vytvorený záznam histórie]
    F --> G[Kuriér upozornený na nové priradenie]
```

#### Podrobný Popis Krokov

**Krok 1: Kontrola Dostupnosti Dokumentu**
- **Spúšťač:** Dokument úspešne nahraný a uložený v systéme
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Systém overuje, že dokument je v stave "nepodpísaný" a pripravený na priradenie
- **Spracovávané Dáta:** Stav dokumentu, informácie o zákazníkovi, miesto doručenia, úroveň priority
- **Obchodné Pravidlá:** Iba dokumenty so stavom "nepodpísaný" môžu byť priradené; dokumenty musia prejsť počiatočnou validáciou
- **Spracovanie Chýb:** Dokumenty s validačnými chybami zostávajú nepriraditeľné až do vyriešenia problémov

**Krok 2: Výber Metódy Priradenia**
- **Spúšťač:** Identifikovaný dostupný dokument na priradenie
- **Zodpovedná Rola:** Operátor alebo Kuriér
- **Proces:** Systém určuje metódu priradenia na základe toho, ako je priradenie iniciované
- **Kritériá Rozhodnutia:**
  - **Manuálne Priradenie:** Operátor vyberie dokument z dashboardu a zvolí konkrétneho kuriéra
  - **Automatické Priradenie:** Kuriér používa mobilnú aplikáciu na skenovanie QR kódu alebo čiarového kódu dokumentu
- **Obchodné Pravidlá:** Metóda priradenia závisí od operačného workflow a preferencií kuriérskej spoločnosti

**Krok 3A: Manuálne Priradenie Operátorom**
- **Spúšťač:** Operátor vyberie dokument na manuálne priradenie
- **Zodpovedná Rola:** Operátor
- **Proces:** Operátor používa web dashboard na výber dokumentu a priradenie konkrétnemu kuriérovi
- **Spracovávané Dáta:** ID dokumentu, ID vybraného kuriéra, dôvod priradenia, ID operátora
- **Validačné Kontroly:**
  - Kuriér musí byť aktívny a nezmazaný
  - Kuriér musí patriť k príslušnému doručovaciemu centru
  - Dokument nesmie byť už priradený
  - Kuriér musí mať kapacitu na dodatočné priradenia
- **Spracovanie Chýb:** Priradenie odmietnuté ak validácia zlyhá; operátor dostane špecifickú chybovú správu

**Krok 3B: Automatické Priradenie Kuriérom**
- **Spúšťač:** Kuriér naskenuje QR kód alebo čiarový kód dokumentu pomocou mobilnej aplikácie
- **Zodpovedná Rola:** Kuriér
- **Proces:** Kuriér používa kameru mobilnej aplikácie na skenovanie identifikátora dokumentu
- **Spracovávané Dáta:** Dáta naskenovaného čiarového kódu/QR kódu, ID kuriéra, časová značka skenovania, lokačné dáta
- **Validačné Kontroly:**
  - Čiarový kód/QR kód musí byť platný a zodpovedať existujúcemu dokumentu
  - Dokument musí byť v stave "nepodpísaný"
  - Kuriér musí byť aktívny a autorizovaný
  - Dokument musí byť dostupný na priradenie (nie už priradený)
- **Spracovanie Chýb:** Neplatné skenovania zobrazujú chybovú správu; kuriér môže opakovať alebo kontaktovať operátora

**Krok 4: Aktualizácia Driver Listu**
- **Spúšťač:** Úspešná validácia priradenia (buď manuálne alebo automatické)
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Dokument je pridaný do aktívneho driver listu kuriéra
- **Spracovávané Dáta:** Vzťah kuriér-dokument, časová značka priradenia, poradie priority
- **Obchodné Pravidlá:** Dokumenty sa zobrazujú v driver liste kuriéra v poradí priradenia; vysokoprioritné dokumenty môžu byť označené
- **Integračné Body:** Synchronizácia mobilnej aplikácie, aktualizácie API driver listu

**Krok 5: Vytvorenie Záznamu Histórie**
- **Spúšťač:** Úspešná aktualizácia driver listu
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Systém vytvorí audit trail záznam akcie priradenia
- **Spracovávané Dáta:** Typ akcie priradenia (ASSIGN), ID dokumentu, ID kuriéra, ID operátora (ak manuálne), časová značka
- **Obchodné Pravidlá:** Všetky akcie priradenia musia byť logované pre compliance a sledovanie
- **Integračné Body:** Systém audit logovania, databáza sledovania histórie

**Krok 6: Notifikácia Kuriéra**
- **Spúšťač:** Záznam histórie úspešne vytvorený
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Kuriér dostane notifikáciu o novom priradení dokumentu
- **Metódy Notifikácie:**
  - Push notifikácia mobilnej aplikácie
  - Aktualizácia driver listu v aplikácii
  - Voliteľná SMS notifikácia (konfigurovateľná)
- **Spracovávané Dáta:** Súhrn dokumentu, lokácia zákazníka, úroveň priority, časová značka priradenia
- **Obchodné Pravidlá:** Notifikácie odosielané iba aktívnym kuriérom; preferencie notifikácií konfigurovateľné

#### Obchodné Pravidlá
- Iba aktívni, nezmazaní kuriéri môžu dostávať priradenia
- Kuriéri môžu byť priradení iba k dokumentom pre ich určené doručovacie centrá
- Dokumenty nemôžu byť priradené viacerým kuriérom súčasne
- História priradení musí byť udržiavaná pre audit účely
- Vysokoprioritné dokumenty môžu prepísať normálne poradie priradenia
- Operátori môžu preradiť dokumenty ak je to potrebné (vytvorí nový záznam histórie)
- Automatické priradenie vyžaduje platné skenovanie čiarového kódu/QR kódu
- Limity kapacity priradenia môžu byť konfigurované pre každého kuriéra

#### Integračné Body
- **API Mobilnej Aplikácie:** Skenovanie čiarových kódov, aktualizácie driver listu, notifikácie
- **Web Dashboard:** Rozhranie manuálneho priradenia, výber kuriéra
- **Audit Systém:** Logovanie akcií priradenia a sledovanie histórie
- **Notifikačné Služby:** Push notifikácie, SMS upozornenia
- **Databáza:** Vzťahy kuriér-dokument, časové značky priradenia
- **Lokačné Služby:** GPS sledovanie pre validáciu priradenia (voliteľné)

#### Kritériá Úspechu
- Dokument úspešne priradený aktívnemu kuriérovi
- Metóda priradenia (manuálne/automatické) správne zaznamenaná
- Driver list kuriéra aktualizovaný s novým dokumentom
- Záznam histórie vytvorený s kompletnými detailmi priradenia
- Kuriér upozornený na nové priradenie
- Priradenie viditeľné v web dashboarde aj mobilnej aplikácii
- Audit trail kompletný a v súlade s predpismi

### 5.3 Spracovanie Chýb a Karanténa

#### Prehľad Procesu
Proces Spracovanie Chýb a Karanténa spravuje dokumenty, ktoré narazili na chyby spracovania alebo validačné zlyhania počas podpisovacieho workflow. Tento proces zabezpečuje, že problematické dokumenty sú izolované, preskúmané a buď vyriešené alebo správne zlikvidované, udržiavajúc integritu systému a compliance pri poskytovaní jasných eskalačných ciest na riešenie problémov.

#### Mermaid Diagram
```mermaid
flowchart TD
    A[Chyba spracovania dokumentu] --> B[Detaily chyby zalogované]
    B --> C[Dokument presunutý do karantény]
    C --> D[Notifikácia odoslaná manažérom]
    D --> E[Iniciované manuálne preskúmanie]
    E --> F{Riešenie možné?}
    F -->|Áno| G[Dokument vrátený do workflow]
    F -->|Nie| H[Dokument označený na zmazanie]
    G --> I[Obnovené normálne spracovanie]
    H --> J[Vyčistenie po retenčnej dobe]
```

#### Podrobný Popis Krokov

**Krok 1: Detekcia Chyby**
- **Spúšťač:** Rôzne systémové procesy detekujú chyby spracovania dokumentov
- **Bežné Zdroje Chýb:**
  - Zlyhania validácie nahratia (poškodené súbory, chýbajúce metadáta)
  - Chyby podpisovacieho procesu (neúplné podpisy, validačné zlyhania)
  - Problémy integrity súborov (poškodené ZIP súbory, chýbajúce dokumenty)
  - Chyby validácie schémy (nesprávny formát zákazníckych dát)
  - Zlyhania systémovej integrácie (chyby S3 nahratia, databázové problémy)
- **Zodpovedná Rola:** Systém (automatická detekcia)
- **Spracovávané Dáta:** Typ chyby, chybová správa, ID dokumentu, časová značka, kontext procesu
- **Metódy Detekcie:** Automatické validačné kontroly, spracovanie výnimiek, overenie integrity

**Krok 2: Logovanie Chyby**
- **Spúšťač:** Detekcia chyby v akomkoľvek kroku spracovania dokumentu
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Komplexné detaily chyby sú zalogované do systémových logov a databázy
- **Spracovávané Dáta:**
  - Klasifikácia chyby (validácia, korupcia, integrácia, systém)
  - Detailná chybová správa a stack trace
  - Metadáta dokumentu a aktuálny stav
  - Používateľský kontext (kuriér, operátor) ak je aplikovateľný
  - Informácie o systémovom prostredí
- **Obchodné Pravidlá:** Všetky chyby musia byť zalogované s dostatočnými detailmi na riešenie problémov
- **Integračné Body:** Sentry sledovanie chýb, systémové logy, audit databáza

**Krok 3: Presun do Karantény**
- **Spúšťač:** Dokončenie logovania chyby
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Dokument je presunutý z aktívneho spracovania do stavu karantény
- **Spracovávané Dáta:** ID dokumentu, časová značka karantény, dôvod chyby, pôvodný stav
- **Databázové Zmeny:**
  - Stav dokumentu aktualizovaný na "quarantined"
  - Vytvorený záznam karantény s detailmi chyby
  - Zachované pôvodné dáta dokumentu na analýzu
- **Obchodné Pravidlá:** Dokumenty v karanténe sú izolované od normálneho workflow spracovania
- **Spracovanie Úložiska:** Súbory dokumentov zostávajú v S3 ale sú označené ako v karanténe

**Krok 4: Notifikácia Manažéra**
- **Spúšťač:** Dokument úspešne presunutý do karantény
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Notifikácie odoslané určeným manažérom a administrátorom
- **Obsah Notifikácie:**
  - ID dokumentu a informácie o zákazníkovi
  - Typ a popis chyby
  - Časová značka karantény
  - Odporúčané akcie
  - Odkaz na dashboard karantény
- **Metódy Notifikácie:**
  - Emailové upozornenia na konfigurované adresy
  - Dashboard notifikácie
  - Voliteľné SMS upozornenia pre kritické chyby
- **Obchodné Pravidlá:** Notifikácie odoslané okamžite pre vysokoprioritné chyby; zoskupené pre rutinné problémy

**Krok 5: Iniciácia Manuálneho Preskúmania**
- **Spúšťač:** Manažér dostane notifikáciu o karanténe
- **Zodpovedná Rola:** Manažér alebo Administrátor
- **Proces:** Manažér pristupuje k dashboardu karantény na preskúmanie problematického dokumentu
- **Aktivity Preskúmania:**
  - Preskúmanie detailov chyby a logov
  - Analýza obsahu dokumentu a metadát
  - Kontrola stavu systému a integračných bodov
  - Určenie základnej príčiny zlyhania
  - Posúdenie možností riešenia
- **Prístupné Dáta:** Kompletný balík dokumentu, logy chýb, história spracovania, stav systému
- **Používané Nástroje:** Dashboard karantény, nástroje analýzy logov, prehliadače dokumentov

**Krok 6: Rozhodnutie o Riešení**
- **Spúšťač:** Dokončenie manuálneho preskúmania
- **Zodpovedná Rola:** Manažér alebo Administrátor
- **Proces:** Manažér určuje, či dokument môže byť vyriešený alebo musí byť zmazaný
- **Kritériá Rozhodnutia:**
  - **Riešiteľné Problémy:**
    - Dočasné systémové zlyhania (možný opakovaný pokus)
    - Opraviteľné problémy formátu dát
    - Chýbajúce nekritické informácie
    - Obnoviteľná korupcia súborov
  - **Neriešiteľné Problémy:**
    - Trvalá korupcia súborov
    - Kritické chýbajúce dáta
    - Porušenia compliance
    - Stiahnutie súhlasu zákazníkom
- **Dokumentácia:** Rozhodnutie o riešení a odôvodnenie musí byť zdokumentované

**Krok 7A: Riešenie a Návrat Dokumentu**
- **Spúšťač:** Manažér určí, že dokument je riešiteľný
- **Zodpovedná Rola:** Manažér alebo Systém (automatický opakovaný pokus)
- **Proces:** Dokument je opravený a vrátený do normálneho workflow spracovania
- **Akcie Riešenia:**
  - Oprava alebo dokončenie dát
  - Oprava alebo náhrada súborov
  - Opravy konfigurácie systému
  - Manuálne prepísanie spracovania
- **Aktualizácie Dát:** Stav dokumentu zmenený z "quarantined" na príslušný stav workflow
- **Validácia:** Vyriešený dokument musí prejsť všetkými validačnými kontrolami pred návratom do workflow

**Krok 7B: Označenie Dokumentu na Zmazanie**
- **Spúšťač:** Manažér určí, že dokument nemôže byť vyriešený
- **Zodpovedná Rola:** Manažér
- **Proces:** Dokument je označený na trvalé zmazanie
- **Aktualizácie Dát:**
  - Stav dokumentu zmenený na "marked_for_deletion"
  - Zaznamenaný dôvod a časová značka zmazania
  - Vypočítané retenčné obdobie na základe compliance požiadaviek
- **Obchodné Pravidlá:** Zmazanie musí byť v súlade s politikami uchovávania dát a právnymi požiadavkami
- **Schválenie:** Dokumenty s vysokou hodnotou môžu vyžadovať dodatočné schválenie na zmazanie

**Krok 8A: Obnovenie Normálneho Spracovania**
- **Spúšťač:** Dokument úspešne vrátený do workflow
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Dokument pokračuje normálnym podpisovacím workflow z príslušného kroku
- **Monitoring:** Vyriešené dokumenty sú monitorované na opakujúce sa problémy
- **Aktualizácia Histórie:** Akcia riešenia zaznamenaná v histórii dokumentu
- **Notifikácia:** Relevantné strany upozornené na úspešné riešenie

**Krok 8B: Vyčistenie Retenčného Obdobia**
- **Spúšťač:** Retenčné obdobie vyprší pre zmazané dokumenty
- **Zodpovedná Rola:** Systém (automatizovaný cleanup job)
- **Proces:** Trvalé odstránenie súborov dokumentov a databázových záznamov
- **Akcie Vyčistenia:**
  - Zmazanie S3 súborov
  - Odstránenie databázových záznamov
  - Archivácia audit logov
  - Compliance dokumentácia
- **Obchodné Pravidlá:** Vyčistenie sa vykonáva iba po vypršaní právnych retenčných období
- **Overenie:** Akcie vyčistenia sú logované a overené pre compliance

#### Obchodné Pravidlá
- Všetky chyby spracovania musia byť zalogované s kompletným kontextom
- Dokumenty v karanténe sú izolované od normálneho workflow
- Preskúmanie manažérom je požadované pre všetky riešenia karantény
- Rozhodnutia o zmazaní musia byť zdokumentované s odôvodnením
- Politiky uchovávania dát musia byť dodržané pre všetky zmazania
- Kritické chyby vyžadujú okamžitú notifikáciu
- Pokusy o riešenie sú obmedzené na zabránenie nekonečných slučiek
- Compliance požiadavky majú prednosť pred operačným pohodlím

#### Integračné Body
- **Sledovanie Chýb:** Sentry integrácia pre komplexné monitorovanie chýb
- **Notifikačné Služby:** Emailové a SMS upozornenia pre udalosti karantény
- **Audit Systém:** Kompletné sledovanie akcií karantény a riešenia
- **Úložisko Súborov:** S3 integrácia pre správu dokumentov v karanténe
- **Compliance Systémy:** Integrácia s právnymi a regulačnými požiadavkami
- **Dashboard Systémy:** Real-time stav karantény a správcovské rozhranie

#### Kritériá Úspechu
- Chyba správne detekovaná a klasifikovaná
- Kompletné detaily chyby zalogované na analýzu
- Dokument úspešne izolovaný v karanténe
- Príslušné notifikácie odoslané manažérom
- Manuálne preskúmanie dokončené so zdokumentovaným rozhodnutím
- Riešenie alebo zmazanie vykonané podľa obchodných pravidiel
- Audit trail kompletný a v súlade s predpismi
- Integrita systému udržaná počas celého procesu

### 5.4 Proces Správy Prieskumov

#### Prehľad Procesu
Proces Správy Prieskumov umožňuje zbieranie spätnej väzby zákazníkov a dát o spokojnosti prostredníctvom tablet-based prieskumov administrovaných kuriérmi počas doručovania. Tento proces umožňuje systematické zhromažďovanie poznatkov zákazníkov, monitoring kvality a iniciatívy zlepšovania služieb pri zachovaní integrity dát a poskytovaní komplexnej analytiky pre manažérske rozhodovanie.

#### Mermaid Diagram
```mermaid
flowchart TD
    A[Manažér vytvorí prieskum] --> B[Prieskum priradený kuriérom/centrám]
    B --> C[Prieskum sa zobrazí v driver liste kuriéra]
    C --> D[Zákazník vyplní prieskum na tablete]
    D --> E[Odpoveď na prieskum zaznamenaná]
    E --> F[Manažér preskúma výsledky prieskumu]
    F --> G[Vygenerované reporty na analýzu]
```

#### Podrobný Popis Krokov

**Krok 1: Vytvorenie Prieskumu**
- **Spúšťač:** Manažérske rozhodnutie zbierať spätnú väzbu zákazníkov alebo vykonať hodnotenie kvality
- **Zodpovedná Rola:** Manažér alebo Administrátor
- **Proces:** Manažér používa web dashboard na vytvorenie nového prieskumu so špecifickými parametrami
- **Spracovávané Dáta:**
  - Predmet a popis prieskumu
  - URL formulára pre tablet rozhranie
  - Cieľová skupina (konkrétni kuriéri alebo doručovacie centrá)
  - Trvanie prieskumu a nastavenia opakovania
  - Súbory balíka prieskumu (wizard rozhranie)
- **Možnosti Konfigurácie:**
  - Jednorazové alebo opakujúce sa prieskumy
  - Nasadenie špecifické pre kuriéra alebo celé centrum
  - Vlastné polia formulára a validačné pravidlá
  - Nastavenia brandingu a prezentácie
- **Validácia:** Konfigurácia prieskumu musí byť kompletná a URL formulára prístupná

**Krok 2: Priradenie Kuriérov a Centier**
- **Spúšťač:** Dokončenie vytvorenia prieskumu
- **Zodpovedná Rola:** Manažér
- **Proces:** Manažér priradí prieskum konkrétnym kuriérom alebo celým doručovacím centrám
- **Metódy Priradenia:**
  - **Individuálne Priradenie:** Výber konkrétnych kuriérov podľa mena/ID
  - **Priradenie Centra:** Priradenie všetkým kuriérom vo vybraných doručovacích centrách
  - **Hybridné Priradenie:** Kombinácia individuálneho a centrum-based priradenia
- **Spracovávané Dáta:** ID kuriérov, ID centier, časové značky priradenia, parametre prieskumu
- **Obchodné Pravidlá:**
  - Iba aktívni kuriéri dostávajú priradenia prieskumov
  - Prieskumy môžu byť priradené viacerým kuriérom súčasne
  - Zmeny priradenia sú sledované v audit logoch
- **Validácia:** Priradení kuriéri musia byť aktívni a patriť k platným centrám

**Krok 3: Integrácia Driver Listu**
- **Spúšťač:** Dokončenie priradenia prieskumu
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Prieskum sa zobrazí v driver listoch priradených kuriérov spolu s regulárnymi dokumentmi
- **Synchronizácia Dát:**
  - Metadáta prieskumu synchronizované do mobilných aplikácií
  - Balík prieskumu stiahnutý do kuriérskych tabletov
  - Driver list aktualizovaný s dostupnosťou prieskumu
  - Stav prieskumu sledovaný pre každého kuriéra
- **Obchodné Pravidlá:** Prieskumy sa zobrazujú v driver liste na základe priradenia a aktivačného stavu
- **Integračné Body:** API mobilnej aplikácie, synchronizácia driver listu, distribúcia balíka prieskumu

**Krok 4: Dokončenie Prieskumu Zákazníkom**
- **Spúšťač:** Kuriér iniciuje prieskum so zákazníkom počas doručovania
- **Zodpovedná Rola:** Zákazník (s asistenciou kuriéra)
- **Proces:** Zákazník dokončí prieskum pomocou tablet-based rozhrania
- **Vykonanie Prieskumu:**
  - Kuriér spustí prieskum z driver listu
  - Zákazník preskúma otázky a pokyny prieskumu
  - Zákazník poskytne odpovede pomocou dotykového rozhrania
  - Prieskum validuje úplnosť pred odoslaním
  - Zachytený digitálny podpis alebo potvrdenie
- **Zbierané Dáta:**
  - Odpovede na prieskum (hodnotenia, text, výber z možností)
  - Demografické informácie zákazníka (voliteľné)
  - Časová značka dokončenia a trvanie
  - Kontext kuriéra a doručovania
  - Podpis alebo súhlas zákazníka
- **Validácia:** Povinné polia musia byť vyplnené; aplikovaná validácia formátu dát

**Krok 5: Zaznamenanie Odpovede**
- **Spúšťač:** Zákazník dokončí a odošle prieskum
- **Zodpovedná Rola:** Systém (automatizované)
- **Proces:** Odpoveď na prieskum je zaznamenaná v databáze s kompletnými metadátami
- **Spracovanie Dát:**
  - Odpovede na prieskum uložené so šifrovaním pre citlivé dáta
  - Odpoveď prepojená s kuriérom, zákazníkom a kontextom doručovania
  - Stav dokončenia aktualizovaný v driver liste kuriéra
  - Vytvorený audit trail pre dokončenie prieskumu
- **Obchodné Pravidlá:**
  - Odpovede sú nemenné po odoslaní
  - Osobné dáta spracovávané podľa predpisov o ochrane súkromia
  - Úplnosť odpovede validovaná pred uložením
- **Integračné Body:** Databázové úložisko, šifrovacie služby, audit logovanie

**Krok 6: Preskúmanie Výsledkov**
- **Spúšťač:** Odpovede na prieskum akumulované v čase
- **Zodpovedná Rola:** Manažér alebo Administrátor
- **Proces:** Manažér pristupuje k výsledkom prieskumu cez web dashboard analytiku
- **Aktivity Preskúmania:**
  - Preskúmanie individuálnych odpovedí
  - Analýza agregovaných štatistík
  - Identifikácia trendov v časových obdobiach
  - Korelácia výkonu kuriérov
  - Metriky spokojnosti zákazníkov
- **Analýza Dát:**
  - Výpočty miery odpovedí
  - Štatistické súhrny (priemery, distribúcie)
  - Porovnávacia analýza naprieč kuriérmi/centrami
  - Trendová analýza založená na čase
  - Analýza segmentov zákazníkov
- **Vizualizácia:** Grafy, diagramy a súhrnné tabuľky pre interpretáciu dát

**Krok 7: Generovanie Reportov**
- **Spúšťač:** Manažér požiada formálnu analýzu prieskumu
- **Zodpovedná Rola:** Systém (automatizované generovanie reportov)
- **Proces:** Komplexné reporty generované na základe dát prieskumu
- **Typy Reportov:**
  - **Súhrnné Reporty:** Celkové skóre spokojnosti a kľúčové metriky
  - **Detailná Analýza:** Rozdelenie odpoveď-po-odpovedi s kontextom
  - **Trendové Reporty:** Zmeny výkonu v časových obdobiach
  - **Porovnávacie Reporty:** Porovnania výkonu kuriér/centrum
  - **Vlastné Reporty:** Filtrovaná analýza na základe špecifických kritérií
- **Možnosti Exportu:** PDF reporty, Excel tabuľky, CSV dátové exporty
- **Distribúcia:** Reporty môžu byť naplánované na automatické doručovanie stakeholderom

#### Obchodné Pravidlá
- Iba aktívne prieskumy sa zobrazujú v driver listoch kuriérov
- Zákazníci musia poskytnúť súhlas pred účasťou na prieskume
- Odpovede na prieskum sú dôverné a anonymizované v reportoch
- Opakujúce sa prieskumy majú konfigurovateľné frekvenčné limity
- Dokončenie prieskumu je voliteľné a nemôže zdržať doručovanie
- Dáta odpovedí musia byť v súlade s predpismi o ochrane súkromia (GDPR)
- Balíky prieskumov musia byť validované pred nasadením
- Neúplné prieskumy môžu byť uložené a obnovené (konfigurovateľné)

#### Integračné Body
- **Rozhranie Mobilnej Aplikácie:** Prezentácia prieskumu a zbieranie odpovedí
- **Web Dashboard:** Vytvorenie, správa a analytika prieskumov
- **Databázové Systémy:** Ukladanie a získavanie odpovedí
- **Analytický Engine:** Štatistická analýza a generovanie reportov
- **Notifikačné Služby:** Upozornenia na dokončenie prieskumu a distribúcia reportov
- **Compliance Súkromia:** Anonymizácia dát a správa súhlasov
- **Export Systémy:** Generovanie reportov a funkcionalita exportu dát

#### Kritériá Úspechu
- Prieskum úspešne vytvorený s kompletnou konfiguráciou
- Príslušní kuriéri/centrá priradení a upozornení
- Prieskum sa zobrazí v driver listoch kuriérov a je prístupný
- Zákazník dokončí prieskum so všetkými požadovanými informáciami
- Odpoveď úspešne zaznamenaná s kompletnými metadátami
- Dáta prieskumu dostupné na preskúmanie a analýzu manažmentom
- Reporty generované presne odrážajú zozbierané dáta
- Požiadavky na súkromie a compliance splnené počas celého procesu
- Poznatky z prieskumu prispievajú k iniciatívam zlepšovania služieb

## 6. Frontend Podpisovací Wizard

### 6.1 Architektúra Wizarda
Podpisovací wizard je React-based aplikácia, ktorá beží na Android tabletoch používaných kuriérmi počas doručovania.

**Kľúčové Komponenty:**
- **SigningProcess:** Hlavné podpisovacie rozhranie
- **FormTypes:** Dynamické generovanie formulárov na základe schém zákazníkov
- **AppState:** Globálna správa stavu pomocou Immutable.js
- **Operations:** Spracovanie súborov a manipulácia PDF

### 6.2 Tok Podpisovacieho Procesu
1. **Načítanie Dokumentu:** Wizard načíta nepodpísaný balík dokumentov
2. **Zobrazenie Formulára:** Vykreslené sú formuláre špecifické pre zákazníka
3. **Zbieranie Dát:** Zachytené sú informácie o zákazníkovi
4. **Podpisovanie Dokumentu:** PDF dokumenty sú digitálne podpísané
5. **Vytvorenie Balíka:** Podpísané dokumenty sú zabalené s metadátami
6. **Nahratie:** Dokončený balík je nahraný na server

### 6.3 Podporované Operácie
- Vyplňovanie PDF formulárov s dátami zákazníka
- Aplikácia digitálneho podpisu
- Funkcionalita náhľadu dokumentu
- Podpora podpisovania viacerých dokumentov
- Offline schopnosť so synchronizáciou pri online pripojení

## 7. Integračné Body

### 7.1 Integrácia AWS Služieb
- **S3 Storage:** Ukladanie súborov dokumentov s verziovaním
- **SES Email:** Automatizované notifikácie a reporty
- **CloudWatch:** Monitoring a logovanie (cez Sentry)

### 7.2 Systémy Kuriérskych Spoločností
- **Telekom:** Vlastná schéma a integrácia notifikácií
- **Orange:** Špecializované formáty dokumentov a workflow
- **O2:** Špecifické validačné pravidlá a procesy
- **Union:** Vlastné reportovanie a export dát
- **Packeta/InTime:** Integrácie doručovacích spoločností

### 7.3 Externé API
- **Validácia Certifikátov:** Správa digitálnych certifikátov
- **Notifikačné Služby:** SMS a email doručovanie
- **Tracking Systémy:** Integrácia sledovania balíkov

## 8. Bezpečnosť a Autentifikácia

### 8.1 Mechanizmy Autentifikácie
- **Session-based:** Autentifikácia web dashboardu
- **Token-based:** Autentifikácia mobilných aplikácií
- **Basic Auth:** API prístup pre externé systémy
- **Fail2ban:** Ochrana proti brute force útokom

### 8.2 Bezpečnostné Opatrenia
- **HTTPS:** Všetka komunikácia šifrovaná
- **Správa Certifikátov:** Digitálne podpisovacie certifikáty
- **Kontrola Prístupu:** Oprávnenia založené na rolách
- **Audit Logging:** Kompletné sledovanie akcií
- **Šifrovanie Dát:** Ochrana citlivých dát

### 8.3 Compliance Funkcie
- **Audit Trail:** Kompletná história všetkých akcií
- **Retencia Dát:** Konfigurovateľné retenčné politiky
- **Export Kontroly:** Bezpečná funkcionalita exportu dát
- **Ochrana Súkromia:** GDPR compliance funkcie

## 9. Systémová Administrácia

### 9.1 Správa Konfigurácie
- **Systém Nastavení:** Dynamická konfigurácia cez web rozhranie
- **Správa Schém:** Schémy dokumentov špecifické pre zákazníka
- **Pravidlá Notifikácií:** Konfigurovateľné prahy upozornení
- **Retenčné Politiky:** Konfigurácia automatizovaného čistenia

### 9.2 Monitoring a Údržba
- **Health Checks:** Monitoring stavu systému
- **Metriky Výkonu:** Sledovanie času odozvy a priepustnosti
- **Sledovanie Chýb:** Sentry integrácia pre monitoring chýb
- **Zálohovacie Systémy:** Automatizované procedúry zálohovania dát

### 9.3 Cron Joby a Automatizácia
- **Čistenie Dokumentov:** Automatizované odstránenie starých dokumentov
- **Spracovanie Notifikácií:** Plánované doručovanie notifikácií
- **Generovanie Reportov:** Automatizované vytváranie reportov
- **Správa Archívu:** Procesy archivácie dokumentov

## 10. Technické Detaily Implementácie

### 10.1 Databázová Schéma
- **PostgreSQL:** Primárna databáza s podporou JSON polí
- **Migrácie:** Django migračný systém pre zmeny schémy
- **Indexovanie:** Optimalizované indexy pre výkon
- **Obmedzenia:** Vynucovanie integrity dát

### 10.2 Architektúra Úložiska Súborov
- **S3 Buckety:** Organizované podľa zákazníka a typu dokumentu
- **Verziovanie:** Správa verzií dokumentov
- **Šifrovanie:** Server-side šifrovanie pre citlivé dáta
- **Kontrola Prístupu:** IAM-based správa prístupu

### 10.3 Optimalizácia Výkonu
- **Caching:** Redis/Memcached pre session a dátové cache
- **Optimalizácia Databázy:** Optimalizácia dotazov a connection pooling
- **CDN Integrácia:** Optimalizácia doručovania statických assetov
- **Load Balancing:** Podpora multi-instance deploymentu

## Záver

Axepto predstavuje komplexné riešenie pre digitálne podpisovanie dokumentov v kuriérskom priemysle. Systém kombinuje moderné technológie s robustnými obchodnými procesmi na poskytnutie spoľahlivej, škálovateľnej a bezpečnej platformy pre správu dokumentov a ich podpisovania.

Kľúčové výhody systému zahŕňajú:
- **Efektívnosť:** Automatizácia procesov a zníženie manuálnej práce
- **Spoľahlivosť:** Robustné error handling a audit trail
- **Škálovateľnosť:** Multi-tenant architektúra podporujúca viacero zákazníkov
- **Bezpečnosť:** Komplexné bezpečnostné opatrenia a compliance
- **Flexibilita:** Konfigurovateľné workflow a integračné možnosti

Systém je navrhnutý tak, aby podporoval rastúce potreby kuriérskych spoločností a poskytoval základ pre budúce rozšírenia a vylepšenia.
