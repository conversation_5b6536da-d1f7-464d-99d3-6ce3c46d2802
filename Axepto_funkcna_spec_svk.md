# Axepto - Funkč<PERSON>if<PERSON> (Slovenčina)

## 1. Prehľad Aplikácie

### 1.1 Účel a Hlavné Funkcie
Axepto je komplexný systém na správu podpisovania dokumentov a doručovania pre kuriérske spoločnosti. Aplikácia umožňuje digitálne podpisovanie dokumentov počas doručovania balíkov a poskytuje kompletný workflow od nahratia dokumentu až po získanie podpísaného dokumentu.

**Kľúčové Funkcie:**
- Digitálne podpisovanie dokumentov cez tablet rozhranie
- Podpora viacerých kuriérskych spoločností (multi-tenant)
- Správa workflow dokumentov
- Sledovanie v reálnom čase a notifikácie
- Audit logging a compliance
- Systém prieskumov a spätnej väzby
- Správa karantény pre problematické dokumenty

### 1.2 Technologický Stack
- **Backend:** Django 3.2.8 (Python web framework)
- **Databáza:** PostgreSQL s podporou JSON polí
- **Úložisko:** AWS S3 pre ukladanie dokumentov
- **Frontend:** React-based podpisovací wizard pre tablety
- **Web Rozhranie:** Django templates s Bootstrap
- **Autentifikácia:** Django autentifikácia s fail2ban ochranou
- **Email:** AWS SES pre notifikácie
- **Monitoring:** Sentry pre sledovanie chýb
- **Deployment:** Docker s uWSGI a Nginx

### 1.3 Prehľad Architektúry
Systém využíva viacvrstvovú architektúru:
- **REST API Vrstva:** Spracováva integráciu mobilných aplikácií a operátorských systémov
- **Web Rozhranie Vrstva:** Poskytuje dashboard pre operátorov, manažérov a klientov
- **Business Logic Vrstva:** Spravuje workflow dokumentov a obchodné pravidlá
- **Dátová Vrstva:** PostgreSQL databáza s audit loggingom
- **Storage Vrstva:** AWS S3 pre súbory dokumentov
- **Integračná Vrstva:** Externé systémy kuriérskych spoločností

## 2. Dátové Modely a Entity

### 2.1 Základné Modely

#### Document (Dokument)
Centrálna entita reprezentujúca balík/dokument v systéme.
- **Polia:** id, client_id, timestamp, rest (JSON metadata), tracking (JSON), parent (self-reference), signed_locally, signed_remotely, author, customer, version, zip_size, deleted, hard_delete
- **Stavy:** unsigned (nepodpísaný) → signed/error (podpísaný/chyba)
- **Vzťahy:** Parent-child pre nepodpísané/podpísané verzie, patrí k Customer, priradený k Courier

#### Customer (Zákazník)
Reprezentuje klientov kuriérskych spoločností (Telekom, Orange, O2, Union, atď.)
- **Polia:** id (primárny kľúč), name, shipper_id, limity pre odstránenie rôznych typov dokumentov, has_password_protected_packages, active
- **Účel:** Multi-tenant podpora pre rôzne kuriérske spoločnosti

#### Courier (Kuriér)
Reprezentuje doručovateľov, ktorí podpisujú dokumenty
- **Polia:** id, user (OneToOne s Django User), documents (ManyToMany), active, center, deleted
- **Funkcionalita:** Priradenie dokumentov, správa driver listu, priradenie k centru

#### History (História)
Sleduje všetky dôležité akcie v systéme
- **Akcie:** ASSIGN, DELETE, DOWNLOAD, LOGIN_REST, SIGNED_REMOTELY, ACKNOWLEDGED
- **Polia:** author, user, document, created_at, action, auto (boolean pre automatické akcie)

#### Survey (Prieskum)
Systém prieskumov spokojnosti zákazníkov
- **Polia:** subject, couriers, courier_centers, active, repeating, description, form_url, package
- **Účel:** Zbieranie spätnej väzby od zákazníkov po doručení

#### Quarantine (Karanténa)
Spracováva problematické dokumenty, ktoré nemožno normálne spracovať
- **Polia:** id, timestamp, zip_size, url_id, courier, customer, deleted
- **Účel:** Izolácia a správa dokumentov s chybami spracovania

### 2.2 Používateľské Roly a Oprávnenia

#### Courier (Kuriér)
- Prístup k driver listu (priradené dokumenty)
- Sťahovanie nepodpísaných dokumentov
- Nahrávanie podpísaných dokumentov
- Odosielanie odpovedí na prieskumy
- Potvrdenie prijatia dokumentu

#### Operator (Operátor)
- Nahrávanie nepodpísaných dokumentov
- Priradenie dokumentov kuriérom
- Zobrazenie zoznamov podpísaných/nepodpísaných dokumentov
- Správa životného cyklu dokumentov

#### Manager (Manažér)
- Plný prístup k systému
- Zobrazenie všetkých reportov a analytík
- Správa kuriérov a prieskumov
- Prístup ku karanténe a audit logom
- Konfigurácia systému

#### Client (Klient)
- Zobrazenie dokumentov a informácií o kuriéroch
- Prístup k reportom a súhrnom
- Správa účtov kuriérov

## 3. REST API Endpointy

### 3.1 Autentifikačné Endpointy
```
POST /login/ - Autentifikácia používateľa (Basic Auth alebo form-based)
POST /logout/ - Odhlásenie používateľa
GET /auth/ - Získanie session tokenu pre kuriérske aplikácie
```

### 3.2 Endpointy pre Správu Dokumentov
```
GET /active/ - Získanie priradených dokumentov kuriéra (driver list)
POST /active/add/ - Priradenie dokumentu kuriérovi skenovaním
GET /document/unsigned/{id}/ - Stiahnutie nepodpísaného dokumentu
POST /document/unsigned/ - Nahratie nepodpísaného dokumentu (operátor)
GET /document/signed/{id}/ - Stiahnutie podpísaného dokumentu
POST /document/signed/{id}/ - Nahratie podpísaného dokumentu (kuriér)
POST /document/unsigned/{id}/ack/ - Potvrdenie prijatia dokumentu
POST /document/mark-signed/ - Označenie dokumentu ako vzdialene podpísaného
```

### 3.3 Endpointy pre Synchronizáciu Dát
```
GET /document/signed/since/{timestamp}/ - Zoznam podpísaných dokumentov od časovej značky
GET /document/unsigned/since/{timestamp}/ - Zoznam nepodpísaných dokumentov od časovej značky
```

### 3.4 Priradenie a Správa
```
POST /assign/ - Priradenie dokumentov kuriérom (operátor)
POST /assign2/ - Hromadné priradenie endpoint
```

### 3.5 Logovanie a Prieskumy
```
POST /logs/ - Odoslanie kuriérskych logov
GET /logs/{id}/ - Získanie logov (manažér)
POST /survey/ - Odoslanie odpovedí na prieskum
```

### 3.6 Správa Používateľov
```
POST /register/ - Registrácia nového kuriéra
POST /changepassword/ - Zmena hesla kuriéra
```

## 4. Funkcionalita Web Dashboardu

### 4.1 Autentifikácia a Prihlásenie

#### Prihlasovacia Stránka (`/web/login/`)
**Účel:** Bezpečný prístup k web dashboardu pre všetky typy používateľov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────┐
│              AXEPTO LOGO            │
├─────────────────────────────────────┤
│  Používateľské meno: [____________] │
│  Heslo: [________________________] │
│                                     │
│           [Prihlásiť sa]            │
└─────────────────────────────────────┘
```

**Funkcionalita:**
- Centrovaná prihlasovacia karta s logom spoločnosti
- Polia pre používateľské meno a heslo s validáciou
- Fail2ban ochrana proti brute force útokom
- Automatické presmerovanie na dashboard po úspešnom prihlásení
- Podpora "next" parametra pre presmerovanie na zamýšľanú stránku
- Téma-aware logo (podpora svetlého/tmavého režimu)

**Používateľská Skúsenosť:**
- Čisté, profesionálne rozhranie
- Responzívny dizajn pre rôzne veľkosti obrazoviek
- Chybové správy pre neplatné prihlasovacie údaje
- Správa relácií s konfigurovateľným timeoutom

### 4.2 Hlavný Dashboard (`/web/dashboard/`)

#### Prehľad Dashboardu
**Účel:** Centrálne centrum pre správu a monitoring dokumentov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Hľadať: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Zoznam Balíkov                                              │
│ Prednastavené filtre:                                       │
│ [Posledný Pracovný Deň] | [Tento Týždeň] | [Minulý Týždeň] │
├─────────────────────────────────────────────────────────────┤
│ Filtre: [Začiatok] [Koniec] [Stav▼] [Kuriér▼] [Zákazník▼] │
│         [Zobraziť] [Export] [Vypočítať]                    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID        │ ID Klienta│ Stav    │ Kuriér  │ Dátum     │ │
│ │ DOC001    │ CLI123    │ Podpísaný│ Ján D. │ 21/01/04  │ │
│ │ DOC002    │ CLI124    │ Nepodpís.│ Jana S.│ 21/01/04  │ │
│ │ DOC003    │ CLI125    │ Chyba   │ Bob M.  │ 21/01/04  │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 10 [Ďalšia >]               │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Vyhľadávací Panel:** Vyhľadávanie dokumentov v reálnom čase podľa ID, ID klienta alebo iných polí
- **Prednastavené Filtre:** Rýchly prístup k bežným dátumovým rozsahom
- **Pokročilé Filtrovanie:** Filtre pre dátumový rozsah, stav, kuriéra a zákazníka
- **Tabuľka Dokumentov:** Triediteľné stĺpce s indikátormi stavu
- **Stránkovanie:** Efektívna navigácia cez veľké množstvo dokumentov
- **Export Funkcionalita:** Excel export s aktuálnymi filtrami
- **Funkcia Výpočtu:** Výpočet štatistík v reálnom čase

**Indikátory Stavu:**
- 🟢 **Podpísaný:** Úspešne dokončené dokumenty
- 🟡 **Nepodpísaný:** Dokumenty čakajúce na podpis
- 🔴 **Chyba:** Dokumenty s problémami spracovania
- 🔵 **Priradený:** Dokumenty priradené kuriérom

### 4.3 Informácie o Dokumente (`/web/document_info/{id}/`)

#### Detailné Zobrazenie Dokumentu
**Účel:** Komplexný pohľad na jednotlivý dokument s úplnou históriou a metadátami.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [PODPÍSANÝ] [NEPODPÍSANÝ] [CHYBA] ← Záložky stavu          │
├─────────────────────────────────────────────────────────────┤
│ Detaily Dokumentu                                           │
│ ┌─────────────────┬─────────────────────────────────────┐   │
│ │ Základné Info   │ Metadáta                            │   │
│ │ ID: DOC001      │ Meno zákazníka: Ján Novák          │   │
│ │ ID Klienta: CLI │ Adresa: Hlavná 123                 │   │
│ │ Stav: Podpísaný │ Telefón: +421901234567             │   │
│ │ Veľkosť: 2.5 MB │ Email: <EMAIL>             │   │
│ │ Dátum: 21/01/04 │ Typ zmluvy: Internetová služba     │   │
│ └─────────────────┴─────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ Dokumenty:                                                  │
│ ✓ Zmluva PDF | Povinný | zmluva.pdf                        │
│ ✓ Sken OP | Povinný | op_sken.jpg                          │
│ ✓ Podpis | Povinný | podpis.pdf                            │
├─────────────────────────────────────────────────────────────┤
│ Skeny:                                                      │
│ ✓ Predná strana OP | Overenie totožnosti | Dokončené       │
│ ✓ Zadná strana OP | Overenie totožnosti | Dokončené        │
│ ✓ Foto zákazníka | Vizuálne potvrdenie | Dokončené         │
├─────────────────────────────────────────────────────────────┤
│ História Akcií:                                             │
│ 2024-01-04 10:30 - PRIRADENÝ kuriérovi Ján Novák          │
│ 2024-01-04 11:15 - STIAHNUTÝ kuriérom                     │
│ 2024-01-04 14:22 - PODPÍSANÝ zákazníkom                   │
│ 2024-01-04 14:25 - NAHRANÁ podpísaná verzia               │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Navigácia Stavu:** Záložky zobrazujúce progres dokumentu (nepodpísaný → podpísaný/chyba)
- **Zobrazenie Metadát:** Informácie o zákazníkovi parsované z metadát dokumentu
- **Správa Súborov:** Zoznam povinných a voliteľných dokumentov so stavom dokončenia
- **Overenie Skenov:** Vizuálne potvrdenie dokladov totožnosti a fotografií
- **Časová Os Histórie:** Kompletný audit trail všetkých akcií dokumentu
- **Odkazy na Stiahnutie:** Priamy prístup k súborom dokumentov (na základe oprávnení)
- **Tracking Dáta:** Časy spustenia/ukončenia wizarda a doba podpisovania (iba manažér)

### 4.4 Správa Kuriérov (`/web/couriers/`)

#### Zobrazenie Zoznamu Kuriérov
**Účel:** Komplexné rozhranie pre správu kuriérov pre administrátorov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ [☰] Hľadať: [________________] [🔍]                        │
├─────────────────────────────────────────────────────────────┤
│ Zoznam Kuriérov                                             │
│ Filtre: [Aktívny▼] [Centrum▼] [Zobraziť] [Export]         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │Meno     │ ID    │Priradené│Podpísané│Posl.prihl│Aktívny│ │ │
│ │jan.novak│JN001  │   15    │   12    │21/01/04  │  ✓   │Upr│ │
│ │jana.sm  │JS002  │    8    │    8    │20/01/04  │  ✓   │Upr│ │
│ │bob.mil  │BM003  │    3    │    1    │19/01/04  │  ✗   │Upr│ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 5 [Ďalšia >]                │
│                                    [Pridať Kuriéra]        │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Vyhľadávacia Funkcionalita:** Nájdenie kuriérov podľa mena alebo ID
- **Filtrovanie Stavu:** Filtrovanie podľa aktívneho/neaktívneho stavu a kuriérskeho centra
- **Metriky Výkonu:** Počty priradených vs. podpísaných dokumentov
- **Sledovanie Aktivity:** Časové značky posledného prihlásenia
- **Hromadné Operácie:** Export dát kuriérov do Excelu
- **Rýchle Akcie:** Priame odkazy na úpravu pre každého kuriéra

#### Formulár Pridania/Úpravy Kuriéra
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Pridať Nového Kuriéra                                       │
├─────────────────────────────────────────────────────────────┤
│ Používateľské meno: [________________]                     │
│ ID Kuriéra: [________________]                             │
│ Heslo: [________________]                                  │
│ Potvrdiť heslo: [________________]                         │
│ Centrum: [Vybrať centrum ▼]                                │
│ Aktívny: ☑ Povolený                                        │
│                                                             │
│ [Zrušiť] [Uložiť Kuriéra]                                 │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Vytvorenie Používateľa:** Automatické vytvorenie Django používateľského účtu
- **Validácia Hesla:** Konfigurovateľné požiadavky na silu hesla
- **Priradenie Centra:** Prepojenie kuriéra s konkrétnym doručovacím centrom
- **Správa Stavu:** Povolenie/zakázanie účtov kuriérov
- **Validácia:** Jedinečnosť používateľského mena a validácia ID kuriéra

### 4.5 Správa Prieskumov (`/web/surveys/`)

#### Zobrazenie Zoznamu Prieskumov
**Účel:** Správa prieskumov spokojnosti zákazníkov a zbieranie spätnej väzby.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Zoznam Prieskumov                                           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Názov                   │ Aktívny │ Kuriéri │          │ │
│ │ Spokojnosť zákazníkov   │    ✓    │   25    │   Upraviť│ │
│ │ Kontrola kvality služby │    ✗    │   12    │   Upraviť│ │
│ │ Skúsenosť s doručením   │    ✓    │   30    │   Upraviť│ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 3 [Ďalšia >]                │
│                                    [Pridať Prieskum]       │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Prehľad Prieskumov:** Zoznam všetkých prieskumov so stavom a počtom priradení
- **Aktívny Stav:** Povolenie/zakázanie prieskumov pre priradenie kuriérom
- **Priradenie Kuriérov:** Sledovanie počtu kuriérov s prístupom k jednotlivým prieskumom
- **Záznamy Prieskumov:** Kliknutie na názov prieskumu zobrazí odpovede a analytiku

#### Formulár Vytvorenia/Úpravy Prieskumu
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Vytvoriť Nový Prieskum                                      │
├─────────────────────────────────────────────────────────────┤
│ Predmet: [________________________________]               │
│ URL formulára: [________________________________]          │
│ Popis:                                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Prosím ohodnoťte vašu skúsenosť s doručením...          │ │
│ │ - Ako ste boli spokojní s časom doručenia?              │ │
│ │ - Bol kuriér profesionálny a zdvorilý?                 │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Opakovanie: [___] krát (voliteľné)                         │
│ Súbor balíka: [Vybrať súbor] prieskum_balik.zip           │
│ Aktívny: ☑ Povolený                                        │
│                                                             │
│ Priradení Kuriéri:                                          │
│ ☑ Ján Novák (JN001)    ☑ Jana Svoboda (JS002)             │
│ ☐ Bob Miller (BM003)   ☑ Alica Johnson (AJ004)            │
│                                                             │
│ Priradené Centrá:                                           │
│ ☑ Centrum 01 - Centrum  ☐ Centrum 02 - Predmestie         │
│                                                             │
│ [Zrušiť] [Uložiť Prieskum]                                 │
└─────────────────────────────────────────────────────────────┘
```

**Funkcionalita:**
- **Konfigurácia Prieskumu:** Nastavenie predmetu, popisu a URL formulára
- **Nahratie Balíka:** Balík prieskumového wizarda pre nasadenie na tablet
- **Priradenie Kuriérov:** Individuálne priradenie kuriéra alebo na základe centra
- **Nastavenia Opakovania:** Konfigurácia frekvencie prieskumu pre pravidelnú spätnú väzbu
- **Kontrola Stavu:** Povolenie/zakázanie dostupnosti prieskumu

### 4.6 Správa Karantény (`/web/quarantine/`)

#### Dashboard Karantény
**Účel:** Monitoring a správa dokumentov s chybami spracovania.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Dashboard Karantény                                         │
│ Zobraziť: [Všetky ▼] [Aktívne] [Zmazané]                  │
├─────────────────────────────────────────────────────────────┤
│ ⚠️  Dokumenty vyžadujúce pozornosť                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ID      │ Typ chyby         │ Dátum    │ Veľk.│ Akcia  │ │
│ │ DOC001  │ Neplatný podpis   │ 21/01/04 │ 2.1M │ Preskúm│ │
│ │ DOC002  │ Chýbajú metadáta  │ 21/01/04 │ 1.8M │ Preskúm│ │
│ │ DOC003  │ Poškodený ZIP     │ 20/01/04 │ 0.5M │ Zmazať │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 2 [Ďalšia >]                │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Klasifikácia Chýb:** Rôzne typy chýb so špecifickým spracovaním
- **Filtrovanie Stavu:** Zobrazenie všetkých, aktívnych alebo zmazaných položiek karantény
- **Monitoring Veľkosti:** Sledovanie využitia úložiska dokumentov v karanténe
- **Správa Akcií:** Možnosti preskúmania, obnovenia alebo trvalého zmazania
- **Automatické Čistenie:** Konfigurovateľné retenčné politiky

### 4.7 Systémové Logy (`/web/logs/`)

#### Dashboard Logov
**Účel:** Monitoring systémovej aktivity a akcií kuriérov.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Systémové Logy                                              │
├─────────────────────────────────────────────────────────────┤
│ 📊 Prehľad Aktivity                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Čas         │ Kuriér   │ Akcia     │ Dokument │ Veľk. │ │
│ │ 21/01/04 14:30│ jan.novak│ STIAHNUTIE│ DOC001   │ 2.1M │ │
│ │ 21/01/04 14:25│ jana.sm  │ NAHRATIE  │ DOC002   │ 1.8M │ │
│ │ 21/01/04 14:20│ bob.mil  │ PRIHLÁS.  │ -        │ -    │ │
│ │ 21/01/04 14:15│ alice.j  │ PRIRADENIE│ DOC003   │ 0.9M │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [< Predchádzajúca] Strana 1 z 50 [Ďalšia >]               │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Monitoring v Reálnom Čase:** Živý prehľad systémových aktivít
- **Sledovanie Akcií:** Operácie prihlásenia, stiahnutia, nahratia, priradenia
- **Aktivita Kuriérov:** Monitoring výkonu jednotlivých kuriérov
- **Sledovanie Veľkosti Súborov:** Monitoring objemov prenosu dát
- **Historická Analýza:** Analýza dlhodobých vzorov aktivity

### 4.8 Súhrnné Reporty (`/web/summary/`)

#### Dashboard Analytiky
**Účel:** Generovanie komplexných reportov a štatistík.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Súhrnné Reporty                                             │
│ Prednastavené filtre:                                       │
│ [Posledný Pracovný Deň] | [Tento Týždeň] | [Minulý Týždeň] │
├─────────────────────────────────────────────────────────────┤
│ Vlastný rozsah: [Začiatok] [Koniec] [Zákazník▼]           │
│ [Generovať Report] [Export Excel]                          │
├─────────────────────────────────────────────────────────────┤
│ 📈 Metriky Výkonu                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Typ Metriky      │ Počet │ Percento │ Trend           │ │
│ │ Podpísané Dok.   │ 1,245 │   87.5%  │ ↗ +5.2%        │ │
│ │ Nepodpísané Dok. │  178  │   12.5%  │ ↘ -2.1%        │ │
│ │ Miera Chýb       │   23  │    1.6%  │ ↘ -0.8%        │ │
│ │ Priem. Čas Spraco│ 2.3h  │    -     │ ↗ +0.2h        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📊 Rozdelenie Zákazníkov                                    │
│ Telekom: 45% | Orange: 28% | O2: 15% | Union: 12%         │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Prednastavené Obdobia:** Rýchly prístup k bežným reportovacím obdobiam
- **Vlastné Dátumové Rozsahy:** Flexibilné časové rámce reportovania
- **Filtrovanie Zákazníkov:** Multi-tenant reportovacie schopnosti
- **Metriky Výkonu:** Kľúčové ukazovatele výkonu s trendmi
- **Export Funkcionalita:** Excel export pre ďalšiu analýzu
- **Vizuálna Analytika:** Grafy a diagramy pre vizualizáciu dát

### 4.9 Správa Nastavení (`/web/settings/`)

#### Konfigurácia Systému
**Účel:** Konfigurácia systémových parametrov a obchodných pravidiel.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Nastavenia Systému                                          │
├─────────────────────────────────────────────────────────────┤
│ 🔧 Konfiguračné Parametre                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Názov Nastavenia          │ Hodnota      │ Typ  │ Upraviť│ │
│ │ DELETE_OLD_DOCUMENTS      │ 30 dní       │ INT  │ ✏️     │ │
│ │ NOTIFICATION_MAIL         │ admin@...    │ STR  │ ✏️     │ │
│ │ VALIDATE_PASSWORDS        │ True         │ BOOL │ ✏️     │ │
│ │ DELAY_NOTIFICATION_LIMIT  │ 5 dní        │ TIME │ ✏️     │ │
│ │ AWS_STORAGE_BUCKET_NAME   │ axepto-docs  │ STR  │ ✏️     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 📋 Schémy Zákazníkov                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Zákazník │ Verzia Schémy │ Posl. Aktualizácia │ Upraviť │ │
│ │ Telekom  │ v2.1.3        │ 21/01/04           │ Konfig. │ │
│ │ Orange   │ v1.8.7        │ 20/01/04           │ Konfig. │ │
│ │ O2       │ v2.0.1        │ 19/01/04           │ Konfig. │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Dynamická Konfigurácia:** Modifikácia parametrov za behu
- **Validácia Typov:** Vynucovanie správnych dátových typov
- **Správa Schém:** Schémy dokumentov špecifické pre zákazníka
- **Nastavenia Notifikácií:** Konfigurácia emailov a upozornení
- **Konfigurácia Úložiska:** AWS S3 a nastavenia správy súborov
- **Bezpečnostné Parametre:** Politiky hesiel a nastavenia autentifikácie

### 4.10 Audit a Bany (`/web/audit/`, `/web/bans/`)

#### Audit Dashboard
**Účel:** Sledovanie zmien systému a compliance.

**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Audit Systému                                               │
│ Status: [🟢 Aktívny] [Zapnúť/Vypnúť Audit]                 │
├─────────────────────────────────────────────────────────────┤
│ Export Dát: [Od Dátumu] [Do Dátumu] [Exportovať]           │
├─────────────────────────────────────────────────────────────┤
│ 📋 Posledné Záznamy                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Čas         │ Operátor │ Kuriér   │ Akcia            │ │
│ │ 21/01/04 15:30│ admin   │ jan.novak│ Nahratie dokumentu│ │
│ │ 21/01/04 15:25│ operator│ jana.sm  │ Priradenie       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Správa Banov
**Vizuálne Rozloženie:**
```
┌─────────────────────────────────────────────────────────────┐
│ Správa Banов                                                │
│ Hľadať: [________________] [🔍]                            │
│ Čas: [Od Dátumu] [Zobraziť]                                │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Používateľ │ IP Adresa    │ Od       │ Do       │ Akcia │ │
│ │ hacker123  │ ***********  │ 21/01/04 │ 22/01/04 │ Unban │ │
│ │ -          │ ********     │ 20/01/04 │ 21/01/04 │ Unban │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Kľúčové Funkcie:**
- **Audit Kontrola:** Zapnutie/vypnutie audit logovania
- **Export Funkcionalita:** CSV export audit záznamov
- **Ban Management:** Správa zakázaných používateľov a IP adries
- **Automatické Bany:** Fail2ban integrácia pre bezpečnosť
- **Unban Funkcionalita:** Manuálne odblokovanie používateľov

### 4.11 Diagramy Toku Používateľského Rozhrania

#### Tok Navigácie Hlavného Dashboardu

```mermaid
flowchart TD
    A[Prihlasovacia Stránka] --> B{Autentifikácia}
    B -->|Úspech| C[Hlavný Dashboard]
    B -->|Neúspech| A
    C --> D[Zoznam Dokumentov]
    C --> E[Hľadanie a Filtrovanie]
    C --> F[Export Dát]
    D --> G[Info o Dokumente]
    G --> H[Zobrazenie Histórie]
    G --> I[Stiahnutie Súborov]
    E --> J[Filtrované Výsledky]
    F --> K[Excel Export]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style G fill:#fff3e0
```

#### Workflow Správy Kuriérov

```mermaid
flowchart TD
    A[Zoznam Kuriérov] --> B[Hľadanie Kuriérov]
    A --> C[Pridať Nového Kuriéra]
    A --> D[Upraviť Existujúceho Kuriéra]
    B --> E[Filtrované Výsledky]
    C --> F[Formulár Kuriéra]
    D --> F
    F --> G{Validácia}
    G -->|Platný| H[Uložiť Kuriéra]
    G -->|Neplatný| I[Zobraziť Chyby]
    H --> J[Aktualizovať Zoznam]
    I --> F
    E --> K[Vybrať Kuriéra]
    K --> D

    style A fill:#e8f5e8
    style F fill:#fff3e0
    style H fill:#e1f5fe
```

#### Stavy Spracovania Dokumentov

```mermaid
stateDiagram-v2
    [*] --> Nahraný: Operátor nahráva dokument
    Nahraný --> Priradený: Priradenie kuriérovi
    Priradený --> Stiahnutý: Kuriér stiahne
    Stiahnutý --> Podpísaný: Zákazník podpíše na tablete
    Stiahnutý --> Chyba: Podpisovanie zlyhá
    Podpísaný --> Dokončený: Operátor stiahne
    Chyba --> Karanténa: Presun do karantény
    Karanténa --> Priradený: Vyriešenie a opätovné priradenie
    Karanténa --> Zmazaný: Trvalé odstránenie
    Dokončený --> [*]
    Zmazaný --> [*]
```

#### Proces Správy Prieskumov

```mermaid
flowchart TD
    A[Zoznam Prieskumov] --> B[Vytvoriť Prieskum]
    A --> C[Upraviť Prieskum]
    A --> D[Zobraziť Odpovede]
    B --> E[Formulár Prieskumu]
    C --> E
    E --> F[Priradiť Kuriérov]
    F --> G[Priradiť Centrá]
    G --> H[Nahrať Balík]
    H --> I{Validácia}
    I -->|Platný| J[Aktivovať Prieskum]
    I -->|Neplatný| K[Zobraziť Chyby]
    J --> L[Nasadiť na Tablety]
    K --> E
    D --> M[Dashboard Analytiky]

    style A fill:#f3e5f5
    style E fill:#fff3e0
    style J fill:#e8f5e8
```

#### Architektúra Systémových Komponentov

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Web Dashboard]
        B[React Signing Wizard]
        C[Mobile Courier App]
    end

    subgraph "Application Layer"
        D[Django Web Views]
        E[REST API Views]
        F[Business Logic]
    end

    subgraph "Data Layer"
        G[PostgreSQL Database]
        H[AWS S3 Storage]
        I[Audit Logs]
    end

    subgraph "External Services"
        J[AWS SES Email]
        K[Sentry Monitoring]
        L[Courier Company APIs]
    end

    A --> D
    B --> E
    C --> E
    D --> F
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style G fill:#e8f5e8
```

## 5. Obchodné Procesy a Workflow

### 5.1 Workflow Podpisovania Dokumentov

```mermaid
flowchart TD
    A[Operátor nahráva nepodpísaný dokument] --> B[Dokument uložený v S3]
    B --> C[Dokument priradený kuriérovi]
    C --> D[Kuriér stiahne dokument cez mobilnú aplikáciu]
    D --> E[Zákazník podpíše na tablete pomocou wizarda]
    E --> F[Podpísaný dokument nahraný do systému]
    F --> G{Nahratie úspešné?}
    G -->|Áno| H[Dokument označený ako podpísaný]
    G -->|Nie| I[Dokument presunutý do karantény]
    H --> J[Operátor stiahne podpísaný dokument]
    I --> K[Manuálne preskúmanie a riešenie]
```

### 5.2 Proces Priradenia Kuriéra

```mermaid
flowchart TD
    A[Nepodpísaný dokument dostupný] --> B{Metóda priradenia}
    B -->|Manuálne| C[Operátor priradí konkrétnemu kuriérovi]
    B -->|Automaticky| D[Kuriér naskenuje QR/čiarový kód]
    C --> E[Dokument pridaný do driver listu kuriéra]
    D --> E
    E --> F[Vytvorený záznam histórie]
    F --> G[Kuriér upozornený na nové priradenie]
```

### 5.3 Spracovanie Chýb a Karanténa

```mermaid
flowchart TD
    A[Chyba spracovania dokumentu] --> B[Detaily chyby zalogované]
    B --> C[Dokument presunutý do karantény]
    C --> D[Notifikácia odoslaná manažérom]
    D --> E[Iniciované manuálne preskúmanie]
    E --> F{Riešenie možné?}
    F -->|Áno| G[Dokument vrátený do workflow]
    F -->|Nie| H[Dokument označený na zmazanie]
    G --> I[Obnovené normálne spracovanie]
    H --> J[Vyčistenie po retenčnej dobe]
```

### 5.4 Proces Správy Prieskumov

```mermaid
flowchart TD
    A[Manažér vytvorí prieskum] --> B[Prieskum priradený kuriérom/centrám]
    B --> C[Prieskum sa zobrazí v driver liste kuriéra]
    C --> D[Zákazník vyplní prieskum na tablete]
    D --> E[Odpoveď na prieskum zaznamenaná]
    E --> F[Manažér preskúma výsledky prieskumu]
    F --> G[Vygenerované reporty na analýzu]
```

## 6. Frontend Podpisovací Wizard

### 6.1 Architektúra Wizarda
Podpisovací wizard je React-based aplikácia, ktorá beží na Android tabletoch používaných kuriérmi počas doručovania.

**Kľúčové Komponenty:**
- **SigningProcess:** Hlavné podpisovacie rozhranie
- **FormTypes:** Dynamické generovanie formulárov na základe schém zákazníkov
- **AppState:** Globálna správa stavu pomocou Immutable.js
- **Operations:** Spracovanie súborov a manipulácia PDF

### 6.2 Tok Podpisovacieho Procesu
1. **Načítanie Dokumentu:** Wizard načíta nepodpísaný balík dokumentov
2. **Zobrazenie Formulára:** Vykreslené sú formuláre špecifické pre zákazníka
3. **Zbieranie Dát:** Zachytené sú informácie o zákazníkovi
4. **Podpisovanie Dokumentu:** PDF dokumenty sú digitálne podpísané
5. **Vytvorenie Balíka:** Podpísané dokumenty sú zabalené s metadátami
6. **Nahratie:** Dokončený balík je nahraný na server

### 6.3 Podporované Operácie
- Vyplňovanie PDF formulárov s dátami zákazníka
- Aplikácia digitálneho podpisu
- Funkcionalita náhľadu dokumentu
- Podpora podpisovania viacerých dokumentov
- Offline schopnosť so synchronizáciou pri online pripojení

## 7. Integračné Body

### 7.1 Integrácia AWS Služieb
- **S3 Storage:** Ukladanie súborov dokumentov s verziovaním
- **SES Email:** Automatizované notifikácie a reporty
- **CloudWatch:** Monitoring a logovanie (cez Sentry)

### 7.2 Systémy Kuriérskych Spoločností
- **Telekom:** Vlastná schéma a integrácia notifikácií
- **Orange:** Špecializované formáty dokumentov a workflow
- **O2:** Špecifické validačné pravidlá a procesy
- **Union:** Vlastné reportovanie a export dát
- **Packeta/InTime:** Integrácie doručovacích spoločností

### 7.3 Externé API
- **Validácia Certifikátov:** Správa digitálnych certifikátov
- **Notifikačné Služby:** SMS a email doručovanie
- **Tracking Systémy:** Integrácia sledovania balíkov

## 8. Bezpečnosť a Autentifikácia

### 8.1 Mechanizmy Autentifikácie
- **Session-based:** Autentifikácia web dashboardu
- **Token-based:** Autentifikácia mobilných aplikácií
- **Basic Auth:** API prístup pre externé systémy
- **Fail2ban:** Ochrana proti brute force útokom

### 8.2 Bezpečnostné Opatrenia
- **HTTPS:** Všetka komunikácia šifrovaná
- **Správa Certifikátov:** Digitálne podpisovacie certifikáty
- **Kontrola Prístupu:** Oprávnenia založené na rolách
- **Audit Logging:** Kompletné sledovanie akcií
- **Šifrovanie Dát:** Ochrana citlivých dát

### 8.3 Compliance Funkcie
- **Audit Trail:** Kompletná história všetkých akcií
- **Retencia Dát:** Konfigurovateľné retenčné politiky
- **Export Kontroly:** Bezpečná funkcionalita exportu dát
- **Ochrana Súkromia:** GDPR compliance funkcie

## 9. Systémová Administrácia

### 9.1 Správa Konfigurácie
- **Systém Nastavení:** Dynamická konfigurácia cez web rozhranie
- **Správa Schém:** Schémy dokumentov špecifické pre zákazníka
- **Pravidlá Notifikácií:** Konfigurovateľné prahy upozornení
- **Retenčné Politiky:** Konfigurácia automatizovaného čistenia

### 9.2 Monitoring a Údržba
- **Health Checks:** Monitoring stavu systému
- **Metriky Výkonu:** Sledovanie času odozvy a priepustnosti
- **Sledovanie Chýb:** Sentry integrácia pre monitoring chýb
- **Zálohovacie Systémy:** Automatizované procedúry zálohovania dát

### 9.3 Cron Joby a Automatizácia
- **Čistenie Dokumentov:** Automatizované odstránenie starých dokumentov
- **Spracovanie Notifikácií:** Plánované doručovanie notifikácií
- **Generovanie Reportov:** Automatizované vytváranie reportov
- **Správa Archívu:** Procesy archivácie dokumentov

## 10. Technické Detaily Implementácie

### 10.1 Databázová Schéma
- **PostgreSQL:** Primárna databáza s podporou JSON polí
- **Migrácie:** Django migračný systém pre zmeny schémy
- **Indexovanie:** Optimalizované indexy pre výkon
- **Obmedzenia:** Vynucovanie integrity dát

### 10.2 Architektúra Úložiska Súborov
- **S3 Buckety:** Organizované podľa zákazníka a typu dokumentu
- **Verziovanie:** Správa verzií dokumentov
- **Šifrovanie:** Server-side šifrovanie pre citlivé dáta
- **Kontrola Prístupu:** IAM-based správa prístupu

### 10.3 Optimalizácia Výkonu
- **Caching:** Redis/Memcached pre session a dátové cache
- **Optimalizácia Databázy:** Optimalizácia dotazov a connection pooling
- **CDN Integrácia:** Optimalizácia doručovania statických assetov
- **Load Balancing:** Podpora multi-instance deploymentu

## Záver

Axepto predstavuje komplexné riešenie pre digitálne podpisovanie dokumentov v kuriérskom priemysle. Systém kombinuje moderné technológie s robustnými obchodnými procesmi na poskytnutie spoľahlivej, škálovateľnej a bezpečnej platformy pre správu dokumentov a ich podpisovania.

Kľúčové výhody systému zahŕňajú:
- **Efektívnosť:** Automatizácia procesov a zníženie manuálnej práce
- **Spoľahlivosť:** Robustné error handling a audit trail
- **Škálovateľnosť:** Multi-tenant architektúra podporujúca viacero zákazníkov
- **Bezpečnosť:** Komplexné bezpečnostné opatrenia a compliance
- **Flexibilita:** Konfigurovateľné workflow a integračné možnosti

Systém je navrhnutý tak, aby podporoval rastúce potreby kuriérskych spoločností a poskytoval základ pre budúce rozšírenia a vylepšenia.
