# Axepto - Funkč<PERSON>if<PERSON> (Slovenčina)

## 1. Prehľad Aplikácie

### 1.1 Účel a Hlavné Funkcie
Axepto je komplexný systém na správu podpisovania dokumentov a doručovania pre kuriérske spoločnosti. Aplikácia umožňuje digitálne podpisovanie dokumentov počas doručovania balíkov a poskytuje kompletný workflow od nahratia dokumentu až po získanie podpísaného dokumentu.

**Kľúčové Funkcie:**
- Digitálne podpisovanie dokumentov cez tablet rozhranie
- Podpora viacerých kuriérskych spoločností (multi-tenant)
- Správa workflow dokumentov
- Sledovanie v reálnom čase a notifikácie
- Audit logging a compliance
- Systém prieskumov a spätnej väzby
- Správa karantény pre problematické dokumenty

### 1.2 Technologický Stack
- **Backend:** Django 3.2.8 (Python web framework)
- **Databáza:** PostgreSQL s podporou JSON polí
- **Úložisko:** AWS S3 pre ukladanie dokumentov
- **Frontend:** React-based podpisovací wizard pre tablety
- **Web Rozhranie:** Django templates s Bootstrap
- **Autentifikácia:** Django autentifikácia s fail2ban ochranou
- **Email:** AWS SES pre notifikácie
- **Monitoring:** Sentry pre sledovanie chýb
- **Deployment:** Docker s uWSGI a Nginx

### 1.3 Prehľad Architektúry
Systém využíva viacvrstvovú architektúru:
- **REST API Vrstva:** Spracováva integráciu mobilných aplikácií a operátorských systémov
- **Web Rozhranie Vrstva:** Poskytuje dashboard pre operátorov, manažérov a klientov
- **Business Logic Vrstva:** Spravuje workflow dokumentov a obchodné pravidlá
- **Dátová Vrstva:** PostgreSQL databáza s audit loggingom
- **Storage Vrstva:** AWS S3 pre súbory dokumentov
- **Integračná Vrstva:** Externé systémy kuriérskych spoločností

## 2. Dátové Modely a Entity

### 2.1 Základné Modely

#### Document (Dokument)
Centrálna entita reprezentujúca balík/dokument v systéme.
- **Polia:** id, client_id, timestamp, rest (JSON metadata), tracking (JSON), parent (self-reference), signed_locally, signed_remotely, author, customer, version, zip_size, deleted, hard_delete
- **Stavy:** unsigned (nepodpísaný) → signed/error (podpísaný/chyba)
- **Vzťahy:** Parent-child pre nepodpísané/podpísané verzie, patrí k Customer, priradený k Courier

#### Customer (Zákazník)
Reprezentuje klientov kuriérskych spoločností (Telekom, Orange, O2, Union, atď.)
- **Polia:** id (primárny kľúč), name, shipper_id, limity pre odstránenie rôznych typov dokumentov, has_password_protected_packages, active
- **Účel:** Multi-tenant podpora pre rôzne kuriérske spoločnosti

#### Courier (Kuriér)
Reprezentuje doručovateľov, ktorí podpisujú dokumenty
- **Polia:** id, user (OneToOne s Django User), documents (ManyToMany), active, center, deleted
- **Funkcionalita:** Priradenie dokumentov, správa driver listu, priradenie k centru

#### History (História)
Sleduje všetky dôležité akcie v systéme
- **Akcie:** ASSIGN, DELETE, DOWNLOAD, LOGIN_REST, SIGNED_REMOTELY, ACKNOWLEDGED
- **Polia:** author, user, document, created_at, action, auto (boolean pre automatické akcie)

#### Survey (Prieskum)
Systém prieskumov spokojnosti zákazníkov
- **Polia:** subject, couriers, courier_centers, active, repeating, description, form_url, package
- **Účel:** Zbieranie spätnej väzby od zákazníkov po doručení

#### Quarantine (Karanténa)
Spracováva problematické dokumenty, ktoré nemožno normálne spracovať
- **Polia:** id, timestamp, zip_size, url_id, courier, customer, deleted
- **Účel:** Izolácia a správa dokumentov s chybami spracovania

### 2.2 Používateľské Roly a Oprávnenia

#### Courier (Kuriér)
- Prístup k driver listu (priradené dokumenty)
- Sťahovanie nepodpísaných dokumentov
- Nahrávanie podpísaných dokumentov
- Odosielanie odpovedí na prieskumy
- Potvrdenie prijatia dokumentu

#### Operator (Operátor)
- Nahrávanie nepodpísaných dokumentov
- Priradenie dokumentov kuriérom
- Zobrazenie zoznamov podpísaných/nepodpísaných dokumentov
- Správa životného cyklu dokumentov

#### Manager (Manažér)
- Plný prístup k systému
- Zobrazenie všetkých reportov a analytík
- Správa kuriérov a prieskumov
- Prístup ku karanténe a audit logom
- Konfigurácia systému

#### Client (Klient)
- Zobrazenie dokumentov a informácií o kuriéroch
- Prístup k reportom a súhrnom
- Správa účtov kuriérov

## 3. REST API Endpointy

### 3.1 Autentifikačné Endpointy
```
POST /login/ - Autentifikácia používateľa (Basic Auth alebo form-based)
POST /logout/ - Odhlásenie používateľa
GET /auth/ - Získanie session tokenu pre kuriérske aplikácie
```

### 3.2 Endpointy pre Správu Dokumentov
```
GET /active/ - Získanie priradených dokumentov kuriéra (driver list)
POST /active/add/ - Priradenie dokumentu kuriérovi skenovaním
GET /document/unsigned/{id}/ - Stiahnutie nepodpísaného dokumentu
POST /document/unsigned/ - Nahratie nepodpísaného dokumentu (operátor)
GET /document/signed/{id}/ - Stiahnutie podpísaného dokumentu
POST /document/signed/{id}/ - Nahratie podpísaného dokumentu (kuriér)
POST /document/unsigned/{id}/ack/ - Potvrdenie prijatia dokumentu
POST /document/mark-signed/ - Označenie dokumentu ako vzdialene podpísaného
```

### 3.3 Endpointy pre Synchronizáciu Dát
```
GET /document/signed/since/{timestamp}/ - Zoznam podpísaných dokumentov od časovej značky
GET /document/unsigned/since/{timestamp}/ - Zoznam nepodpísaných dokumentov od časovej značky
```

### 3.4 Priradenie a Správa
```
POST /assign/ - Priradenie dokumentov kuriérom (operátor)
POST /assign2/ - Hromadné priradenie endpoint
```

### 3.5 Logovanie a Prieskumy
```
POST /logs/ - Odoslanie kuriérskych logov
GET /logs/{id}/ - Získanie logov (manažér)
POST /survey/ - Odoslanie odpovedí na prieskum
```

### 3.6 Správa Používateľov
```
POST /register/ - Registrácia nového kuriéra
POST /changepassword/ - Zmena hesla kuriéra
```

## 4. Funkcionalita Web Dashboardu

### 4.1 Dashboard Zobrazenia
- **Hlavný Dashboard:** Zoznam dokumentov s filtrovaním, triedením a exportom
- **Info o Dokumente:** Detailné zobrazenie dokumentu s históriou a súbormi
- **Info o Kuriérovi:** Zoznam dokumentov špecifických pre kuriéra a štatistiky

### 4.2 Správcovské Rozhrania
- **Správa Kuriérov:** Pridanie, úprava, mazanie kuriérov; priradenie k centrám
- **Správa Prieskumov:** Vytvorenie, úprava prieskumov; zobrazenie odpovedí
- **Správa Karantény:** Zobrazenie a správa problematických dokumentov
- **Správa Nastavení:** Konfigurácia systému a parametrov

### 4.3 Reportovanie a Analytika
- **Súhrnné Reporty:** Štatistiky a metriky dokumentov
- **Sledovanie Histórie:** Kompletný audit trail všetkých akcií
- **Logs Dashboard:** Aktivita systému a kuriérov
- **Export Funkcionalita:** Excel export pre všetky hlavné dátové zobrazenia

## 5. Obchodné Procesy a Workflow

### 5.1 Workflow Podpisovania Dokumentov

```mermaid
flowchart TD
    A[Operátor nahráva nepodpísaný dokument] --> B[Dokument uložený v S3]
    B --> C[Dokument priradený kuriérovi]
    C --> D[Kuriér stiahne dokument cez mobilnú aplikáciu]
    D --> E[Zákazník podpíše na tablete pomocou wizarda]
    E --> F[Podpísaný dokument nahraný do systému]
    F --> G{Nahratie úspešné?}
    G -->|Áno| H[Dokument označený ako podpísaný]
    G -->|Nie| I[Dokument presunutý do karantény]
    H --> J[Operátor stiahne podpísaný dokument]
    I --> K[Manuálne preskúmanie a riešenie]
```

### 5.2 Proces Priradenia Kuriéra

```mermaid
flowchart TD
    A[Nepodpísaný dokument dostupný] --> B{Metóda priradenia}
    B -->|Manuálne| C[Operátor priradí konkrétnemu kuriérovi]
    B -->|Automaticky| D[Kuriér naskenuje QR/čiarový kód]
    C --> E[Dokument pridaný do driver listu kuriéra]
    D --> E
    E --> F[Vytvorený záznam histórie]
    F --> G[Kuriér upozornený na nové priradenie]
```

### 5.3 Spracovanie Chýb a Karanténa

```mermaid
flowchart TD
    A[Chyba spracovania dokumentu] --> B[Detaily chyby zalogované]
    B --> C[Dokument presunutý do karantény]
    C --> D[Notifikácia odoslaná manažérom]
    D --> E[Iniciované manuálne preskúmanie]
    E --> F{Riešenie možné?}
    F -->|Áno| G[Dokument vrátený do workflow]
    F -->|Nie| H[Dokument označený na zmazanie]
    G --> I[Obnovené normálne spracovanie]
    H --> J[Vyčistenie po retenčnej dobe]
```

### 5.4 Proces Správy Prieskumov

```mermaid
flowchart TD
    A[Manažér vytvorí prieskum] --> B[Prieskum priradený kuriérom/centrám]
    B --> C[Prieskum sa zobrazí v driver liste kuriéra]
    C --> D[Zákazník vyplní prieskum na tablete]
    D --> E[Odpoveď na prieskum zaznamenaná]
    E --> F[Manažér preskúma výsledky prieskumu]
    F --> G[Vygenerované reporty na analýzu]
```

## 6. Frontend Podpisovací Wizard

### 6.1 Architektúra Wizarda
Podpisovací wizard je React-based aplikácia, ktorá beží na Android tabletoch používaných kuriérmi počas doručovania.

**Kľúčové Komponenty:**
- **SigningProcess:** Hlavné podpisovacie rozhranie
- **FormTypes:** Dynamické generovanie formulárov na základe schém zákazníkov
- **AppState:** Globálna správa stavu pomocou Immutable.js
- **Operations:** Spracovanie súborov a manipulácia PDF

### 6.2 Tok Podpisovacieho Procesu
1. **Načítanie Dokumentu:** Wizard načíta nepodpísaný balík dokumentov
2. **Zobrazenie Formulára:** Vykreslené sú formuláre špecifické pre zákazníka
3. **Zbieranie Dát:** Zachytené sú informácie o zákazníkovi
4. **Podpisovanie Dokumentu:** PDF dokumenty sú digitálne podpísané
5. **Vytvorenie Balíka:** Podpísané dokumenty sú zabalené s metadátami
6. **Nahratie:** Dokončený balík je nahraný na server

### 6.3 Podporované Operácie
- Vyplňovanie PDF formulárov s dátami zákazníka
- Aplikácia digitálneho podpisu
- Funkcionalita náhľadu dokumentu
- Podpora podpisovania viacerých dokumentov
- Offline schopnosť so synchronizáciou pri online pripojení

## 7. Integračné Body

### 7.1 Integrácia AWS Služieb
- **S3 Storage:** Ukladanie súborov dokumentov s verziovaním
- **SES Email:** Automatizované notifikácie a reporty
- **CloudWatch:** Monitoring a logovanie (cez Sentry)

### 7.2 Systémy Kuriérskych Spoločností
- **Telekom:** Vlastná schéma a integrácia notifikácií
- **Orange:** Špecializované formáty dokumentov a workflow
- **O2:** Špecifické validačné pravidlá a procesy
- **Union:** Vlastné reportovanie a export dát
- **Packeta/InTime:** Integrácie doručovacích spoločností

### 7.3 Externé API
- **Validácia Certifikátov:** Správa digitálnych certifikátov
- **Notifikačné Služby:** SMS a email doručovanie
- **Tracking Systémy:** Integrácia sledovania balíkov

## 8. Bezpečnosť a Autentifikácia

### 8.1 Mechanizmy Autentifikácie
- **Session-based:** Autentifikácia web dashboardu
- **Token-based:** Autentifikácia mobilných aplikácií
- **Basic Auth:** API prístup pre externé systémy
- **Fail2ban:** Ochrana proti brute force útokom

### 8.2 Bezpečnostné Opatrenia
- **HTTPS:** Všetka komunikácia šifrovaná
- **Správa Certifikátov:** Digitálne podpisovacie certifikáty
- **Kontrola Prístupu:** Oprávnenia založené na rolách
- **Audit Logging:** Kompletné sledovanie akcií
- **Šifrovanie Dát:** Ochrana citlivých dát

### 8.3 Compliance Funkcie
- **Audit Trail:** Kompletná história všetkých akcií
- **Retencia Dát:** Konfigurovateľné retenčné politiky
- **Export Kontroly:** Bezpečná funkcionalita exportu dát
- **Ochrana Súkromia:** GDPR compliance funkcie

## 9. Systémová Administrácia

### 9.1 Správa Konfigurácie
- **Systém Nastavení:** Dynamická konfigurácia cez web rozhranie
- **Správa Schém:** Schémy dokumentov špecifické pre zákazníka
- **Pravidlá Notifikácií:** Konfigurovateľné prahy upozornení
- **Retenčné Politiky:** Konfigurácia automatizovaného čistenia

### 9.2 Monitoring a Údržba
- **Health Checks:** Monitoring stavu systému
- **Metriky Výkonu:** Sledovanie času odozvy a priepustnosti
- **Sledovanie Chýb:** Sentry integrácia pre monitoring chýb
- **Zálohovacie Systémy:** Automatizované procedúry zálohovania dát

### 9.3 Cron Joby a Automatizácia
- **Čistenie Dokumentov:** Automatizované odstránenie starých dokumentov
- **Spracovanie Notifikácií:** Plánované doručovanie notifikácií
- **Generovanie Reportov:** Automatizované vytváranie reportov
- **Správa Archívu:** Procesy archivácie dokumentov

## 10. Technické Detaily Implementácie

### 10.1 Databázová Schéma
- **PostgreSQL:** Primárna databáza s podporou JSON polí
- **Migrácie:** Django migračný systém pre zmeny schémy
- **Indexovanie:** Optimalizované indexy pre výkon
- **Obmedzenia:** Vynucovanie integrity dát

### 10.2 Architektúra Úložiska Súborov
- **S3 Buckety:** Organizované podľa zákazníka a typu dokumentu
- **Verziovanie:** Správa verzií dokumentov
- **Šifrovanie:** Server-side šifrovanie pre citlivé dáta
- **Kontrola Prístupu:** IAM-based správa prístupu

### 10.3 Optimalizácia Výkonu
- **Caching:** Redis/Memcached pre session a dátové cache
- **Optimalizácia Databázy:** Optimalizácia dotazov a connection pooling
- **CDN Integrácia:** Optimalizácia doručovania statických assetov
- **Load Balancing:** Podpora multi-instance deploymentu

## Záver

Axepto predstavuje komplexné riešenie pre digitálne podpisovanie dokumentov v kuriérskom priemysle. Systém kombinuje moderné technológie s robustnými obchodnými procesmi na poskytnutie spoľahlivej, škálovateľnej a bezpečnej platformy pre správu dokumentov a ich podpisovania.

Kľúčové výhody systému zahŕňajú:
- **Efektívnosť:** Automatizácia procesov a zníženie manuálnej práce
- **Spoľahlivosť:** Robustné error handling a audit trail
- **Škálovateľnosť:** Multi-tenant architektúra podporujúca viacero zákazníkov
- **Bezpečnosť:** Komplexné bezpečnostné opatrenia a compliance
- **Flexibilita:** Konfigurovateľné workflow a integračné možnosti

Systém je navrhnutý tak, aby podporoval rastúce potreby kuriérskych spoločností a poskytoval základ pre budúce rozšírenia a vylepšenia.
